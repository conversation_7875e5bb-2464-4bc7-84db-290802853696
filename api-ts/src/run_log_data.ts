import { ToS<PERSON>, ident, join, snippet, sql } from "@braintrust/btql/planner";
import {
  ASYNC_SCORING_CONTROL_FIELD,
  AUDIT_METADATA_FIELD,
  AUDIT_SOURCE_FIELD,
  IS_MERGE_FIELD,
  MERGE_PATHS_FIELD,
  OBJECT_DELETE_FIELD,
  PARENT_ID_FIELD,
  SKIP_ASYNC_SCORING_FIELD,
  TRANSACTION_ID_FIELD,
  deterministicReplacer,
  mapAt,
  mapSetDefault,
  mergeDictsWithPaths,
  mergeRowBatch,
  recordAt,
  recordFind,
  recordSetDefault,
} from "braintrust/util";
import {
  AsyncScoringControl,
  AsyncScoringState,
  AttachmentReference,
  OnlineScoreConfig,
  datetimeStringSchema,
} from "@braintrust/typespecs";
import { ASYNC_SCORING_STATE_FIELD, ROW_REF_FIELD } from "@braintrust/local";
import {
  AuditMerge,
  INTERNAL_OVERRIDE_TRANSACTION_ID_FIELD,
  OBJECT_TYPE_FIELD,
  ObjectIdsUnion,
  RowRef,
  commentEventSchema,
  insertControlFieldsSchema,
  logEventSchema,
  objectIdsUnionSchema,
} from "@braintrust/local/api-schema";
import { batch as makeBatch } from "@braintrust/local/query";
import { DatabaseError } from "pg";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { advisoryLockInfo, computeAdvisoryLockIds } from "./advisory_locks";
import {
  ASYNC_SCORING_CONFIG_CACHE,
  asyncScoringSkipMerge,
  getSpanName,
  hasMetricsEnd,
  invokeAsyncScoring,
  nextAsyncScoringState,
} from "./async_scoring";
import {
  auditLogObjectIdsSchema,
  computeAuditMergeData,
  makeAuditLogEntry,
  popAuditFields,
} from "./audit_log";
import {
  brainstoreRealtimeGetWalTokens,
  brainstoreRealtimeWalInsert,
  makePaginationKey,
  resolveRowRefs,
} from "./brainstore/wal";
import { getPG, LogPGConnection } from "./db/pg";
import {
  DISABLE_ATTACHMENT_OPTIMIZATION,
  PG_COMMENTS_TABLE,
  PG_LOGS2_TABLE,
  PG_LOGS_TABLE,
} from "./env";
import { LogToPublisherFn } from "./event_publisher";
import { OBJECT_CACHE, ObjectCacheEntry } from "./object_cache";
import {
  PromptCacheInvalidation,
  executePromptCacheInvalidations,
} from "./prompt_cache_invalidation";
import { makeObjectIdExprFromTable } from "./query_util";
import { RequestContext } from "./request_context";
import { ResourceCheckInput, resourceCheck } from "./resource_check";
import {
  LEAF_OBJECT_ID_FIELDS,
  ObjectType,
  convertPathStorageLogical,
  getLeafObjectId,
  getLogicalToStorageMap,
  getStorageToLogicalMap,
  removeControlFields,
} from "./schema";
import {
  IdToSpanParentMap,
  addResolvedRow,
  makeIdToSpanParentKey,
  makeObjectIdKey,
  resolveSpanParents,
  resolvedRowSchema,
  spanParentResolveRow,
} from "./span_parent";
import { LogInsertedEvent } from "./telemetry/events";
import { idempotency } from "./telemetry/idempotency";
import { withTelemetry } from "./telemetry/withTelemetry";
import {
  AccessDeniedError,
  BadRequestError,
  HTTPError,
  InternalServerError,
  aclObjectTypeToColName,
  extractErrorText,
  fullAclObjectTypeSchema,
  getAclObjectId,
  objectTypeToAclObjectType,
  wrapZodError,
} from "./util";
import { nextXactId } from "./xact_id";
import { logger } from "./telemetry/logger";
import { STANDARD_METRICS } from "./telemetry/constants";
import { ERROR_CONTEXT_CACHE } from "./error_context_cache";
import {
  canContainRowRefs,
  canPgInsertLogs2 as canPgInsertLogs2Base,
  shouldUseBrainstore,
} from "./brainstore/brainstore";
import { getLogger } from "./instrumentation/logger";
import { logCounter, otelTraced } from "./instrumentation/api";
import { AUTOMATION_CACHE, executeLogAutomations } from "./automations";
import { Attributes, Span } from "@opentelemetry/api";
import {
  checkAttachmentsEnvironment,
  defaultAttachmentStatus,
  doneAttachmentStatus,
  makeAttachmentKey,
  makeAttachmentStatusKey,
} from "./attachment";
import { makeObjectStore, ObjectStore } from "./object-storage/object-storage";
import { replacePayloadWithAttachmentsCallback } from "./replace-attachment";
import { newId } from "braintrust";
import { checkLogsOrgRateLimit } from "./rate-limit";

const toWellFormed: (
  str: string,
) => string = require("string.prototype.towellformed");

type InputRowMetrics = {
  byteSize: number;
  scoreCount: number;
  metricCount: number;
};

export type RunLogDataInput = {
  rows: unknown;
  appOrigin: string;
  token: string | undefined;
  logToPublisher: LogToPublisherFn;
  resourceCheckWasCachedToken?: string | undefined;
  asyncScoringConfigWasCachedToken?: string | undefined;
  automationWasCachedToken?: string | undefined;
  forceDisableLogs2?: boolean | null;
};

export type RunLogDataOutput = {
  // These ids line up 1-1 with the input rows.
  ids: string[];
  // null iff ids.length === 0.
  xactId: string | null;
  // Does not necessarily line up with the input rows.
  insertedRows: {
    fullRowData: Record<string, unknown>;
    objectIds: ObjectIdsUnion;
    fullRowDataByteSize: number;
    inputRow: InputRowMetrics;
  }[];
  // A map of stringified FullAclObjectType to object mapping AclObjectType to ObjectCacheEntry.
  objectsByType: Map<string, Record<string, ObjectCacheEntry>>;
};

async function runLogDataImplTraced(
  span: Span,
  {
    rows: rowsU,
    appOrigin,
    token,
    resourceCheckWasCachedToken,
    asyncScoringConfigWasCachedToken,
    automationWasCachedToken,
    logToPublisher,
    forceDisableLogs2,
  }: RunLogDataInput,
): Promise<RunLogDataOutput> {
  const { origRowIdKeys, mergedRows, projectIdToName, objectsByType } =
    await sanitizeRows({
      rows: rowsU,
      appOrigin,
      token,
    });

  // Enforce org-level log rate limiting
  const countByOrgId: Record<string, number> = {};
  for (const row of mergedRows) {
    const orgId = row.controlFields.org_id;
    if (orgId) {
      countByOrgId[orgId] = (countByOrgId[orgId] || 0) + 1;
    }
  }
  for (const [orgId, count] of Object.entries(countByOrgId)) {
    // It's an unlikely scenario that a user is sending data to multiple orgs
    // and getting rate limited in one request, so we reject the whole payload
    // if any org is rate limited.
    await checkLogsOrgRateLimit({
      appOrigin,
      authToken: token,
      orgId,
      numLogs: count,
    });
  }

  await runResourceCheck({
    rows: mergedRows.map((x) => ({
      ...x.objectIds,
      org_id: x.controlFields.org_id,
      hasComment: "comment" in x.opaqueData,
      inputRow: x.inputRow,
    })),
    token,
    wasCachedToken: resourceCheckWasCachedToken,
  });

  const asyncScoringConfigs =
    await ASYNC_SCORING_CONFIG_CACHE.getAsyncScoringConfigMulti({
      appOrigin,
      authToken: token,
      allObjectIds: mergedRows.map((x) => x.objectIds),
      wasCachedToken: asyncScoringConfigWasCachedToken,
    });

  const { insertedRowData, promptCacheInvalidations } = await logPG({
    mergedRows,
    asyncScoringConfigs,
    logToPublisher,
    forceDisableLogs2,
  });

  const insertedRows = insertedRowData.map(
    ({ fullRowData, objectIds, fullRowDataByteSize, inputRow }) => ({
      fullRowData,
      objectIds,
      fullRowDataByteSize,
      inputRow,
    }),
  );
  recordLogMetrics(span, insertedRows);

  const rows = insertedRows.map(({ fullRowData }) => fullRowData);

  broadcastOrgProjectMetadata({
    rows,
    logToPublisher,
  });
  await executePromptCacheInvalidations({
    promptCacheInvalidations,
    projectIdToName,
  });
  await invokeAsyncScoring({
    allRows: insertedRowData,
    appOrigin,
    token,
  });

  const uniqueXactIds: string[] = [
    ...new Set<string>(
      rows.map((r) => z.string().parse(r[TRANSACTION_ID_FIELD])),
    ).keys(),
  ];
  if (uniqueXactIds.length > 1) {
    throw new Error(
      `Multiple transaction IDs in one batch: ${JSON.stringify(uniqueXactIds)}`,
    );
  }

  // Sanitize the returned inserted rows.
  rows.forEach((row, idx) => {
    rows[idx] = z
      .object({
        created: datetimeStringSchema.nullish(),
      })
      .passthrough()
      .parse(row);
  });

  const projectIdToAutomations = await AUTOMATION_CACHE.getAutomationMulti({
    appOrigin,
    authToken: token,
    projectIds: mergedRows.flatMap((x) => {
      // Filter down to project_logs to run log automations
      if (x.objectIds[OBJECT_TYPE_FIELD] === "project_logs") {
        return [x.objectIds.project_id];
      }
      return [];
    }),
    wasCachedToken: automationWasCachedToken,
  });
  executeLogAutomations({
    appOrigin,
    authToken: token,
    projectIdToAutomations,
    insertedRows,
  });

  const ids = origRowIdKeys.map(
    (k) => rowIdKeySchema.parse(JSON.parse(k)).row_id,
  );

  return {
    ids,
    xactId: uniqueXactIds.length ? uniqueXactIds[0] : null,
    insertedRows,
    objectsByType,
  };
}

const runLogDataImpl = async (args: RunLogDataInput) =>
  otelTraced("runLogData", async (span) => runLogDataImplTraced(span, args));

const sanitizedControlFieldsSchema = insertControlFieldsSchema
  .omit({ id: true })
  .extend({
    id: z.string(),
  });

type SanitizedControlFields = z.infer<typeof sanitizedControlFieldsSchema>;

type SanitizeRowsOutput = {
  // Used for constructing RunLogDataOutput. The keys are constructed with
  // makeRowIdKey.
  origRowIdKeys: string[];
  // The rows we actually want to insert into the DB. There should be at most
  // one item per distinct (object id, row id).
  //
  // Note: the `opaqueData` and `controlFields` schemas are independent.
  // Generally it should be possible to work on the controlFields independently
  // of the opaqueData, and then merge them together at the very end.
  mergedRows: {
    opaqueData: Record<string, unknown>;
    objectIds: ObjectIdsUnion;
    controlFields: SanitizedControlFields;
    inputRow: InputRowMetrics;
  }[];
  // Used for future cache invalidation.
  projectIdToName: Map<string, string>;
  // A map of stringified FullAclObjectType to object mapping AclObjectType to ObjectCacheEntry.
  objectsByType: Map<string, Record<string, ObjectCacheEntry>>;
};

function preparePlaygroundLogsRowForWrite<T extends Record<string, unknown>>(
  row: T,
): T {
  if ("prompt_session_id" in row && "log_id" in row) {
    const ret: Record<string, unknown> = { ...row };
    ret.prompt_session_id = `${row.prompt_session_id}:${row.log_id}`;
    delete ret.log_id;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return ret as T;
  } else {
    return row;
  }
}

// Given the original row data, validates and preprocesses it. Returns the set
// of rows we ultimately want to insert. The returned rows are guaranteed to
// have their 'id' field populated.
async function sanitizeRows({
  rows: rowsU,
  appOrigin,
  token,
}: Pick<RunLogDataInput, "rows"> &
  Pick<RequestContext, "appOrigin" | "token">): Promise<SanitizeRowsOutput> {
  // Parse the rows into a basic, but fully-permissive form.
  const rowsRaw = wrapZodError(() =>
    z.record(z.unknown()).array().parse(rowsU),
  );

  const fullAclObjectTypeToIds = new Map<string, Set<string>>();

  // Do some basic validity checks and collect the object ids we are writing to
  // so that we can do auth checks and fill in parent ids.
  const splitRows = rowsRaw.map(
    (
      row,
    ): {
      opaqueData: Record<string, unknown>;
      objectIds: ObjectIdsUnion;
      controlFields: SanitizedControlFields;
    } => {
      // Validate the rows.
      const [objectIds, controlFieldsRaw] = wrapZodError(() => {
        if ("comment" in row) {
          commentEventSchema.parse(row);
        } else {
          logEventSchema.parse(row);
        }
        const objectIds = objectIdsUnionSchema.parse(row);
        const controlFieldsRaw = insertControlFieldsSchema.parse(row);
        return [objectIds, controlFieldsRaw];
      });

      const controlFields = {
        ...controlFieldsRaw,
        id: controlFieldsRaw.id ?? uuidv4(),
      };
      if (!!controlFields.span_id !== !!controlFields.root_span_id) {
        throw new BadRequestError(
          "Must include both 'span_id' and 'root_span_id' or neither",
        );
      }
      const backfillNonMergeFields = controlFields[IS_MERGE_FIELD]
        ? undefined
        : backfillNonMergeRow({
            ...controlFields,
            [IS_MERGE_FIELD]: controlFields[IS_MERGE_FIELD],
          });

      const fullAclObjectType = objectTypeToAclObjectType(
        objectIds[OBJECT_TYPE_FIELD],
      );
      mapSetDefault(
        fullAclObjectTypeToIds,
        JSON.stringify(fullAclObjectType, deterministicReplacer),
        new Set<string>(),
      ).add(getAclObjectId(objectIds));

      // Normalize data (e.g. if the user specifies "input" rename it to "inputs"
      // in the data)
      const columnLogicalToStorage = getLogicalToStorageMap(
        objectIds[OBJECT_TYPE_FIELD],
      );
      Object.entries(columnLogicalToStorage).forEach(([k, v]) => {
        if (k in row) {
          row[v] = row[k];
          delete row[k];
        }
      });

      // "Shift" the logged rows from a playground into the prompt session but with a `:log:` suffix.
      row = preparePlaygroundLogsRowForWrite(row);

      return {
        opaqueData: row,
        objectIds,
        controlFields: { ...controlFields, ...backfillNonMergeFields },
      };
    },
  );

  // Collect info for all objects being written to.
  const projectIdToName = new Map<string, string>();
  const fullAclObjectTypeToIdToCacheEntry = new Map<
    string,
    Record<string, ObjectCacheEntry>
  >();
  for (const [k, idsSet] of fullAclObjectTypeToIds.entries()) {
    const fullAclObjectType = fullAclObjectTypeSchema.parse(JSON.parse(k));
    const { aclObjectType, overrideRestrictObjectType } = fullAclObjectType;
    const objectIds = [...idsSet.keys()];
    const idToEntry = await OBJECT_CACHE.checkAndGetMulti({
      appOrigin,
      authToken: token,
      aclObjectType,
      overrideRestrictObjectType,
      objectIds,
    });
    fullAclObjectTypeToIdToCacheEntry.set(
      JSON.stringify(fullAclObjectType, deterministicReplacer),
      idToEntry,
    );
    // Collect all project names for prompt stuff.
    if (aclObjectType === "project") {
      Object.entries(idToEntry).forEach(([id, entry]) => {
        projectIdToName.set(id, entry.object_name);
      });
    }
    Object.entries(idToEntry).forEach(([_id, entry]) => {
      const projectCol = entry.parent_cols.get("project");
      if (projectCol) {
        projectIdToName.set(projectCol.id, projectCol.name);
      }
    });
  }

  // Collect the parent ids of each row. Also do ACL checks.
  const rowParentIds = splitRows.map(({ objectIds, controlFields }) => {
    const fullAclObjectType = objectTypeToAclObjectType(
      objectIds[OBJECT_TYPE_FIELD],
    );
    const aclObjectId = getAclObjectId(objectIds);
    const cacheEntry = recordAt(
      mapAt(
        fullAclObjectTypeToIdToCacheEntry,
        JSON.stringify(fullAclObjectType, deterministicReplacer),
      ),
      aclObjectId,
    );
    const parentIds: Record<string, unknown> = {};
    Array.from(cacheEntry.parent_cols.entries()).forEach(
      ([parentObjectType, colInfo]) => {
        const parentColName = recordFind(
          aclObjectTypeToColName,
          parentObjectType,
        );
        if (parentColName) {
          parentIds[parentColName] = colInfo.id;
        }
      },
    );

    // For a row deletion, we require the 'delete' permission. Otherwise we must
    // have the 'update' permission.
    //
    // Ideally, we would further distinguish between a pure addition of data
    // (insert) vs. a merge/replacement (update), but making this distinction
    // would require extra query effort on the backend to identify which
    // non-merge row IDs already exist in the object.
    const requiredPermission = controlFields[OBJECT_DELETE_FIELD]
      ? "delete"
      : "update";
    if (!cacheEntry.permissions.includes(requiredPermission)) {
      throw new AccessDeniedError({
        permission: requiredPermission,
        ...fullAclObjectType,
        objectId: aclObjectId,
      });
    }

    return parentIds;
  });

  const objectStore = await getAttachmentsObjectStore();
  const uploadPromises: Promise<void>[] = [];
  const replacedAttachmentBytes: number[] = Array(splitRows.length).fill(0);

  if (objectStore) {
    for (const [idx, splitRow] of splitRows.entries()) {
      const orgId = z
        .string()
        .safeParse(splitRow.controlFields.org_id ?? rowParentIds[idx]?.org_id);
      if (!orgId.success) {
        getLogger().warn(
          { splitRow, rowParentIds },
          "Skipping attachment upload for row without org_id",
        );
        continue;
      }

      splitRow.opaqueData = replaceOpaqueDataWithAttachments({
        opaqueData: splitRow.opaqueData,
        objectStore,
        orgId: orgId.data,
        uploadPromises,
        incrementBytes: (bytes) => {
          replacedAttachmentBytes[idx] += bytes;
        },
      });
    }
  }

  try {
    await Promise.all(uploadPromises);
  } catch (e) {
    getLogger().error(
      { error: extractErrorText(e) },
      "Failed to upload attachments during log data insertion: " +
        extractErrorText(e),
    );
    throw new InternalServerError(
      "Failed to upload attachments during log data insertion: " +
        extractErrorText(e),
    );
  }

  // Merge the batch of rows into a set we can insert into the data warehouse.
  // We don't currently do any parallel processing, so we can just flatten the
  // list of rows to merge.
  const rowsToMerge = splitRows.map(({ opaqueData, controlFields }, idx) => ({
    ...opaqueData,
    ...controlFields,
    ...rowParentIds[idx],
  }));
  const mergedRows = mergeRowBatch(rowsToMerge).flat();

  // Finally, split out our sanitized control fields from the opaque data for
  // the remainder of the insert function. Since the object id information is
  // immutable, we can parse that out but not remove it from the opaque data.
  const mergedRowsSplit = mergedRows.map((mergedRow, idx) => {
    const byteSize =
      JSON.stringify(mergedRow).length + replacedAttachmentBytes[idx];
    const objectIds = objectIdsUnionSchema.parse(mergedRow);
    const controlFields = sanitizedControlFieldsSchema.parse(mergedRow);
    const opaqueData = removeControlFields(mergedRow);

    const scoresAndMetrics =
      z
        .object({
          scores: z.record(z.unknown()).nullish(),
          metrics: z.record(z.unknown()).nullish(),
        })
        .safeParse(mergedRow).data || {};

    return {
      opaqueData,
      objectIds,
      controlFields,
      inputRow: {
        byteSize,
        scoreCount: scoresAndMetrics.scores
          ? Object.keys(scoresAndMetrics.scores).length
          : 0,
        metricCount: scoresAndMetrics.metrics
          ? Object.entries(scoresAndMetrics.metrics).filter(
              ([metric, value]) =>
                !STANDARD_METRICS.has(metric) &&
                value !== null &&
                value !== undefined,
            ).length
          : 0,
      },
    };
  });

  const origRowIdKeys = splitRows.map((splitRow) =>
    makeRowIdKey(splitRow.objectIds, splitRow.controlFields.id),
  );
  if (origRowIdKeys.length !== rowsRaw.length) {
    throw new Error("Impossible");
  }

  // Log a warning if the user is trying to insert rows with the _parent_id
  // control field. This field is now deprecated and will be removed in a future
  // version of the API.
  if (mergedRowsSplit.some((r) => r.controlFields[PARENT_ID_FIELD])) {
    try {
      const errorContext = await ERROR_CONTEXT_CACHE.getErrorContext({
        appOrigin,
        authToken: token,
      });
      getLogger().warn(
        { errorContext },
        "Found log with _parent_id field. The _parent_id control field is deprecated and will be removed in a future version of the API.",
      );
    } catch (e) {
      getLogger().error({ error: e }, "Failed to log _parent_id warning");
    }
  }

  return {
    origRowIdKeys,
    mergedRows: mergedRowsSplit,
    projectIdToName,
    objectsByType: fullAclObjectTypeToIdToCacheEntry,
  };
}

const orgIdProjectIdSchema = z.object({
  org_id: z.string().nullish(),
  project_id: z.string().nullish(),
  id: z.string().nullish(),
  span_id: z.string().nullish(),
  root_span_id: z.string().nullish(),
});

function recordLogMetrics(
  span: Span,
  insertedRows: RunLogDataOutput["insertedRows"],
) {
  let totalFullRowBytes = 0;
  let totalInputRowBytes = 0;
  let totalInputRowScoreCounts = 0;
  let totalInputRowMetricCounts = 0;
  let totalRows = 0;
  const projectIds = new Set<string>();
  const orgIds = new Set<string>();
  const objectTypes = new Set<string>();
  let firstRowId = undefined;
  let firstSpanId = undefined;
  let firstRootSpanId = undefined;

  insertedRows.forEach(
    ({ fullRowData, objectIds, fullRowDataByteSize, inputRow }) => {
      const parsedRow = orgIdProjectIdSchema.safeParse(fullRowData);
      const attributes: Attributes = {};
      if (parsedRow.success) {
        if (parsedRow.data.org_id) {
          orgIds.add(parsedRow.data.org_id);
          attributes.org_id = parsedRow.data.org_id;
        }
        if (parsedRow.data.project_id) {
          projectIds.add(parsedRow.data.project_id);
          attributes.project_id = parsedRow.data.project_id;
        }

        if (parsedRow.data.id) {
          firstRowId = parsedRow.data.id;
        }
        if (parsedRow.data.span_id) {
          firstSpanId = parsedRow.data.span_id;
        }
        if (parsedRow.data.root_span_id) {
          firstRootSpanId = parsedRow.data.root_span_id;
        }
      }
      attributes.object_type = objectIds[OBJECT_TYPE_FIELD];
      objectTypes.add(attributes.object_type);

      logCounter({
        name: "api.log_data.num_rows",
        value: 1,
        attributes,
      });
      totalRows += 1;

      // full row includes merged/resolved row's data
      logCounter({
        name: "api.log_data.num_bytes",
        value: fullRowDataByteSize,
        attributes,
      });
      totalFullRowBytes += fullRowDataByteSize;

      // input row includes only what the customer provided on insertion
      logCounter({
        name: "api.log_data.input_row_num_bytes",
        value: inputRow.byteSize,
        attributes,
      });
      totalInputRowBytes += inputRow.byteSize;

      logCounter({
        name: "api.log_data.input_row_score_count",
        value: inputRow.scoreCount,
        attributes,
      });
      totalInputRowScoreCounts += inputRow.scoreCount;

      logCounter({
        name: "api.log_data.input_row_metric_count",
        value: inputRow.metricCount,
        attributes,
      });
      totalInputRowMetricCounts += inputRow.metricCount;
    },
  );

  const projectIdArr = Array.from(projectIds);
  const orgIdArr = Array.from(orgIds);
  const objectTypeArr = Array.from(objectTypes);
  span.setAttributes({
    "api.log_data.num_rows": totalRows,
    "api.log_data.num_bytes": totalFullRowBytes,
    "api.log_data.total_input_row_num_bytes": totalInputRowBytes,
    "api.log_data.total_input_row_score_counts": totalInputRowScoreCounts,
    "api.log_data.total_input_row_metric_counts": totalInputRowMetricCounts,
    "api.log_data.project_id": projectIdArr[0],
    "api.log_data.org_id": orgIdArr[0],
    "api.log_data.object_type": objectTypeArr[0],
    "api.log_data.project_ids": JSON.stringify(projectIdArr),
    "api.log_data.org_ids": JSON.stringify(orgIdArr),
    "api.log_data.object_types": JSON.stringify(objectTypeArr),
    "api.log_data.row.id": firstRowId,
    "api.log_data.row.span_id": firstSpanId,
    "api.log_data.row.root_span_id": firstRootSpanId,
  });
}

async function runResourceCheck({
  rows,
  token,
  wasCachedToken,
}: {
  rows: (ObjectIdsUnion & {
    org_id: string | null;
    hasComment: boolean;
    inputRow: InputRowMetrics;
  })[];
  token: string | undefined;
  wasCachedToken: string | undefined;
}): Promise<void> {
  const resourceCheckOrgIds = new Set<string>();
  const resourceCheckProjectIds = new Set<string>();
  rows.forEach((row) => {
    if (row.org_id) {
      resourceCheckOrgIds.add(row.org_id);
    }
    if ("project_id" in row && row.project_id) {
      resourceCheckProjectIds.add(row.project_id);
    }
  });

  await resourceCheck({
    token,
    orgIds: [...resourceCheckOrgIds.keys()],
    projectIds: [...resourceCheckProjectIds.keys()],
    getResourceCheckInput() {
      const experiments: ResourceCheckInput["experiments"] & {} = {};
      const logs: ResourceCheckInput["logs"] & {} = {};
      const datasets: ResourceCheckInput["datasets"] & {} = {};
      rows.forEach((row) => {
        if (row.org_id) {
          // Regardless of the object, we need to account all of the opaqueData.
          // Ideally, we would have a separate global logs/num_row_bytes, but we're reusing this to avoid complexity in the control plane
          // for data planes that are out of date.
          recordSetDefault(logs, row.org_id, {
            num_row_actions: 0,
            num_row_bytes: 0,
          }).num_row_bytes += row.inputRow.byteSize;
        }

        if (row.hasComment) {
          // Comments are not counted towards the num_row_action-based resource limits
          return;
        }

        if (row.experiment_id) {
          recordSetDefault(experiments, row.experiment_id, {
            num_row_actions: 0,
          }).num_row_actions++;
        }

        if (row.log_id) {
          if (!row.org_id) {
            if ("prompt_session_id" in row) {
              throw new InternalServerError(
                `Log entry for prompt session ${row.prompt_session_id} does not belong to an org`,
              );
            } else {
              throw new InternalServerError(
                `Log entry for project ${row.project_id} does not belong to an org`,
              );
            }
          }

          recordSetDefault(logs, row.org_id, {
            num_row_actions: 0,
            num_row_bytes: 0,
          }).num_row_actions++;
        }

        if (row.dataset_id) {
          recordSetDefault(datasets, row.dataset_id, {
            num_row_actions: 0,
          }).num_row_actions++;
        }

        // TODO: limit scoreCount & metricsCount
      });

      return {
        experiments,
        logs,
        datasets,
      };
    },
    wasCachedToken,
  });
}

type FetchOldRowsRow = Readonly<
  ObjectIdsUnion & { id: string; shouldFetch: boolean }
>;

function makeFetchOldRowObjectIdsCte(rows: FetchOldRowsRow[]): ToSQL {
  rows = rows.map(preparePlaygroundLogsRowForWrite);

  const objectIdFragments = LEAF_OBJECT_ID_FIELDS.map((field) => {
    const rowIds = rows.map((r) => getLeafObjectId(r, field));
    return sql`${rowIds}::text[]`;
  });

  return sql`
  to_fetch as (
      select
          ${makeObjectIdExprFromTable("to_fetch_r")} object_id,
          id,
          (row_num - 1) row_index
      from
          unnest(
              ${join(objectIdFragments, ",")},
              ${rows.map((r) => r.id)}::text[],
              ${rows.map((r) => r.shouldFetch)}::boolean[])
          with ordinality
          as to_fetch_r(${join(
            [...LEAF_OBJECT_ID_FIELDS, "id", "should_fetch", "row_num"].map(
              ident,
            ),
            ",",
          )})
      where
          should_fetch
  )
  `;
}

function makeFetchOldRowsQuery({
  rows,
  tableName1,
  tableName2,
}: {
  rows: FetchOldRowsRow[];
  tableName1: string;
  tableName2: string | undefined;
}): ToSQL {
  const objectDelete = ident(OBJECT_DELETE_FIELD);
  const xactId = ident(TRANSACTION_ID_FIELD);

  function makeCte(tableName: string): ToSQL {
    const tableIdent = ident(tableName);
    return sql`
      select distinct on (object_id, id)
          ${makeObjectIdExprFromTable(tableName)} object_id,
          ${tableIdent}.id,
          ${xactId},
          data::text,
          ${objectDelete},
          row_index
      from
          ${tableIdent}
          join to_fetch on (
              ${makeObjectIdExprFromTable(tableName)} = to_fetch.object_id
              and ${tableIdent}.id = to_fetch.id
          )
      order by
          object_id, id, ${xactId} desc
    `;
  }

  if (tableName2) {
    return sql`
      with ${makeFetchOldRowObjectIdsCte(rows)},
      primary_cte as (${makeCte(tableName1)}),
      secondary_cte as (${makeCte(tableName2)}),
      joined_cte as (
        select
          object_id,
          id,
          (case
            when primary_cte.${xactId} is null then secondary_cte.${xactId}
            when secondary_cte.${xactId} is null then primary_cte.${xactId}
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.${xactId}
            else secondary_cte.${xactId}
            end
          ) as ${xactId},
          (case
            when primary_cte.${xactId} is null then secondary_cte.data
            when secondary_cte.${xactId} is null then primary_cte.data
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.data
            else secondary_cte.data
            end
          ) as data,
          (case
            when primary_cte.${xactId} is null then secondary_cte.${objectDelete}
            when secondary_cte.${xactId} is null then primary_cte.${objectDelete}
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.${objectDelete}
            else secondary_cte.${objectDelete}
            end
          ) as ${objectDelete},
          (case
            when primary_cte.${xactId} is null then secondary_cte.row_index
            when secondary_cte.${xactId} is null then primary_cte.row_index
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.row_index
            else secondary_cte.row_index
            end
          ) as row_index
        from
          primary_cte full outer join secondary_cte using (object_id, id)
      )
      select
        data,
        row_index
      from joined_cte
      where not ${objectDelete}
    `;
  } else {
    return sql`
      with ${makeFetchOldRowObjectIdsCte(rows)}
      select
        data,
        row_index
      from ( ${makeCte(tableName1)}) "sub"
      where not ${objectDelete}
    `;
  }
}

function makeFetchParentSpanInfoQuery({
  rows,
  tableName1,
  tableName2,
}: {
  rows: FetchOldRowsRow[];
  tableName1: string;
  tableName2: string | undefined;
}): ToSQL {
  const objectDelete = ident(OBJECT_DELETE_FIELD);
  const xactId = ident(TRANSACTION_ID_FIELD);

  function makeCte(tableName: string): ToSQL {
    const tableIdent = ident(tableName);
    return sql`
      select distinct on (object_id, id)
          ${makeObjectIdExprFromTable(tableName)} object_id,
          ${tableIdent}.id,
          ${tableIdent}.span_id,
          ${tableIdent}.root_span_id,
          ${tableIdent}.${objectDelete},
          ${tableIdent}.${xactId},
          row_index
      from
          ${tableIdent}
          join to_fetch on (
              ${makeObjectIdExprFromTable(tableName)} = to_fetch.object_id
              and ${tableIdent}.id = to_fetch.id
          )
      order by
          object_id, id, ${xactId} desc
    `;
  }

  if (tableName2) {
    return sql`
      with ${makeFetchOldRowObjectIdsCte(rows)},
      primary_cte as (${makeCte(tableName1)}),
      secondary_cte as (${makeCte(tableName2)}),
      joined_cte as (
        select
          object_id,
          id,
          (case
            when primary_cte.${xactId} is null then secondary_cte.span_id
            when secondary_cte.${xactId} is null then primary_cte.span_id
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.span_id
            else secondary_cte.span_id
            end
          ) as span_id,
          (case
            when primary_cte.${xactId} is null then secondary_cte.root_span_id
            when secondary_cte.${xactId} is null then primary_cte.root_span_id
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.root_span_id
            else secondary_cte.root_span_id
            end
          ) as root_span_id,
          (case
            when primary_cte.${xactId} is null then secondary_cte.${objectDelete}
            when secondary_cte.${xactId} is null then primary_cte.${objectDelete}
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.${objectDelete}
            else secondary_cte.${objectDelete}
            end
          ) as ${objectDelete},
          (case
            when primary_cte.${xactId} is null then secondary_cte.${xactId}
            when secondary_cte.${xactId} is null then primary_cte.${xactId}
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.${xactId}
            else secondary_cte.${xactId}
            end
          ) as ${xactId},
          (case
            when primary_cte.${xactId} is null then secondary_cte.row_index
            when secondary_cte.${xactId} is null then primary_cte.row_index
            when primary_cte.${xactId} > secondary_cte.${xactId} then primary_cte.row_index
            else secondary_cte.row_index
            end
          ) as row_index
        from
          primary_cte full outer join secondary_cte using (object_id, id)
      )
      select
        id,
        COALESCE(span_id, id) span_id,
        COALESCE(root_span_id, id) root_span_id,
        row_index
      from joined_cte
      where not ${objectDelete}
    `;
  } else {
    return sql`
      with ${makeFetchOldRowObjectIdsCte(rows)}
      select
        id,
        COALESCE(span_id, id) span_id,
        COALESCE(root_span_id, id) root_span_id,
        row_index
      from ( ${makeCte(tableName1)}) "sub"
      where not ${objectDelete}
    `;
  }
}

type LogPGOutput = {
  insertedRowData: {
    // The full content of the row we inserted.
    fullRowData: Record<string, unknown>;
    // Undefined if we did not insert a row ref for this row.
    rowRef: RowRef | undefined;
    objectIds: ObjectIdsUnion;
    skipAsyncScoring: boolean;
    fullRowDataByteSize: number;
    inputRow: InputRowMetrics;
  }[];
  promptCacheInvalidations: PromptCacheInvalidation[];
};

const LOG_PG_HELPER_TIMEOUT_MS = 60 * 1000;
const MERGE_ADVISORY_LOCK_TIMEOUT_MS = 10 * 1000;
const MERGE_ADVISORY_LOCK_OPTIMISTIC_TRY_ACQUIRE_MS = 100;
const MERGE_ADVISORY_LOCK_OPTIMISTIC_TRY_ACQUIRE_SLEEP_MS = 10;
const MERGE_ADVISORY_LOCK_MAX_WAITERS = 50;

class QueryTimeoutError extends Error {
  constructor(reason: string) {
    super(`Timed out waiting for ${reason}`);
  }
}

class MergeAdvisoryLocksContentionError extends Error {
  constructor(reason: string) {
    super(`Merge advisory locks contention: ${reason}`);
  }
}

async function logPG({
  mergedRows,
  logToPublisher,
  asyncScoringConfigs,
  forceDisableLogs2,
}: {
  mergedRows: {
    opaqueData: Record<string, unknown>;
    objectIds: ObjectIdsUnion;
    controlFields: SanitizedControlFields;
    inputRow: InputRowMetrics;
  }[];
  logToPublisher: LogToPublisherFn;
  asyncScoringConfigs: Map<string, OnlineScoreConfig[]>;
  forceDisableLogs2?: boolean | null;
}): Promise<LogPGOutput> {
  let rawConn;

  try {
    rawConn = await getPG().connect();
  } catch (e) {
    getLogger().error(
      {
        error: extractErrorText(e),
      },
      "Failed to connect to Postgres",
    );
    throw new InternalServerError(
      `Failed to connect to Postgres (see logs for details)`,
    );
  }
  const brainstoreObjectIdToWalToken = await brainstoreRealtimeGetWalTokens(
    mergedRows.map((r) => ({
      event: { ...r.opaqueData, ...r.controlFields },
      objectIds: r.objectIds,
    })),
    rawConn,
  );
  // From here on out, we're in a transaction, so we wrap the connection in a
  // LogPGConnection to prepend LOG_PG_TRANSACTION_QUERY_PREFIX to all our
  // queries.
  const conn = new LogPGConnection(rawConn);
  await conn.query("begin");
  const pid = rawConn.connectionPid();
  let forciblyTerminate = false;
  try {
    let finishedLogPGHelper = false;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions --  Hard to get the type right when racing against an exception-throwing promise.
    const ret = (await Promise.race([
      otelTraced("write rows to pg", async () => {
        try {
          return await logPGHelper({
            mergedRows,
            logToPublisher,
            asyncScoringConfigs,
            brainstoreObjectIdToWalToken,
            forceDisableLogs2,
            conn,
          });
        } finally {
          finishedLogPGHelper = true;
        }
      }),
      new Promise((_resolve, reject) => {
        setTimeout(() => {
          if (finishedLogPGHelper) {
            return;
          }
          reject(new QueryTimeoutError("insert"));
        }, LOG_PG_HELPER_TIMEOUT_MS);
      }),
    ])) as LogPGOutput;
    await conn.query("commit");
    return ret;
  } catch (e) {
    const orgIds = mergedRows
      .map((r) => r.controlFields.org_id)
      .filter(Boolean);
    if (e instanceof QueryTimeoutError) {
      forciblyTerminate = true;
      getLogger().error(
        {
          error: e.message,
          orgIds,
        },
        "Query timed out",
      );
      throw new HTTPError(429, "Too many concurrent insert requests");
    } else if (e instanceof MergeAdvisoryLocksContentionError) {
      await conn.query("rollback");
      getLogger().error(
        {
          error: e.message,
          orgIds,
        },
        "Merge advisory locks contention",
      );
      throw new HTTPError(
        429,
        "Too many concurrent update requests to the same row",
      );
    } else {
      await conn.query("rollback");
      throw e;
    }
  } finally {
    rawConn.release(forciblyTerminate);
    if (forciblyTerminate) {
      getLogger().warn(`Forcibly terminating process at pid ${pid}`);
      await getPG().query("select pg_terminate_backend($1)", [pid]);
    }
  }
}

const INSERT_BATCH_SIZE = 100;

type SanitizedControlFieldsWithXactId = Omit<
  SanitizedControlFields,
  typeof TRANSACTION_ID_FIELD
> & { [TRANSACTION_ID_FIELD]: string };

type ProcessMergesControlFieldsBase = Pick<
  SanitizedControlFieldsWithXactId,
  | typeof ASYNC_SCORING_CONTROL_FIELD
  | typeof IS_MERGE_FIELD
  | typeof MERGE_PATHS_FIELD
  | typeof PARENT_ID_FIELD
  | "id"
  | "span_id"
  | "root_span_id"
  | "span_parents"
  | "created"
>;

type ProcessMergesBatch<T, U> = {
  opaqueData: Record<string, unknown>;
  objectIds: ObjectIdsUnion;
  controlFields: T;
} & U;

type ProcessMergesOutput<T, U> = {
  opaqueData: Record<string, unknown>;
  objectIds: ObjectIdsUnion;
  controlFields: T;
  auditData?: AuditMerge;
  firstSettingOfMetricsEnd: boolean;
} & U;

async function processMerges<
  T extends ProcessMergesControlFieldsBase,
  U extends object,
>({
  conn,
  tableName1,
  tableName2,
  batch,
}: {
  conn: LogPGConnection;
  tableName1: string;
  tableName2?: string;
  batch: ProcessMergesBatch<T, U>[];
}): Promise<{
  mergeOutput: ProcessMergesOutput<T, U>[];
  didMergeAnything: boolean;
}> {
  const oldRows = await (async () => {
    const fetchOldRowsInput = batch.map(({ objectIds, controlFields }) => ({
      ...objectIds,
      id: controlFields.id,
      shouldFetch: controlFields[IS_MERGE_FIELD],
    }));
    const oldRowsQuery = makeFetchOldRowsQuery({
      rows: fetchOldRowsInput,
      tableName1,
      tableName2,
    });
    const { query, params } = oldRowsQuery.toNumericParamQuery();
    const { rows } = await conn.query(query, params);
    const parsedRows = z
      .object({
        data: z
          .string()
          .transform((s) => z.record(z.unknown()).parse(JSON.parse(s))),
        row_index: z.number(),
      })
      .array()
      .parse(rows);
    const resolvedRows = await resolveRowRefs(
      parsedRows.map((r) => ({
        data: r.data,
        objectIds: batch[r.row_index].objectIds,
      })),
    );
    return resolvedRows.map((row, idx) => ({
      data: row,
      row_index: parsedRows[idx].row_index,
    }));
  })();

  const batchIdxToOldRow = new Map<number, Record<string, unknown>>(
    oldRows.map((oldRowData) => {
      return [oldRowData.row_index, oldRowData.data];
    }),
  );

  let didMergeAnything = false;
  const newBatchAndSkip = batch.map(
    (b, batchIdx): [ProcessMergesOutput<T, U>, boolean] => {
      const { opaqueData, objectIds, controlFields } = b;
      const newHasMetricsEnd = hasMetricsEnd(opaqueData);
      const oldRowRaw = batchIdxToOldRow.get(batchIdx);
      if (oldRowRaw === undefined) {
        return [
          {
            ...b,
            firstSettingOfMetricsEnd: newHasMetricsEnd,
          },
          false,
        ];
      }

      // Inherit some control fields from the original row.
      const oldRowControlFields = insertControlFieldsSchema
        .pick({
          [ASYNC_SCORING_STATE_FIELD]: true,
          created: true,
          span_id: true,
          span_parents: true,
          root_span_id: true,
          [PARENT_ID_FIELD]: true,
        })
        .parse(oldRowRaw);

      {
        const oldRowAsyncScoringState =
          oldRowControlFields[ASYNC_SCORING_STATE_FIELD];
        const newRowAsyncScoringControl =
          controlFields[ASYNC_SCORING_CONTROL_FIELD];
        if (
          asyncScoringSkipMerge({
            oldRowAsyncScoringState,
            newRowAsyncScoringControl,
          })
        ) {
          getLogger().warn(
            {
              objectType: objectIds[OBJECT_TYPE_FIELD],
              rowId: controlFields.id,
              oldRowAsyncScoringState,
              newRowAsyncScoringControl,
            },
            "Skipping merge due to async scoring state",
          );
          return [
            {
              ...b,
              // Doesn't matter because we're skipping the row.
              firstSettingOfMetricsEnd: false,
            },
            true,
          ];
        }
      }

      const newControlFields = {
        ...controlFields,
        ...oldRowControlFields,
      };
      const oldRow = removeControlFields(oldRowRaw);
      const oldHasMetricsEnd = hasMetricsEnd(oldRow);

      // TODO: running this after `mergeRowPopPaths` with a deep copy of `oldRow`
      // and `newOpaqueData` would be _more_ correct -- there's a deep edge
      // case where the custom merging logic specified by `_merge_paths` causes
      // a different merge (e.g. removed keys or partially merged objects) than what we assume here.
      // This change would require rewriting computeAuditMergeData to compare every path in old and new,
      // rather than treating updateToMerge as a change set and only looking at keys in updateToMerge.
      const auditData = computeAuditMergeData({
        objectType: objectIds[OBJECT_TYPE_FIELD],
        oldRow,
        updateToMerge: opaqueData,
      });

      // Detect which top-level fields have changed
      const changedFields = new Set(Object.keys(opaqueData));

      if (!newControlFields[IS_MERGE_FIELD]) {
        throw new Error("Impossible");
      }

      const newOpaqueData = mergeRowPopPaths({
        newRow: oldRow,
        mergeData: opaqueData,
        objectIds: objectIds,
        controlFields: {
          ...newControlFields,
          [IS_MERGE_FIELD]: newControlFields[IS_MERGE_FIELD],
        },
      });
      didMergeAnything = true;

      return [
        {
          ...b,
          opaqueData: newOpaqueData,
          controlFields: newControlFields,
          auditData,
          firstSettingOfMetricsEnd: newHasMetricsEnd && !oldHasMetricsEnd,
          changedFields,
        },
        false,
      ];
    },
  );

  return {
    mergeOutput: newBatchAndSkip
      .filter(([_, skipRow]) => !skipRow)
      .map(([b, _]) => b),
    didMergeAnything,
  };
}

async function logPGHelper({
  mergedRows,
  logToPublisher,
  asyncScoringConfigs,
  brainstoreObjectIdToWalToken,
  forceDisableLogs2,
  conn,
}: {
  mergedRows: {
    opaqueData: Record<string, unknown>;
    objectIds: ObjectIdsUnion;
    controlFields: SanitizedControlFields;
    inputRow: InputRowMetrics;
  }[];
  logToPublisher: LogToPublisherFn;
  asyncScoringConfigs: Map<string, OnlineScoreConfig[]>;
  brainstoreObjectIdToWalToken: Map<string, string>;
  forceDisableLogs2?: boolean | null;
  conn: LogPGConnection;
}): Promise<LogPGOutput> {
  // When inserting, we allow deep-merging rows which have IS_MERGE_FIELD ==
  // True with the latest version of the row with the same ID.
  //
  // In theory, we could do this merge in several places/ways. We note their
  // pros and cons here.
  //
  // - Merge at read time (in pg_scan_objects_query):
  //     - Pros:
  //         - Insert is trivial.
  //         - Total size of stored data is less, because we're not storing
  //           redundant versions of data in the DB.
  //     - Cons:
  //         - We must merge *prior* to doing any post-processing (picking the
  //           latest TRANSACTION_ID, filtering out deleted rows, computing
  //           aggregate statistics, etc). Merging will drop all the stored
  //           columns, so we lose the benefit of creating indices for
  //           post-merged data. We must also re-parse the JSON data each time.
  //         - More total work, since we must do the same merge operation on
  //           every read. This could be addressed by caching reads.
  // - Merge at write time (in log_pg):
  //     - Pros:
  //         - Opposite of read-time cons.
  //         - We can (and do) implement more advanced write-time logic, such
  //         as de-duplication.
  //     - Cons:
  //         - Opposite of read-time pros.
  // - In-place update (in log_pg):
  //     - The main issue is that we lose the versioning property of our data by
  //       transaction ID, since we only maintain one version of each row.

  const insertedRowData: LogPGOutput["insertedRowData"] = [];
  const promptCacheInvalidations: PromptCacheInvalidation[] = [];

  // Our merge operation requires reading the latest version of each row and
  // then updating it, before doing the insert. To avoid race conditions with
  // different inserters on the same row forking the merge history, we take
  // advisory locks. Empirically, this is faster than using serializable
  // isolation and more correct than taking locks during the select query (see
  // experimental/manu/insert_select_update.py).
  await (async () => {
    const advisoryLocks = await advisoryLockInfo(conn);
    const allAdvisoryLockIds = computeAdvisoryLockIds({
      rows: mergedRows.map((x) => ({
        id: x.controlFields.id,
        objectType: x.objectIds[OBJECT_TYPE_FIELD],
        objectId: getAclObjectId(x.objectIds),
        [IS_MERGE_FIELD]: x.controlFields[IS_MERGE_FIELD],
      })),
      minLockId: advisoryLocks.min_merge_lock,
      maxLockId: advisoryLocks.max_merge_lock,
    });

    // First we try optimistically try-acquiring the locks for
    // MERGE_ADVISORY_LOCK_OPTIMISTIC_TRY_ACQUIRE_MS. If that doesn't complete
    // in time, we proceed with taking the rest of the locks cautiously (see
    // comments below).
    let numOptimisticallyAcquiredLocks = 0;
    const optimisticAcquireStartTime = Date.now();
    const optimisticAcquireEndTime =
      optimisticAcquireStartTime +
      MERGE_ADVISORY_LOCK_OPTIMISTIC_TRY_ACQUIRE_MS;

    await otelTraced("optimistic lock acquisition", async () => {
      while (
        numOptimisticallyAcquiredLocks < allAdvisoryLockIds.length &&
        Date.now() < optimisticAcquireEndTime
      ) {
        const lockId = allAdvisoryLockIds[numOptimisticallyAcquiredLocks];
        // Since our parameters are numbers in a reasonable range, not directly
        // from user input, we should be fine not using query params.
        const query = `select pg_try_advisory_xact_lock(${lockId}) result`;
        const { rows } = await conn.query(query);
        if (rows.length === 0) {
          throw new Error("Impossible");
        }
        const result = z.boolean().parse(rows[0].result);
        if (result) {
          numOptimisticallyAcquiredLocks++;
        } else {
          // Brief sleep to allow the lock to free up.
          await new Promise((resolve) =>
            setTimeout(
              resolve,
              MERGE_ADVISORY_LOCK_OPTIMISTIC_TRY_ACQUIRE_SLEEP_MS,
            ),
          );
        }
      }
    });

    // If we were able to acquire all the locks optimistically, we're done.
    if (numOptimisticallyAcquiredLocks == allAdvisoryLockIds.length) {
      return;
    }

    const remainingLockIds = allAdvisoryLockIds.slice(
      numOptimisticallyAcquiredLocks,
    );

    // Before we acquire the remaining locks, we check how many connections are
    // waiting on this set of locks. If there are too many concurrent waiters,
    // this likely means someone is trying to update a specific set of rows in a
    // loop.  Rather than waiting for all of these updates to finish and piling
    // up connections, we reject the update ahead of time.
    {
      const queryTemplate = sql`
        select count(distinct pid) cnt
        from pg_locks
        where
          locktype = 'advisory'
          and mode = 'ExclusiveLock'
          and not granted
          and waitstart is not null
          and objid = ANY(${remainingLockIds})
      `;
      const { query, params } = queryTemplate.toNumericParamQuery();
      const { rows } = await conn.query(query, params);
      if (rows.length !== 1) {
        throw new Error("Impossible");
      }
      const numWaiters = z.number().parse(rows[0].cnt);
      if (numWaiters > MERGE_ADVISORY_LOCK_MAX_WAITERS) {
        throw new MergeAdvisoryLocksContentionError(
          `Too many concurrent waiters: ${numWaiters}`,
        );
      }
    }

    // Postgres does not allow running multiple (semicolon-separated)
    // parameterized queries in a single statement. So we use
    // `toPlainStringQuery` to run this. Since our parameters are numbers in a
    // reasonable range, not directly from user input, this should be okay.
    const query = join(
      remainingLockIds.map(
        (lockId) =>
          sql`select pg_advisory_xact_lock(${lockId}) /* merge lock */`,
      ),
      ";",
    ).toPlainStringQuery();

    // In pathological cases, e.g. somebody is doing a lot of merge updates to a
    // single row, the lock acquisition can get hung up for a long time. We add
    // a timeout to prevent a backlog of queries from building up and force the
    // caller to retry.
    let finishedQuery = false;
    await Promise.race([
      otelTraced("acquire advisory locks", async () => {
        try {
          return await conn.query(query);
        } finally {
          finishedQuery = true;
        }
      }),
      new Promise((_resolve, reject) => {
        setTimeout(() => {
          if (finishedQuery) {
            return;
          }
          const mergeIds = mergedRows
            .filter((x) => x.controlFields[IS_MERGE_FIELD])
            .map((x) =>
              JSON.stringify({
                objectType: x.objectIds[OBJECT_TYPE_FIELD],
                objectId: getAclObjectId(x.objectIds),
                id: x.controlFields.id,
              }),
            );
          getLogger().warn(
            { mergeIds: mergeIds.join(", ") },
            "Merge advisory locks timeout",
          );
          reject(new QueryTimeoutError("merge advisory locks"));
        }, MERGE_ADVISORY_LOCK_TIMEOUT_MS);
      }),
    ]);
  })();

  // Assign transaction IDs to all rows to insert. We must do this after
  // acquiring the advisory locks, so that concurrent inserters on the same
  // ids are guaranteed to get their transaction IDs assigned serially, in
  // order of insertion.
  //
  // Note that this serialization property is only strictly necessary for
  // merge inserts (we cannot merge a later transaction ID row before the
  // earlier transaction ID row), so if there are no merges, we could have
  // assigned transaction IDs earlier.
  const xactId = await nextXactId();
  const rowControlFieldsXactId = mergedRows.map(
    ({ controlFields }): SanitizedControlFieldsWithXactId => ({
      ...controlFields,
      [TRANSACTION_ID_FIELD]: xactId,
    }),
  );

  const dataRows: {
    opaqueData: Record<string, unknown>;
    objectIds: ObjectIdsUnion;
    controlFields: SanitizedControlFieldsWithXactId;
    inputRow: InputRowMetrics;
  }[] = [];
  const commentRows: typeof dataRows = [];
  mergedRows.forEach(({ opaqueData, objectIds, inputRow }, idx) => {
    const controlFields = rowControlFieldsXactId[idx];
    if ("comment" in opaqueData) {
      commentRows.push({
        opaqueData,
        objectIds,
        controlFields,
        inputRow,
      });
    } else {
      dataRows.push({
        opaqueData,
        objectIds,
        controlFields,
        inputRow,
      });
    }
  });

  // Keep track of the data necessary to resolve PARENT_ID_FIELD references in
  // rows.
  const idToSpanParent: IdToSpanParentMap = new Map();
  const dataRowBatches = makeBatch(dataRows, INSERT_BATCH_SIZE);
  for (const batch0 of dataRowBatches) {
    const batch1 = batch0.map((b) => {
      const { row: newControlFields, auditFields: poppedAuditFields } =
        popAuditFields(b.controlFields);
      const auditEntry = {
        ...poppedAuditFields,
        audit_data: {
          action: z
            .boolean()
            .nullish()
            .parse(newControlFields[OBJECT_DELETE_FIELD])
            ? ("delete" as const)
            : ("upsert" as const),
        },
      };
      return {
        ...b,
        controlFields: newControlFields,
        auditEntry,
      };
    });

    const { mergeOutput, didMergeAnything } = await processMerges({
      conn,
      tableName1: PG_LOGS_TABLE,
      tableName2: PG_LOGS2_TABLE,
      batch: batch1,
    });
    const batch2 = mergeOutput.map((b) => {
      const { auditEntry, auditData, ...rest } = b;
      return {
        ...rest,
        auditEntry: {
          ...auditEntry,
          audit_data: auditData ?? auditEntry.audit_data,
        },
      };
    });

    // We treat any remaining merge rows that we didn't find as non-merge rows
    // and backfill them as such.
    const batch3 = batch2.map((b) => {
      const { controlFields } = b;
      // The only reason we consider all possible rows here, instead of only the
      // merged rows, is because it's possible we merged into an old DB row
      // which wasn't backfilled. So we bockfill now in order to fit all the
      // invariants for parent ID resolution.
      const {
        [IS_MERGE_FIELD]: _isMerge,
        [MERGE_PATHS_FIELD]: _mergePaths,
        ...controlFieldsRest
      } = controlFields;
      return {
        ...b,
        controlFields: {
          ...controlFieldsRest,
          ...backfillNonMergeRow({
            [IS_MERGE_FIELD]: false,
            ...controlFieldsRest,
          }),
        },
      };
    });

    // Next, we try to resolve PARENT_ID_FIELD references. Populate the map with
    // any records which are already resolved.
    batch3.forEach(({ objectIds, controlFields }) => {
      const resolvedRow = resolvedRowSchema.safeParse(controlFields);
      if (resolvedRow.success) {
        addResolvedRow({
          row: resolvedRow.data,
          rowId: controlFields.id,
          objectIds,
          idToSpanParent,
        });
      }
    });

    // Immediately resolve any parent_id references that we can. Note that we
    // might end up utilizing parent references from a previous batch, which
    // should work as long as dependent rows are logged after the rows they
    // reference.
    const batch3AddInvalidParentIdToRow = makeAddInvalidParentIdToRow(batch3);
    resolveSpanParents({
      rows: batch3,
      idToSpanParent,
      addInvalidParentIdToRow: batch3AddInvalidParentIdToRow,
    });

    // If there are any unresolved parent ids, we must fetch their span info
    // from the DB, and then resolve more references.
    if (batch3.some(({ controlFields }) => !!controlFields[PARENT_ID_FIELD])) {
      const fetchParentSpanInfoInput = batch3.map(
        ({ objectIds, controlFields }) => ({
          ...objectIds,
          id: controlFields[PARENT_ID_FIELD] ?? "",
          shouldFetch: !!controlFields[PARENT_ID_FIELD],
        }),
      );
      const parentSpanInfoQuery = makeFetchParentSpanInfoQuery({
        rows: fetchParentSpanInfoInput,
        tableName1: PG_LOGS_TABLE,
        tableName2: PG_LOGS2_TABLE,
      });
      const { query, params } = parentSpanInfoQuery.toNumericParamQuery();
      const { rows } = await conn.query(query, params);
      rows.forEach((rowU) => {
        const row = z
          .object({
            id: z.string(),
            span_id: z.string(),
            root_span_id: z.string(),
            row_index: z.number(),
          })
          .parse(rowU);
        const objectIdKey = makeObjectIdKey(batch3[row.row_index].objectIds);
        idToSpanParent.set(makeIdToSpanParentKey({ id: row.id, objectIdKey }), {
          kind: "live",
          id: row.id,
          objectIdKey,
          span_id: row.span_id,
          root_span_id: row.root_span_id,
        });
      });

      resolveSpanParents({
        rows: batch3,
        idToSpanParent,
        addInvalidParentIdToRow: batch3AddInvalidParentIdToRow,
      });
    }

    // Any remaining references point to invalid parent IDs (nonexistent or
    // deleted). Resolve them as deleted.
    const batch4 = batch3.map((b, batchIdx) => {
      const { objectIds, controlFields } = b;
      if (controlFields[PARENT_ID_FIELD] === null) {
        const { [PARENT_ID_FIELD]: _parentId, ...controlFieldsRest } =
          controlFields;
        return {
          ...b,
          controlFields: controlFieldsRest,
        };
      }
      const objectIdKey = makeObjectIdKey(objectIds);
      const spanParent = mapSetDefault(
        idToSpanParent,
        makeIdToSpanParentKey({ id: controlFields.id, objectIdKey }),
        { kind: "deleted", id: controlFields[PARENT_ID_FIELD], objectIdKey },
      );
      return {
        ...b,
        controlFields: spanParentResolveRow({
          spanParent,
          objectIds,
          row: controlFields,
          addInvalidParentId: (invalidParentId) =>
            batch3AddInvalidParentIdToRow(batchIdx, invalidParentId),
        }),
      };
    });

    // Determine [ASYNC_SCORING_STATE_FIELD] for each row.
    batch4.forEach((b) => {
      const { opaqueData, objectIds, controlFields, firstSettingOfMetricsEnd } =
        b;
      const changedFields =
        z.object({ changedFields: z.set(z.string()).nullish() }).parse(b)
          .changedFields ?? undefined;
      const nextScoringResult = nextAsyncScoringState({
        asyncScoringState: controlFields[ASYNC_SCORING_STATE_FIELD],
        asyncScoringControl: controlFields[ASYNC_SCORING_CONTROL_FIELD],
        skipAsyncScoring: controlFields[SKIP_ASYNC_SCORING_FIELD],
        rowInfo: {
          objectIds,
          span_id: controlFields.span_id,
          root_span_id: controlFields.root_span_id,
          span_parents: controlFields.span_parents,
          span_name: getSpanName(opaqueData),
          reachedSelectionEpoch: firstSettingOfMetricsEnd,
          opaqueData,
          changedFields,
        },
        asyncScoringConfigs,
      });
      controlFields[ASYNC_SCORING_STATE_FIELD] = nextScoringResult.state;
      controlFields[SKIP_ASYNC_SCORING_FIELD] = nextScoringResult.shouldSkip;
    });

    batch4.forEach(({ opaqueData, objectIds, controlFields }) => {
      if ("tags" in opaqueData && controlFields.span_parents.length) {
        throw new BadRequestError("Cannot specify 'tags' for non-root spans");
      }
      if (
        (objectIds[OBJECT_TYPE_FIELD] === "project_prompts" ||
          objectIds[OBJECT_TYPE_FIELD] === "project_functions") &&
        // If you delete multiple times, slug may not be present, but
        // that's ok.
        (!controlFields[OBJECT_DELETE_FIELD] || "slug" in opaqueData)
      ) {
        promptCacheInvalidations.push({
          projectId: objectIds.project_id,
          slug: z.string().nullish().parse(opaqueData["slug"]),
          promptId: controlFields.id,
        });
      } else if (objectIds[OBJECT_TYPE_FIELD] === "prompt_session") {
        // Prompt sessions use pretty much the same format as prompts, but
        // instead of the prompt's individual id, they use the
        // prompt_session_id:id as the id prefix.
        //
        // The project_id should have been filled into the opaque data when we
        // filled all parent id columns.
        const projectId = z.string().nullish().parse(opaqueData["project_id"]);
        if (!projectId) {
          throw new Error("Impossible");
        }
        promptCacheInvalidations.push({
          projectId,
          promptId: `${objectIds.prompt_session_id}:${controlFields.id}`,
        });
      }
    });

    const batch5 = batch4.map((b, rowIdx) => {
      const {
        opaqueData,
        objectIds,
        controlFields: controlFieldsRaw,
        auditEntry,
        inputRow,
      } = b;
      const controlFields: {
        id: string;
        created: string;
        span_id: string;
        span_parents: string[];
        root_span_id: string;
        org_id: string | null;
        user_id: string | null;
        [OBJECT_DELETE_FIELD]: boolean;
        [INTERNAL_OVERRIDE_TRANSACTION_ID_FIELD]?: string;
        [TRANSACTION_ID_FIELD]: string;
        [ASYNC_SCORING_STATE_FIELD]: AsyncScoringState;
        [ASYNC_SCORING_CONTROL_FIELD]: AsyncScoringControl | null;
        [SKIP_ASYNC_SCORING_FIELD]: boolean;
        [IS_MERGE_FIELD]?: never;
        [MERGE_PATHS_FIELD]?: never;
        [PARENT_ID_FIELD]?: never;
        [AUDIT_SOURCE_FIELD]?: never;
        [AUDIT_METADATA_FIELD]?: never;
      } = controlFieldsRaw;
      const {
        id,
        created,
        span_id,
        span_parents,
        root_span_id,
        org_id,
        user_id,
        [OBJECT_DELETE_FIELD]: objectDelete,
        [INTERNAL_OVERRIDE_TRANSACTION_ID_FIELD]: overrideXactId,
        [TRANSACTION_ID_FIELD]: xactId,
        [ASYNC_SCORING_STATE_FIELD]: asyncScoringState,
        [SKIP_ASYNC_SCORING_FIELD]: skipAsyncScoring,
      } = controlFields;
      const finalOpaqueData: Record<string, unknown> = {
        ...opaqueData,
        id,
        created,
        span_id,
        span_parents: span_parents.length ? span_parents : undefined,
        root_span_id,
        org_id: org_id ?? undefined,
        user_id: user_id ?? undefined,
        [OBJECT_DELETE_FIELD]: objectDelete || undefined,
        // _bt_internal_override_xact_id is only used for testing currently
        [TRANSACTION_ID_FIELD]: overrideXactId ?? xactId,
        [ASYNC_SCORING_STATE_FIELD]: asyncScoringState ?? undefined,
        ...(shouldUseBrainstore(objectIds[OBJECT_TYPE_FIELD])
          ? {
              _pagination_key: makePaginationKey({ xactId, rowIdx }),
            }
          : {}),
      };

      if (OBJECT_TYPE_FIELD in finalOpaqueData) {
        throw new Error("OBJECT_TYPE_FIELD should not end up in final data");
      }

      const data = sanitizeJsonObject(finalOpaqueData);
      const auditData = sanitizeJsonObject(auditEntry);
      const serializedData = JSON.stringify(data);
      return {
        data,
        auditData,
        serializedData,
        objectIds,
        xactId,
        skipAsyncScoring,
        orgId: org_id,
        inputRow,
      };
    });

    const rowRefs = await brainstoreRealtimeWalInsert(
      batch5.map((r) => ({
        event: r.data,
        objectIds: r.objectIds,
      })),
      brainstoreObjectIdToWalToken,
    );

    const canContainRowRefsMemo: Map<
      ObjectType,
      Map<string, boolean>
    > = new Map();
    const cachedCanContainRowRefs = (objectIds: ObjectIdsUnion) => {
      const objectType = objectIds[OBJECT_TYPE_FIELD];
      const objectId = getAclObjectId(objectIds);
      const objectIdToRes = mapSetDefault(
        canContainRowRefsMemo,
        objectType,
        new Map(),
      );
      const cachedRes = objectIdToRes.get(objectId);
      if (cachedRes !== undefined) {
        return cachedRes;
      }
      const res = canContainRowRefs(objectType, objectId);
      objectIdToRes.set(objectId, res);
      return res;
    };

    const batch6 = batch5.map((r, idx) => {
      const rowRef = rowRefs.get(idx);
      if (!(rowRef && cachedCanContainRowRefs(r.objectIds))) {
        return {
          ...r,
          dataWithRowRef: undefined,
        };
      }

      // If we can insert a row ref, then only retain the fields in data that
      // are essential for metadata queries.
      const objectIdFields = Object.keys(r.objectIds).filter(
        (f) => f !== OBJECT_TYPE_FIELD,
      );
      const essentialDataFields = [
        "id",
        "created",
        "span_id",
        "span_parents",
        "root_span_id",
        "org_id",
        "project_id",
        "user_id",
        OBJECT_DELETE_FIELD,
        TRANSACTION_ID_FIELD,
      ].concat(objectIdFields);
      const dataWithRowRef = {
        ...Object.fromEntries(essentialDataFields.map((f) => [f, r.data[f]])),
        [ROW_REF_FIELD]: rowRef,
      };
      return {
        ...r,
        dataWithRowRef,
      };
    });

    if (batch6.length > 0) {
      const logsItems: {
        valueTuple: ToSQL;
        serializedDataBytes: number;
        projectId: unknown;
      }[] = [];
      const logs2Items: {
        valueTuple: ToSQL;
        serializedDataBytes: number;
        projectId: unknown;
      }[] = [];
      batch6.forEach(
        ({
          data,
          serializedData: serializedFullData,
          dataWithRowRef,
          auditData,
          objectIds,
        }) => {
          const objectId = getAclObjectId(objectIds);
          const serializedData = dataWithRowRef
            ? JSON.stringify(dataWithRowRef)
            : serializedFullData;
          const serializedAuditData = JSON.stringify(auditData);
          const item = {
            valueTuple: sql`(${serializedData}, ${serializedAuditData})`,
            serializedDataBytes: serializedData.length,
            projectId: data.project_id,
          };
          (canPgInsertLogs2(
            objectIds[OBJECT_TYPE_FIELD],
            objectId,
            forceDisableLogs2,
          )
            ? logs2Items
            : logsItems
          ).push(item);
        },
      );
      const [logsRows, logs2Rows] = await Promise.all(
        [
          { items: logsItems, table: PG_LOGS_TABLE },
          { items: logs2Items, table: PG_LOGS2_TABLE },
        ].map(async ({ items, table }) => {
          if (items.length === 0) {
            return [];
          }
          const projectIds = [
            ...new Set(
              items.map(({ projectId }) => projectId).filter(Boolean),
            ).values(),
          ];
          const totalBytesPg = items.reduce(
            (acc, { serializedDataBytes }) => acc + serializedDataBytes,
            0,
          );
          const valueTuples = items.map(({ valueTuple }) => valueTuple);
          const insertMetadataComment = snippet(
            `/* projectIds: [${projectIds}]; didMergeAnything: ${didMergeAnything}; totalBytesKb: ${Math.floor(totalBytesPg / 1024)} */`,
          );
          const insertDataQuery = sql`
          ${insertMetadataComment}
          insert into ${ident(table)}(data, audit_data)
          values ${join(valueTuples, ", ")}
          returning row_created
        `;
          const { query, params } = insertDataQuery.toNumericParamQuery();
          const { rows: rowsRaw } = await (async () => {
            try {
              return await conn.query(query, params);
            } catch (error) {
              if (
                error instanceof DatabaseError &&
                error.code &&
                ["22P02"].includes(error.code)
              ) {
                throw new BadRequestError(
                  `Invalid characters in log data: ${error.detail ?? "Unknown error"}`,
                );
              }
              throw error;
            }
          })();

          const rows = rowsRaw.map((r) => ({
            rowCreated: z.string().datetime().parse(r["row_created"]),
          }));
          return rows;
        }),
      );
      const rows = [...logsRows, ...logs2Rows];

      // We must broadcast rows after merging into the DB, because we don't want
      // to publish un-merged data.
      publishObjectRows({
        rows: batch6.map(({ data, objectIds, orgId }) => ({
          fullRowData: data,
          objectIds,
          orgId: orgId ?? undefined,
        })),
        auditLog: false,
        logToPublisher,
      });
      publishObjectRows({
        rows: batch6.map(
          ({ data, auditData, objectIds, xactId, orgId }, idx) => {
            const auditLogObjectIds = auditLogObjectIdsSchema.parse(data);
            return {
              fullRowData: makeAuditLogEntry({
                objectIds: auditLogObjectIds,
                xactId,
                created: rows[idx].rowCreated,
                auditLogEntryInput: auditData,
              }),
              objectIds,
              orgId: orgId ?? undefined,
            };
          },
        ),
        auditLog: true,
        logToPublisher,
      });

      insertedRowData.push(
        ...batch6.map(
          ({
            data,
            dataWithRowRef,
            serializedData,
            objectIds,
            skipAsyncScoring,
            inputRow,
          }) => ({
            fullRowData: data,
            rowRef: dataWithRowRef ? dataWithRowRef[ROW_REF_FIELD] : undefined,
            fullRowDataByteSize: serializedData.length,
            objectIds,
            skipAsyncScoring,
            inputRow,
          }),
        ),
      );
    }
  }

  const commentRowBatches = makeBatch(commentRows, INSERT_BATCH_SIZE);

  for (const batch0 of commentRowBatches) {
    const batch1 = batch0.map((b) => {
      const { row: newControlFields, auditFields: poppedAuditFields } =
        popAuditFields(b.controlFields);
      return {
        ...b,
        controlFields: newControlFields,
        poppedAuditFields,
      };
    });

    const batch2 = (
      await processMerges({
        conn,
        tableName1: PG_COMMENTS_TABLE,
        batch: batch1,
      })
    ).mergeOutput.map(({ auditData, ...b }) => b);

    const batch3 = batch2.map((b) => {
      const {
        opaqueData,
        objectIds,
        controlFields,
        poppedAuditFields,
        inputRow,
      } = b;
      // Only pull out the parts of the control fields that we need.
      const {
        id,
        created,
        org_id,
        user_id,
        [OBJECT_DELETE_FIELD]: objectDelete,
        [TRANSACTION_ID_FIELD]: xactId,
      }: {
        id: string;
        created: string | null;
        org_id: string | null;
        user_id: string | null;
        [OBJECT_DELETE_FIELD]: boolean;
        [TRANSACTION_ID_FIELD]: string;
      } = controlFields;
      if (!created) {
        throw new InternalServerError(
          `comment rows should have had their 'created' field backfilled`,
        );
      }
      const finalOpaqueData: Record<string, unknown> = {
        ...opaqueData,
        id,
        created,
        org_id: org_id ?? undefined,
        user_id: user_id ?? undefined,
        [OBJECT_DELETE_FIELD]: objectDelete || undefined,
        [TRANSACTION_ID_FIELD]: xactId,
        ...poppedAuditFields,
      };
      const data = sanitizeJsonObject(finalOpaqueData);
      return {
        data,
        serializedData: JSON.stringify(data),
        objectIds,
        inputRow,
      };
    });

    if (batch3.length > 0) {
      const valueTuples = batch3.map(
        ({ serializedData }): ToSQL => sql`(${serializedData})`,
      );
      const insertDataQuery = sql`
        insert into ${ident(PG_COMMENTS_TABLE)}(data)
        values ${join(valueTuples, ", ")}
        returning row_created
      `;
      const { query, params } = insertDataQuery.toNumericParamQuery();
      const { rows: rowsRaw } = await conn.query(query, params);
      const rows = rowsRaw.map((r) => ({
        rowCreated: z.string().datetime().parse(r["row_created"]),
      }));

      // Patch the created value, as we do in the audit log query, to be the
      // row_created for the network broadcast.
      const broadcastBatch = batch3.map(({ data, objectIds }, idx) => ({
        data: {
          ...data,
          created: rows[idx].rowCreated,
        },
        objectIds,
      }));

      publishObjectRows({
        rows: broadcastBatch.map(({ data, objectIds }) => ({
          fullRowData: data,
          objectIds,
          orgId: undefined, // doesn't matter for comments
        })),
        auditLog: true,
        logToPublisher,
      });

      insertedRowData.push(
        ...batch3.map(({ data, serializedData, objectIds, inputRow }) => ({
          fullRowData: data,
          rowRef: undefined,
          fullRowDataByteSize: serializedData.length,
          objectIds,
          skipAsyncScoring: true,
          inputRow,
        })),
      );
    }
  }

  return {
    insertedRowData,
    promptCacheInvalidations,
  };
}

export const runLogData = withTelemetry(
  runLogDataImpl,
  async ({
    args: [{ token, appOrigin }],
    output: { insertedRows, xactId },
  }) => {
    const now = new Date();

    const events: (LogInsertedEvent | undefined)[] = insertedRows.map(
      ({ fullRowData, objectIds, inputRow }) => {
        if (!xactId) {
          // This should never happen if we have insertedRows, but satisfies type checker.
          throw new Error("No transaction IDs found in inserted rows");
        }

        const objectId = getAclObjectId(objectIds);
        const { id: row_id, org_id } = z
          .object({
            id: z.string(),
            org_id: z.string().nullish(),
          })
          .parse(fullRowData);

        if (!org_id) {
          logger.warn("No org id found in row", { row_id });
          return;
        }

        const object_type = objectIds[OBJECT_TYPE_FIELD];

        return {
          event_name: "LogInsertedEvent",
          idempotency_key: idempotency(row_id, object_type, objectId, xactId),
          timestamp: now.toISOString(),
          external_customer_id: org_id,
          properties: {
            org_id,
            log_bytes: inputRow.byteSize,
            scores_count: inputRow.scoreCount,
            metrics_count: inputRow.metricCount,
          },
        };
      },
    );

    return {
      events,
      token,
      appOrigin,
    };
  },
);

type BackfillNonMergeRowInput = {
  [IS_MERGE_FIELD]: false;
  span_id: string | null;
  root_span_id: string | null;
  [PARENT_ID_FIELD]: string | null;
  created: string | null;
};

type BackfillNonMergeRowOutput = {
  span_id: string;
  created: string;
} & (
  | {
      [PARENT_ID_FIELD]: null;
      root_span_id: string;
    }
  | {
      [PARENT_ID_FIELD]: string;
      root_span_id: string | null;
    }
);

function backfillNonMergeRow(
  row: BackfillNonMergeRowInput,
): BackfillNonMergeRowOutput {
  const ret = {
    span_id: row.span_id ?? uuidv4(),
    created: row.created ?? new Date().toISOString(),
  };
  return {
    ...ret,
    // If the row is not waiting for a parent id resolution and is missing a
    // root_span_id, complete it as a root span.
    ...(row[PARENT_ID_FIELD]
      ? {
          [PARENT_ID_FIELD]: row[PARENT_ID_FIELD],
          root_span_id: row.root_span_id,
        }
      : {
          [PARENT_ID_FIELD]: null,
          root_span_id: row.root_span_id ?? ret.span_id,
        }),
  };
}

// Postgres does not support null bytes in JSON data, so we strip them out.
// https://stackoverflow.com/questions/31671634/handling-unicode-sequences-in-postgresql
//
// Also many languages do not support invalid surrogate pairs in their unicode
// representation, so we use the str.toWellFormed() method to repair any such
// strings.
//
// Being lazy with type signatures here, but we know that the input is always a
// "normal" object rather than a class or something, so it should be safe to
// cast the return value back to T.
function sanitizeJsonObject<T>(obj: T): T {
  if (typeof obj === "string") {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return toWellFormed(obj.replace(/\0/g, "")) as T;
  } else if (Array.isArray(obj)) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return obj.map(sanitizeJsonObject) as T;
  } else if (typeof obj === "object" && obj !== null) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return Object.fromEntries(
      Object.entries(obj).map(([k, v]) => [
        sanitizeJsonObject(k),
        sanitizeJsonObject(v),
      ]),
    ) as T;
  } else {
    return obj;
  }
}

export function mergeRowPopPaths<
  T extends { [IS_MERGE_FIELD]: true; [MERGE_PATHS_FIELD]: string[][] },
>({
  newRow,
  mergeData,
  objectIds,
  controlFields,
}: {
  newRow: Record<string, unknown>;
  mergeData: Readonly<Record<string, unknown>>;
  objectIds: ObjectIdsUnion;
  controlFields: T;
}): Record<string, unknown> {
  const logicalToStorageMap = getLogicalToStorageMap(
    objectIds[OBJECT_TYPE_FIELD],
  );
  const mergePathsStorage = controlFields[MERGE_PATHS_FIELD].map((p) =>
    convertPathStorageLogical(logicalToStorageMap, p),
  );
  return mergeDictsWithPaths({
    mergeInto: newRow,
    mergeFrom: mergeData,
    mergePaths: mergePathsStorage,
  });
}

function publishObjectRows({
  rows,
  auditLog,
  logToPublisher,
}: {
  rows: {
    fullRowData: Record<string, unknown>;
    objectIds: ObjectIdsUnion;
    orgId: string | undefined;
  }[];
  auditLog: boolean;
  logToPublisher: LogToPublisherFn;
}): void {
  rows.forEach(({ fullRowData, objectIds, orgId }) => {
    // Normalize the row before broadcasting it to clients.
    const columnStorageToLogical = getStorageToLogicalMap(
      objectIds[OBJECT_TYPE_FIELD],
    );
    const normalizedData = Object.fromEntries(
      Object.entries(fullRowData).map(([k, v]) => [
        recordFind(columnStorageToLogical, k) ?? k,
        v,
      ]),
    );
    // Collect broadcast targets.
    const broadcastTargets: { objectType: ObjectType; objectId: string }[] = [];
    switch (objectIds[OBJECT_TYPE_FIELD]) {
      case "experiment":
        broadcastTargets.push({
          objectType: "experiment",
          objectId: objectIds.experiment_id,
        });
        break;
      case "dataset":
        broadcastTargets.push({
          objectType: "dataset",
          objectId: objectIds.dataset_id,
        });
        break;
      case "prompt_session":
        if (objectIds.prompt_session_id.endsWith(":x")) {
          broadcastTargets.push({
            objectType: "playground_logs",
            objectId: objectIds.prompt_session_id.slice(0, -2), // Remove the :x
          });
        } else {
          broadcastTargets.push({
            objectType: "prompt_session",
            objectId: objectIds.prompt_session_id,
          });
        }
        break;
      case "playground_logs":
        broadcastTargets.push({
          objectType: "playground_logs",
          objectId: objectIds.prompt_session_id,
        });
        break;
      case "project_logs":
        broadcastTargets.push({
          objectType: "project_logs",
          objectId: objectIds.project_id,
        });
        break;
      case "project_prompts":
        broadcastTargets.push({
          objectType: "project_prompts",
          objectId: objectIds.project_id,
        });
        broadcastTargets.push({
          objectType: "project_functions",
          objectId: objectIds.project_id,
        });
        if (orgId) {
          broadcastTargets.push({
            objectType: "org_prompts",
            objectId: orgId,
          });
          broadcastTargets.push({
            objectType: "org_functions",
            objectId: orgId,
          });
        }
        break;
      case "project_functions":
        broadcastTargets.push({
          objectType: "project_functions",
          objectId: objectIds.project_id,
        });
        if (orgId) {
          broadcastTargets.push({
            objectType: "org_functions",
            objectId: orgId,
          });
        }
        break;
      default:
        const x: never = objectIds;
        throw new Error(`Unknown objectIds: ${JSON.stringify(x)}`);
    }

    broadcastTargets.forEach(({ objectType, objectId }) => {
      logToPublisher({
        object_type: objectType,
        object_id: objectId,
        is_audit_log: auditLog,
        type: "data",
        value: normalizedData,
      });
    });
  });
}

// For each org, broadcast metadata about the logged rows.
export function broadcastOrgProjectMetadata({
  rows,
  logToPublisher,
}: {
  rows: Record<string, unknown>[];
  logToPublisher: LogToPublisherFn;
}) {
  const orgIdToObjectTypeToObjectIdMetadata: Record<
    string,
    Record<string, Record<string, Record<string, unknown>>>
  > = {};
  rows.forEach((row) => {
    const { org_id, project_id, id } = z
      .object({
        org_id: z.string().nullish(),
        project_id: z.string().nullish(),
        id: z.string(),
      })
      .parse(row);
    const objectIds = objectIdsUnionSchema.parse(row);
    if (!(org_id && project_id)) {
      return;
    }
    const { aclObjectType, overrideRestrictObjectType } =
      objectTypeToAclObjectType(objectIds[OBJECT_TYPE_FIELD]);
    const rowLeafObjectId =
      objectIds[OBJECT_TYPE_FIELD] === "project_prompts" ||
      objectIds[OBJECT_TYPE_FIELD] === "project_functions"
        ? id
        : getAclObjectId(objectIds);

    // Currently we just broadcast the metadata of "which object IDs were written to".
    recordSetDefault(
      recordSetDefault(
        recordSetDefault(orgIdToObjectTypeToObjectIdMetadata, org_id, {}),
        overrideRestrictObjectType ?? aclObjectType,
        {},
      ),
      rowLeafObjectId,
      {},
    );
  });

  Object.entries(orgIdToObjectTypeToObjectIdMetadata).forEach(
    ([orgId, objectTypeToObjectIdMetadata]) => {
      logToPublisher({
        object_type: "org_project_metadata",
        object_id: orgId,
        is_audit_log: false,
        type: "data",
        value: objectTypeToObjectIdMetadata,
      });
    },
  );
}

function makeAddInvalidParentIdToRow(
  batch: { opaqueData: Record<string, unknown> }[],
) {
  return (rowIdx: number, invalidParentId: string) => {
    batch[rowIdx].opaqueData["metadata"] = {
      ...z
        .record(z.unknown())
        .nullish()
        .parse(batch[rowIdx].opaqueData["metadata"]),
      invalid_parent_id: invalidParentId,
    };
  };
}

const rowIdKeySchema = z.object({
  object_type: z.string(),
  object_id: z.string(),
  row_id: z.string(),
});

type RowIdKey = z.infer<typeof rowIdKeySchema>;

function makeRowIdKey(objectIds: ObjectIdsUnion, rowId: string): string {
  const objectId = getAclObjectId(objectIds);
  const keyObj: RowIdKey = {
    object_type: objectIds[OBJECT_TYPE_FIELD],
    object_id: objectId,
    row_id: rowId,
  };
  return JSON.stringify(keyObj, deterministicReplacer);
}

function canPgInsertLogs2(
  objectType: ObjectType,
  objectId: string,
  forceDisableLogs2: boolean | null | undefined,
): boolean {
  return canPgInsertLogs2Base(objectType, objectId) && !forceDisableLogs2;
}

// Returns null if we cannot auto-convert attachments.
async function getAttachmentsObjectStore(): Promise<ObjectStore | undefined> {
  if (DISABLE_ATTACHMENT_OPTIMIZATION) {
    return undefined;
  }

  const pino = getLogger();
  try {
    checkAttachmentsEnvironment();
  } catch (e) {
    pino.debug(
      { error: extractErrorText(e) },
      "Cannot auto-convert attachments because the attachment bucket is not configured: " +
        extractErrorText(e),
    );
    return undefined;
  }

  try {
    return await makeObjectStore();
  } catch (e) {
    pino.debug(
      { error: extractErrorText(e) },
      "Cannot auto-convert attachments because the attachment bucket is not accessible: " +
        extractErrorText(e),
    );
    return undefined;
  }
}

function replaceOpaqueDataWithAttachments({
  opaqueData,
  objectStore,
  orgId,
  uploadPromises,
  incrementBytes,
}: {
  opaqueData: Record<string, unknown>;
  objectStore: ObjectStore;
  orgId: string;
  uploadPromises: Promise<void>[];
  incrementBytes: (bytes: number) => void;
}): Record<string, unknown> {
  // We should have validated that this will not throw in getObjectStoreForAutoConvertAttachments().
  const { attachmentBucketName, attachmentBucketPrefix } =
    checkAttachmentsEnvironment();

  const attachments = {}; // Unused
  return replacePayloadWithAttachmentsCallback({
    data: opaqueData,
    replaceWithAttachment: (args): AttachmentReference => {
      // We're mimicking what we do in runUploadAttachmentRequest.
      const id = newId();
      const attachmentKey = makeAttachmentKey({
        prefix: attachmentBucketPrefix,
        id,
        orgId,
      });
      const statusKey = makeAttachmentStatusKey(attachmentKey);

      incrementBytes(args.data.byteLength);

      uploadPromises.push(
        uploadAttachment({
          objectStore,
          attachmentBucketName,
          attachmentKey,
          statusKey,
          contentType: args.contentType,
          data: args.data,
        }),
      );
      return {
        type: "braintrust_attachment",
        key: id,
        filename: args.filename,
        content_type: args.contentType,
      };
    },
    attachments,
  });
}

async function uploadAttachment({
  objectStore,
  attachmentBucketName,
  attachmentKey,
  statusKey,
  contentType,
  data,
}: {
  objectStore: ObjectStore;
  attachmentBucketName: string;
  attachmentKey: string;
  statusKey: string;
  contentType: string;
  data: ArrayBuffer;
}) {
  await objectStore.put({
    bucket: attachmentBucketName,
    key: statusKey,
    contentType: "application/json",
    body: JSON.stringify(defaultAttachmentStatus),
    allowOverwrite: false,
  });

  await objectStore.put({
    bucket: attachmentBucketName,
    key: attachmentKey,
    contentType,
    body: new Uint8Array(data),
    allowOverwrite: false,
  });

  await objectStore.put({
    bucket: attachmentBucketName,
    key: statusKey,
    contentType: "application/json",
    body: JSON.stringify(doneAttachmentStatus),
    allowOverwrite: true,
  });
}
