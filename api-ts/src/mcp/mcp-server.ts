import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { z } from "zod";
import express, { Request, Response } from "express";
import { customFetch } from "../custom_fetch";
import { _urljoin } from "braintrust/util";
import {
  objectTypeSchema,
  ObjectType,
  singleObjectTypeSchema,
} from "../schema";
import {
  ALLOWED_ORIGIN,
  MCP_SERVER_URL,
  PROD_ALLOWED_ORIGIN,
  PUBLIC_ALLOWED_ORIGIN,
} from "../env";
import {
  AccessDeniedError,
  BadRequestError,
  InternalServerError,
  objectTypeToAclObjectType,
} from "../util";
import { OBJECT_CACHE } from "../object_cache";
import { getLogger } from "../instrumentation/logger";
import { aclObjectTypeEnum } from "@braintrust/typespecs";
import {
  btqlQueryToolParams,
  InferSchemaTool,
} from "@braintrust/local/optimization/tools";
import { sql, join, snippet, doubleQuote } from "@braintrust/btql/planner";
import { BtqlRequest, runBtql } from "../btql";
import { runExperimentSummarizeRequestInternal } from "../summary";
import { Parser } from "@braintrust/btql/parser";

const DEFAULT_EXPIRATION_SECONDS = 3600; // 1 hour

export class MCPServerWithOAuth {
  private server: McpServer;

  constructor() {
    this.server = new McpServer(
      {
        name: "braintrust-mcp-server",
        version: "1.0.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    this.setupTools();
  }

  private async resolveObjectByName(
    object_type: ObjectType,
    project_name: string | undefined,
    object_name: string | undefined,
    accessToken: string,
  ): Promise<string> {
    const { aclObjectType, overrideRestrictObjectType } =
      objectTypeToAclObjectType(object_type);
    const parentObject =
      object_type === "project_logs" ? "project" : object_type;

    const response = await customFetch(
      _urljoin(controlPlaneUrl(), "api", parentObject, "get"),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          name: object_type === "project_logs" ? project_name : object_name,
          project_name:
            object_type === "project_logs" ? undefined : project_name,
        }),
      },
    );

    if (!response.ok) {
      getLogger().error(
        {
          status: response.status,
          statusText: response.statusText,
        },
        `Error looking up object [${response.status}]: ${await response.text()}`,
      );
      throw new InternalServerError("Error looking up object");
    }

    const matches = z
      .array(z.object({ id: z.string() }))
      .parse(await response.json());
    if (matches.length === 0) {
      throw new AccessDeniedError({
        permission: "read",
        aclObjectType,
        overrideRestrictObjectType,
      });
    }

    return matches[0].id;
  }

  private getAccessTokenFromContext(context: unknown): string {
    // Extract access token from MCP request context
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any -- MCP context typing
    const ctx = context as any;

    // Get authorization header from request info
    const authHeader = ctx?.requestInfo?.headers?.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      throw new Error("No valid authorization header found in request");
    }

    return authHeader.substring(7); // Remove "Bearer " prefix
  }

  private setupMCPCors(req: Request, res: Response, next: () => void): void {
    // Allow CORS from any origin for MCP endpoints since clients can come from anywhere
    res.header("Access-Control-Allow-Origin", "*");
    res.header(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, mcp-protocol-version",
    );
    res.header("Access-Control-Allow-Credentials", "false"); // Don't allow credentials for security

    // Handle preflight requests
    if (req.method === "OPTIONS") {
      res.sendStatus(200);
      return;
    }

    next();
  }

  private getBaseUrlFromRequest(req: Request): string {
    // Use configured URL if available, otherwise discover from request
    if (MCP_SERVER_URL) {
      return MCP_SERVER_URL;
    }

    // Extract from request headers
    const protocol = req.get("X-Forwarded-Proto") || req.protocol || "https";

    // Check for custom CloudFront domain header first
    const host =
      req.get("X-CloudFront-Domain") ||
      req.get("X-Forwarded-Host") ||
      req.get("Host") ||
      req.hostname ||
      "<CANNOT FIND A HOSTNAME. PLEASE SET MCP_SERVER_URL in env>";
    return `${protocol}://${host}`;
  }

  public setupExpressRoutes(app: express.Application): void {
    // Add CORS middleware for all MCP-related endpoints
    app.use("/oauth/*", (req, res, next) => this.setupMCPCors(req, res, next));
    app.use("/mcp/*", (req, res, next) => this.setupMCPCors(req, res, next));
    app.use("/sse", (req, res, next) => this.setupMCPCors(req, res, next));
    app.use("/.well-known/*", (req, res, next) =>
      this.setupMCPCors(req, res, next),
    );

    // Standard OAuth2/OpenID Connect discovery endpoints
    app.get(
      "/.well-known/openid-configuration",
      async (req: Request, res: Response) => {
        const baseUrl = this.getBaseUrlFromRequest(req);
        res.json({
          issuer: baseUrl,
          authorization_endpoint: _urljoin(baseUrl, "oauth/authorize"),
          token_endpoint: _urljoin(baseUrl, "oauth/token"),
          response_types_supported: ["code"],
          grant_types_supported: ["authorization_code"],
          scopes_supported: ["mcp"],
          token_endpoint_auth_methods_supported: [
            "client_secret_post",
            "client_secret_basic",
            "none",
          ],
          code_challenge_methods_supported: ["S256", "plain"],
          registration_endpoint: _urljoin(baseUrl, "oauth/register"),
        });
      },
    );

    app.get(
      "/.well-known/oauth-authorization-server",
      async (req: Request, res: Response) => {
        const baseUrl = this.getBaseUrlFromRequest(req);
        res.json({
          issuer: baseUrl,
          authorization_endpoint: _urljoin(baseUrl, "oauth/authorize"),
          token_endpoint: _urljoin(baseUrl, "oauth/token"),
          response_types_supported: ["code"],
          grant_types_supported: ["authorization_code"],
          scopes_supported: ["mcp"],
          token_endpoint_auth_methods_supported: [
            "client_secret_post",
            "client_secret_basic",
            "none",
          ],
          code_challenge_methods_supported: ["S256", "plain"],
          registration_endpoint: _urljoin(baseUrl, "oauth/register"),
        });
      },
    );

    // Dynamic Client Registration endpoint
    app.post(
      "/oauth/register",
      express.json(),
      async (req: Request, res: Response) => {
        try {
          const {
            redirect_uris,
            grant_types = ["authorization_code"],
            response_types = ["code"],
            token_endpoint_auth_method = "none",
            client_name: _client_name,
            client_uri: _client_uri,
            scope: _scope = "mcp",
          } = req.body;

          // Generate client ID and optional secret
          const clientId = `mcp_${Date.now()}_${Math.random().toString(36).substring(2)}`;
          const clientSecret =
            token_endpoint_auth_method === "none"
              ? undefined
              : `secret_${Date.now()}_${Math.random().toString(36).substring(2)}`;

          // Return client registration response
          const response: Record<string, unknown> = {
            client_id: clientId,
            redirect_uris: redirect_uris || [],
            grant_types,
            response_types,
            token_endpoint_auth_method,
            client_id_issued_at: Math.floor(Date.now() / 1000),
          };

          // Only include client_secret if using secret-based auth
          if (clientSecret) {
            response.client_secret = clientSecret;
            response.client_secret_expires_at =
              Date.now() / 1000 + DEFAULT_EXPIRATION_SECONDS;
          }

          res.json(response);
        } catch {
          res.status(400).json({
            error: "invalid_client_metadata",
            error_description: "Failed to register client",
          });
        }
      },
    );

    // OAuth Protected Resource Discovery (for MCP Inspector)
    app.get(
      "/.well-known/oauth-protected-resource",
      async (req: Request, res: Response) => {
        const baseUrl = this.getBaseUrlFromRequest(req);
        res.json({
          resource: baseUrl,
          authorization_endpoint: _urljoin(baseUrl, "oauth/authorize"),
          token_endpoint: _urljoin(baseUrl, "oauth/token"),
          sse_endpoint: _urljoin(baseUrl, "sse"),
          tools_endpoint: _urljoin(baseUrl, "mcp/tools"),
          supported_auth_methods: ["authorization_code"],
          client_id: "braintrust-mcp",
        });
      },
    );

    // MCP HTTP transport endpoint
    app.get(
      "/.well-known/oauth-protected-resource/sse",
      async (req: Request, res: Response) => {
        const baseUrl = this.getBaseUrlFromRequest(req);
        res.json({
          http_endpoint: _urljoin(baseUrl, "mcp"),
          authorization_endpoint: _urljoin(baseUrl, "oauth/authorize"),
          token_endpoint: _urljoin(baseUrl, "oauth/token"),
          client_id: "braintrust-mcp",
        });
      },
    );

    app.get("/oauth/authorize", async (req: Request, res: Response) => {
      try {
        if (!req.query.client_id || !req.query.redirect_uri) {
          return res.status(400).json({
            error: "invalid_request",
            error_description:
              "Missing required parameters: client_id and redirect_uri",
          });
        }

        // Build parameters to pass to MCP redirect page
        const mcpParams = new URLSearchParams(
          Object.fromEntries(
            Object.entries(req.query).map(([key, value]) => [
              key,
              Array.isArray(value)
                ? value.join(",")
                : typeof value === "string"
                  ? value
                  : String(value),
            ]),
          ),
        );

        const mcpRedirectUrl = _urljoin(
          PUBLIC_ALLOWED_ORIGIN ?? ALLOWED_ORIGIN ?? PROD_ALLOWED_ORIGIN,
          `redirects/mcp?${mcpParams.toString()}`,
        );
        res.redirect(mcpRedirectUrl);
      } catch {
        res.status(500).json({ error: "Authorization failed" });
      }
    });

    app.get("/oauth/token", (_req: Request, res: Response) => {
      res.json({
        error: "invalid_request",
        error_description:
          "Token endpoint requires POST method with authorization code",
        hint: "This endpoint should be called programmatically by the OAuth client, not visited directly",
      });
    });

    // OAuth token endpoint - exchange authorization code for Braintrust access token
    app.post(
      "/oauth/token",
      express.json(),
      express.urlencoded({ extended: true }),
      async (req: Request, res: Response) => {
        try {
          const { grant_type, code, client_id, code_verifier } = req.body;

          // Validate required parameters
          if (grant_type !== "authorization_code") {
            return res.status(400).json({
              error: "unsupported_grant_type",
              error_description:
                "Only authorization_code grant type is supported",
            });
          }

          if (!code) {
            return res.status(400).json({
              error: "invalid_request",
              error_description: "Missing required parameter: code",
            });
          }

          if (!client_id) {
            return res.status(400).json({
              error: "invalid_client",
              error_description: "Missing required parameter: client_id",
            });
          }

          let tokenResponse;

          try {
            // Exchange authorization code for access token via app server
            const exchangeResponse = await customFetch(
              _urljoin(controlPlaneUrl(), "api/mcp/exchange-auth-code"),
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  code: code,
                  client_id: client_id,
                  code_verifier: code_verifier,
                }),
              },
            );

            if (!exchangeResponse.ok) {
              const errorData = await exchangeResponse.json();
              return res.status(400).json({
                error: "invalid_grant",
                error_description:
                  errorData.message || "Invalid or expired authorization code",
              });
            }

            const exchangeResult = await exchangeResponse.json();

            // Return the access token (no server-side storage)
            tokenResponse = {
              access_token: exchangeResult.access_token,
              token_type: "Bearer",
              expires_in: Math.floor(
                exchangeResult.expires_in || DEFAULT_EXPIRATION_SECONDS,
              ),
              scope: "mcp",
            };
          } catch {
            return res.status(400).json({
              error: "invalid_grant",
              error_description: "Could not exchange authorization code",
            });
          }

          res.json(tokenResponse);
        } catch {
          res.status(400).json({
            error: "server_error",
            error_description: "Token exchange failed",
          });
        }
      },
    );

    // MCP HTTP endpoint OPTIONS handler
    app.options("/mcp", (_req: Request, res: Response) => {
      res.header("Access-Control-Allow-Origin", "*");
      res.header("Access-Control-Allow-Methods", "POST, OPTIONS");
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, mcp-protocol-version",
      );
      res.sendStatus(200);
    });

    // MCP HTTP endpoint using StreamableHTTP transport with authentication
    app.post("/mcp", express.json(), async (req: Request, res: Response) => {
      try {
        const authHeader = req.headers.authorization;

        // Extract access token if provided
        let accessToken: string | undefined;
        if (authHeader && authHeader.startsWith("Bearer ")) {
          accessToken = authHeader.substring(7);
        }

        // Require authentication for all MCP requests except 'initialize'
        if (!accessToken) {
          return res.status(401).json({
            jsonrpc: "2.0",
            error: {
              code: -32001,
              message:
                "Authentication required. Include 'Authorization: Bearer <token>' header.",
              data: `All MCP methods require OAuth authentication. Complete OAuth flow first.`,
            },
            id: req.body?.id || null,
          });
        }

        // TODO: Token validation will be handled by existing auth mechanisms
        const transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: undefined, // Stateless
        });

        await this.server.connect(transport);
        await transport.handleRequest(req, res, req.body);

        // This is similar to surrounding this in a try ... finally
        res.on("close", () => {
          void transport.close();
        });
      } catch {
        if (!res.headersSent) {
          res.status(500).json({
            jsonrpc: "2.0",
            error: {
              code: -32603,
              message: "Internal server error",
            },
            id: null,
          });
        }
      }
    });

    // Handle GET requests to MCP endpoint (method not allowed)
    app.get("/mcp", async (_req: Request, res: Response) => {
      res.writeHead(405).end(
        JSON.stringify({
          jsonrpc: "2.0",
          error: {
            code: -32000,
            message: "Method not allowed.",
          },
          id: null,
        }),
      );
    });
  }

  public async startStdioTransport(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log("MCP server started with stdio transport");
  }

  private setupTools(): void {
    this.server.tool(
      "search_docs",
      `Search Braintrust documentation to find relevant information and guidance.

This tool searches through the official Braintrust documentation using semantic search to find the most relevant content for your query.

Use this tool to:
- Learn more about what experiments, datasets, prompts, playgrounds, and logs are, and how to use them in the API or SDK.
- Understand how to write evals and create scorers
- Find sample code and guidance on how to add tracing for custom code as well as popular frameworks like the Vercel AI SDK.
- Find code examples that are relevant to your use cases.

What it returns:
- Multiple documentation snippets ranked by relevance
- Each snippet includes the content, headers hierarchy, and path
- Content is Markdown formatted

Example queries:
- "How do I create an experiment?"
- "BTQL query syntax"
- "Setting up scorers"
- "Using metadata fields"
- "Prompt playground features"`,
      {
        query: z
          .string()
          .describe(
            `A search string to find relevant documentation. The string will be embedded and then used to search for relevant snippets in a vector database.`,
          ),
        top_k: z
          .number()
          .int()
          .positive()
          .optional()
          .describe(
            "The number of top relevant document snippets to return. Higher values return more results but may include less relevant snippets.",
          ),
      },
      async ({ query, top_k }, context) => {
        const accessToken = this.getAccessTokenFromContext(context);

        const result = await customFetch(
          _urljoin(controlPlaneUrl(), "api/docs/search"),
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({
              query,
              topK: top_k,
            }),
          },
        );

        const docsToolResultSchema = z.array(
          z.object({
            id: z
              .string()
              .describe(
                "The unique identifier of the document snippet (a path into the docs)",
              ),
            headers: z
              .array(z.string())
              .describe("The header path of the document snippet"),
            content: z
              .string()
              .describe(
                "The content of the document snippet, which is a markdown string",
              ),
            path: z
              .string()
              .describe(
                "The relative path to the document for constructing a URL",
              ),
          }),
        );

        if (!result.ok) {
          const errorText = await result.text();
          throw new Error(
            `Error searching docs: ${result.status} ${errorText}`,
          );
        }

        const resultJson = await result.json();
        const parsed = docsToolResultSchema.parse(resultJson);

        const formatted =
          parsed
            .map((doc) => {
              const headerPath = doc.headers.length
                ? doc.headers.join(" > ")
                : "No headers";
              return `### ${headerPath}\n\n${doc.content}\n\n[Read more](${_urljoin(PUBLIC_ALLOWED_ORIGIN ?? controlPlaneUrl(), doc.path)})\n`;
            })
            .join("\n---\n\n") || "No results found.";

        return {
          content: [
            {
              type: "text",
              text: formatted,
            },
          ],
        };
      },
    );

    this.server.tool(
      "resolve_object",
      `Convert project names, dataset names, experiment IDs, run IDs, and other entities into their ids, or vice versa. Provide the information you have, and this tool will look up the rest.`,
      {
        object_type: objectTypeSchema.describe(
          "The type of the object. If you specify project_logs, then you do not need to specify an object_name, as the tool will return the logs for the specified project. If you specify a project_name with object_type='logs', the object_id will be the project's id.",
        ),
        project_name: z
          .string()
          .optional()
          .describe(
            "The name of the project. If specified together with an object_name, the resolver will look for an object with that name within the specified project.",
          ),
        object_name: z
          .string()
          .optional()
          .describe(
            "The name of the object. Specify this together with a project_name to look for an object within a specific project and return its id.",
          ),
        object_id: z
          .string()
          .optional()
          .describe(
            "The id of the object. If you specify this, you do not need to specify a project_name or object_name.",
          ),
      },
      async (
        { object_type, project_name, object_name, object_id },
        context,
      ) => {
        const accessToken = this.getAccessTokenFromContext(context);

        const { aclObjectType, overrideRestrictObjectType } =
          objectTypeToAclObjectType(object_type);
        if (object_id && object_name) {
          throw new BadRequestError(
            "Cannot specify both object_id and object_name",
          );
        } else if (object_id) {
          const entry = await OBJECT_CACHE.checkAndGet({
            appOrigin: controlPlaneUrl(),
            authToken: accessToken,
            aclObjectType,
            overrideRestrictObjectType,
            objectId: object_id,
          });
          if (!entry) {
            throw new AccessDeniedError({
              permission: "read",
              aclObjectType,
              overrideRestrictObjectType,
              objectId: object_id,
            });
          }
          if (object_type === "project_logs") {
            project_name = entry.object_name;
          } else {
            object_name = entry.object_name;
            project_name = entry.parent_cols.get("project")?.name;
          }
        } else {
          object_id = await this.resolveObjectByName(
            object_type,
            project_name,
            object_name,
            accessToken,
          );
        }

        const permalink = `${controlPlaneUrl()}/app/object?object_type=${object_type}&object_id=${object_id}`;

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(
                {
                  object_type,
                  project_name,
                  object_name,
                  object_id,
                  permalink,
                },
                null,
                2,
              ),
            },
          ],
        };
      },
    );

    this.server.tool(
      "list_recent_objects",
      `List recently created projects, experiments, datasets, prompts, or functions you have access to. You can use this to browse for objects
and as a crude way of searching for objects (you will need to filter the results yourself).

If an object you expect does not show up, then use the \`startin_after\` parameter to paginate through more results.

If a user asks for something like "list my recent experiments", then you do not need to list through the set of projects. You can just provide
the project name, and then list the experiments within that project. If you get an error, then perhaps the project name is incorrect, and you
should fall back to listing projects first to find the one the user is referring to.`,
      {
        object_type: aclObjectTypeEnum.describe("The type of the object."),
        project_name: z
          .string()
          .optional()
          .describe(
            "Experiments, datasets, prompts, functions, prompt sessions live in projects, so you must specify a project_name to list those object types.",
          ),
        limit: z
          .number()
          .int()
          .positive()
          .max(100)
          .optional()
          .default(10)
          .describe(
            "The maximum number of objects to return. Defaults to 10 if not specified.",
          ),
        starting_after: z
          .string()
          .optional()
          .describe(
            "For pagination, provide the last id from the previous result set to get the next page.",
          ),
      },
      async ({ object_type, project_name, limit, starting_after }, context) => {
        const accessToken = this.getAccessTokenFromContext(context);

        const response = await customFetch(
          _urljoin(controlPlaneUrl(), "api", object_type, "get"),
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({
              project_name,
              limit,
              starting_after,
            }),
          },
        );

        if (!response.ok) {
          throw new Error(`[${response.status}] ${await response.text()}`);
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(await response.json(), null, 2),
            },
          ],
        };
      },
    );

    this.server.tool(
      "infer_schema",
      InferSchemaTool.description,
      {
        object_type: singleObjectTypeSchema,
        object_ids: z
          .string()
          .array()
          .describe(
            "One or more object ids to analyze the schema for. For example, if you need to understand the schema of a specific experiment, just provide its object id.",
          ),
        include_sample_values: z
          .boolean()
          .optional()
          .default(false)
          .describe(
            "Whether to include sample values for each field in the schema. This can help illustrate the type of data stored in each field.",
          ),
        limit: z
          .number()
          .int()
          .positive()
          .optional()
          .default(1000)
          .describe(
            "The maximum number of fields to return (defaults to 1000).",
          ),
        fields: z
          .enum([
            "input",
            "output",
            "expected",
            "metadata",
            "scores",
            "metrics",
          ])
          .array()
          .optional()
          .describe(
            "If specified, only infer schema for these top-level fields. Otherwise, infer schema for all fields.",
          ),
      },

      async (
        { object_type, object_ids, include_sample_values, limit, fields },
        context,
      ) => {
        const fromClause = makeFromClause({
          object_type,
          object_ids,
          shape: "spans",
        });

        const projection =
          fields && fields.length > 0 ? fields.join(", ") : "*";

        const query = `infer: ${projection} | ${fromClause} | limit: ${limit}`;

        const result = await runBtqlQuery({
          accessToken: this.getAccessTokenFromContext(context),
          query,
        });

        if ("explain" in result) {
          throw new Error(
            `infer_schema tool cannot use explain mode: ${JSON.stringify(
              result.explain,
            )}`,
          );
        }

        const results = z
          .object({
            name: z.string().array(),
            type: z.unknown(),
            top_values: z
              .array(z.object({ count: z.number(), value: z.unknown() }))
              .optional(),
          })
          .array()
          .parse(result.rows);

        const formatted = results
          .map((row) => {
            const base = `${escapeIdentPath(row.name)}: ${safeSerializeJSON(row.type)}`;
            if (include_sample_values && row.top_values) {
              const samples = row.top_values
                .map((tv) => `  - ${safeSerializeJSON(tv.value)} (${tv.count})`)
                .join("\n");
              return `${base}\nTop values:\n${samples}\n`;
            } else {
              return base;
            }
          })
          .join("\n");

        return {
          content: [
            {
              type: "text",
              text: formatted || "No fields found.",
            },
          ],
        };
      },
    );

    /*
Excluding playgrounds and prompts for now:


**prompt_session**: Interactive playground sessions where you test prompts manually
- Created when using the Braintrust playground UI to test prompts interactively
- Contains manual prompt testing, not automated evaluations
- Query: from: prompt_session(session_id_1, session_id_2)
- Use for: Analyzing manual testing patterns, prompt iteration history

**playground_logs**: Logs from playground experimentation
- Similar to prompt_session but may contain different types of playground activities
- Query: from: playground_logs(log_id_1, log_id_2)
- Use for: Understanding playground usage patterns, manual testing analysis

**project_prompts**: Prompt templates stored in a project
- Reusable prompt templates that can be versioned and shared
- Not logs themselves, but metadata about prompt templates
- Query: from: project_prompts(project_id)
- Use for: Analyzing prompt template usage, version comparison

**project_functions**: Function definitions stored in a project
- Custom function definitions used in evaluations or applications
- Not logs themselves, but metadata about functions
- Query: from: project_functions(project_id)
- Use for: Understanding function usage, analyzing custom scoring functions
*/

    this.server.tool(
      "btql_query",
      `Execute BTQL (Braintrust Query Language) queries to analyze logs, experiments, and traces.

BTQL is a SQL-like language for querying AI application data. Key capabilities:

OBJECT TYPES IN BRAINTRUST:

**experiment**: Contains logs from running evaluations (Eval() function or braintrust eval CLI)
- Created when you run evaluations comparing different models, prompts, or configurations
- Contains test cases with inputs, outputs, expected results, and scores
- Each experiment belongs to a project and has multiple spans (one per test case)
- Query multiple experiments: from: experiment(exp_id_1, exp_id_2, exp_id_3)
- Use for: Comparing model performance, analyzing evaluation results, finding best configurations

**dataset**: Contains reference data used for evaluations
- Static collections of input/expected output pairs used as test cases
- Stored separately from experiments but referenced during evaluations
- Query multiple datasets: from: dataset(dataset_id_1, dataset_id_2)
- Use for: Understanding your test data, analyzing dataset quality, finding common patterns

**project_logs**: Direct logging to a project (NOT cumulative of all experiments)
- These are logs written directly to a project using log() or trace() functions
- Separate from experiment logs - this is production/development logging
- Contains real application usage data, not evaluation data
- Query: from: project_logs(project_id) - note: use project_id, not individual log IDs
- Use for: Production monitoring, debugging real user interactions, performance analysis

QUERYING MULTIPLE OBJECTS:
- Multiple experiments: from: experiment(id1, id2, id3)
- Multiple datasets: from: dataset(dataset_id1, dataset_id2, dataset_id3)
- Multiple projects: from: project_logs(proj_id1, proj_id2)
- Cross-project experiments: from: experiment(exp_from_proj1, exp_from_proj2, exp_from_proj3)
- Mixed types: NOT SUPPORTED - each query can only target one object type
- You can always combine objects of the same type in a single query

CLAUSES:
- select: Choose fields (use * for all fields)
- filter: Apply conditions (supports =, !=, >, <, >=, <=, IS NULL, LIKE, MATCH, CONTAINS)
- sort: Order results (field asc/desc)
- limit: Limit results (default 100)
- dimensions/measures: For aggregations (count, sum, avg, min, max, percentile)
- unpivot: Expand arrays into individual rows (e.g., unpivot: tags as tag)

FIELDS BY SHAPE:

For 'spans' and 'traces':
- id: Unique span identifier
- span_id: Current span ID
- root_span_id: Root span ID for the trace
- created: Timestamp when created
- input/output: The prompts and responses
- expected: Expected output (if available)
- error: Error information (if any)
- metadata.* : Nested fields MUST use dot notation (e.g., metadata.user_id, metadata.model, metadata.session_id)
- scores.* : Nested score fields (e.g., scores.Factuality, scores.Coherence, scores.Helpfulness)
- metrics.* : Nested metrics (e.g., metrics.tokens, metrics.latency, metrics.cost)
- tags: Array of tags applied to the span
- span_attributes.name/type/purpose: Span classification (use dot notation: span_attributes.name)
- origin: Information about where the span was created

For 'summary' (aggregated per trace):
- id: Root span ID (trace identifier)
- comparison_key: Key for comparing traces
- created: Timestamp of the trace
- input/output/expected/error/metadata: From root span only (truncated to preview_length)
- tags/origin: Full arrays from root span
- scores.*: Averaged across all spans in the trace
- metrics.duration/llm_duration: Timing metrics
- metrics.prompt_tokens/completion_tokens/total_tokens: Summed across trace
- metrics.estimated_cost: Total estimated cost
- span_type_info: Summary of span types in the trace

OPERATORS:
- Text: MATCH (semantic search for finding content), = (standard equality)
- Arrays: CONTAINS (check if array contains value, e.g., tags CONTAINS 'production')
- Logic: AND, OR, NOT
- Time: created > now() - interval 1 day
- IS NULL, IS NOT NULL for checking null values
- =, != for exact matches
- >, <, >=, <= for numeric comparisons

FUNCTIONS:
- Time: day(), hour(), month(), year(), now()
- Aggregates: count(), sum(), avg(), min(), max(), percentile()
- String: lower(), upper(), concat()
- Array: len() (array length), CONTAINS (check if value in array)
- Conditional: Use ternary operator (a ? b : c) for conditional logic, NOT CASE WHEN

FIELD ACCESS RULES:
- ALWAYS use dot notation for nested fields
- If InferSchemaTool shows ['metadata', 'user_id'], write it as: metadata.user_id
- If InferSchemaTool shows ['scores', 'Factuality'], write it as: scores.Factuality
- NEVER use just 'user_id' or 'Factuality' - always include the parent object
- NO WILDCARD SUBFIELDS: To get all fields from an object, project the object itself
  - CORRECT: select: metadata, metrics (gets all metadata fields and all metrics fields)
  - WRONG: select: metadata.*, metrics.* (this syntax does not exist)
- If you use the summary shape, you can only \`select: *\`.

SORT: NEVER add sort clauses when asked for "most recent", "latest", "newest", or "last" items
- Results are automatically sorted by timestamp in descending order (newest first)
- Just use limit to get the desired number of recent items
- Example: "find the most recent error" → "filter: error IS NOT NULL | select: * | limit: 1"
- Example: "show the latest 5 failures" → "filter: error IS NOT NULL | select: * | limit: 5"

LITERAL VALUES:
- Strings: to escape quotes, use backslash (e.g., 'This is a string with a quote: \\'')

WORKING WITH ARRAYS (e.g., tags):
- Filter by exact tag: filter: tags CONTAINS 'production'
- Multiple tags: filter: tags CONTAINS 'production' AND tags CONTAINS 'high_priority'
- Non-empty array: filter: tags IS NOT NULL AND len(tags) > 0
- Expand array for grouping: unpivot: tags as tag | dimensions: tag | measures: count(1)

IMPORTANT ARRAY LIMITATIONS:
- You CANNOT use LIKE with arrays: tags LIKE 'environment:%' is INVALID
- You CANNOT filter on unpivoted columns: after unpivot: tags as tag, you cannot use tag in filters
- To find tags matching a pattern, you must either:
  1. Filter by exact values: filter: tags CONTAINS 'environment:production'
  2. Or unpivot and group all tags, then look for patterns in the results

SPANS AND TRACES:
- When you are querying with the 'spans' shape (the default) or 'traces' shape, you will receive one
  row per span. That means you should be prepared to handle data from subspans, or filter them out.
- To only consider root spans, you can add the \`is_root\` expression to your query. This is useful
  when you are grouping by an attribute in metadata, for example, that is only present on the root span.
- If you see a lot of null values while grouping, you are probably grouping by a field that is not present
  on all spans. Consider using the \`is_root\` expression to only consider root spans, or an "IS NOT NULL"
  filter on your grouping field.

EXAMPLES:
1. Find recent errors: filter: error IS NOT NULL AND created > now() - interval 1 day
2. Group by model: dimensions: metadata.model | measures: count(1) as calls, avg(scores.Factuality) as avg_score
3. Filter by user: filter: metadata.user_id = '3e29ac38-5588-412a-a91c-7498c2f7277e' (NOT just user_id = ...)
4. User activity: dimensions: metadata.user_id | measures: count(1) as activity | sort: activity desc
5. Score filtering: filter: scores.Factuality > 0.8 AND scores.Helpfulness > 0.7
6. Conditional grouping: dimensions: error as has_error | measures: count(1) as count
7. Calculate error rate: measures: count(error) as errors, count(1) as total
8. Most popular tag: unpivot: tags as tag | dimensions: tag | measures: count(1) as usage | sort: usage desc | limit: 1
9. Tag percentage: measures: count(tags CONTAINS 'experimental' ? 1 : null) as exp_count, count(1) as total
10. All tag distribution: unpivot: tags as tag | dimensions: tag | measures: count(1) as count (then filter results for patterns)
11. Specific environment: filter: tags CONTAINS 'environment:production' (must know exact value, not pattern)
12. Compare multiple experiments: from: experiment(exp1_id, exp2_id) | dimensions: experiment_id | measures: avg(scores.Accuracy)
13. Production logs analysis: from: project_logs(project_id) | filter: created > now() - interval 7 days
14. Cross-dataset comparison: from: dataset(dataset1_id, dataset2_id) | dimensions: dataset_id | measures: count(1) as records`,
      btqlQueryToolParams.omit({ visualizationConfig: true }).extend({
        object_type: singleObjectTypeSchema,
        object_ids: z
          .string()
          .array()
          .describe(
            "One or more object ids to query. For example, if you need to get results from a specific experiment, just provide its object id (and do not filter by it). If you want to look at results across multiple experiments, provide multiple object ids.",
          ),
        preview_length: z
          .number()
          .int()
          .optional()
          .default(1024)
          .describe(
            "The maximum number of characters to include in each field, before truncating it with an ellipsis. This helps keep the output concise. If not specified, the default is 1024. You can set it to -1 to get full field contents, but beware, it might use a lot of tokens.",
          ),
      }).shape,
      async (
        { query, shape, object_type, object_ids, preview_length },
        context,
      ) => {
        const fromClause = makeFromClause({
          object_type,
          object_ids,
          shape,
        });

        const fullQuery =
          query +
          " | " +
          fromClause +
          (shape === "summary" ? ` | preview_length: ${preview_length}` : "");

        const result = await runBtqlQuery({
          accessToken: this.getAccessTokenFromContext(context),
          query: fullQuery,
        });

        const data =
          "explain" in result
            ? result.explain
            : {
                schema: result.resultSchema,
                data:
                  shape === "summary"
                    ? result.rows
                    : trimPreview(result.rows, preview_length),
                cursor: result.cursor,
              };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(data, null, 2),
            },
          ],
        };
      },
    );

    this.server.tool(
      "summarize_experiment",
      `An experiment is an artifact that is generated when you run an \`Eval()\` in Braintrust. You can generate one by running:
* \`braintrust eval ...\` from the command line
* the \`Eval()\` function from code
* creating an experiment via the \`init_experiment()\` (fka \`init()\`) function and then logging to it.
* through the UI or API by running an eval through the API

An experiment can be summarized, optionally with respect to another experiment, to get a summary of its performance on the scorers
that were used as well as a number of metrics including token counts, cost, and latency.

If you want more detailed, case-by-case information, use the \`btql_query\` tool to query the experiment's data directly. The \`summary\`
shape is a good starting point, because it returns one summarized row per test case. You can also use the \`spans\` shape to get one row
per span, which is useful for looking at individual LLM or tool calls.`,
      {
        experiment_id: z
          .string()
          .describe("The id of the experiment to summarize."),
        comparison_experiment_id: z
          .string()
          .optional()
          .describe(
            "Optional: the id of an experiment to compare against. If provided, the summary will include comparisons to this baseline.",
          ),
      },
      async ({ experiment_id, comparison_experiment_id }, context) => {
        const accessToken = this.getAccessTokenFromContext(context);

        const result = await runExperimentSummarizeRequestInternal({
          body: {
            experiment_id,
            base_experiment_id: comparison_experiment_id,
          },
          appOrigin: controlPlaneUrl(),
          authToken: accessToken,
        });

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      },
    );

    this.server.tool(
      "generate_permalink",
      `Generate a direct web link (permalink) to a Braintrust object for sharing or bookmarking.

This tool creates URLs that link directly to projects, experiments, datasets, prompts, functions, or specific log entries in the Braintrust web application. Use this when users want to:
- Share a link to an experiment or dataset with teammates
- Create bookmarks to specific objects
- Generate URLs for external documentation or reports
- Navigate directly to specific log entries or traces

The tool accepts either object_id directly, or project_name + object_name to resolve the object first.`,
      {
        object_type: singleObjectTypeSchema.describe(
          "The type of object to create a permalink for",
        ),
        project_name: z
          .string()
          .optional()
          .describe(
            "The project name (required if using object_name instead of object_id)",
          ),
        object_name: z
          .string()
          .optional()
          .describe(
            "The object name within the project (alternative to object_id)",
          ),
        object_id: z
          .string()
          .optional()
          .describe(
            "The UUID of the object (alternative to project_name + object_name)",
          ),
        row_id: z
          .string()
          .optional()
          .describe(
            "Optional: specific row/trace ID within the object to link directly to a log entry",
          ),
      },
      async (
        { object_type, project_name, object_name, object_id, row_id },
        context,
      ) => {
        const accessToken = this.getAccessTokenFromContext(context);

        // If no object_id provided, resolve it first
        if (!object_id) {
          if (!project_name || !object_name) {
            throw new BadRequestError(
              "Must provide either object_id, or both project_name and object_name",
            );
          }

          object_id = await this.resolveObjectByName(
            object_type,
            project_name,
            object_name,
            accessToken,
          );
        }

        // Generate permalink
        const baseUrl = controlPlaneUrl();
        let permalink = `${baseUrl}/app/object?object_type=${object_type}&object_id=${object_id}`;

        if (row_id) {
          permalink += `&id=${row_id}`;
        }

        return {
          content: [
            {
              type: "text",
              text: permalink,
            },
          ],
        };
      },
    );
  }
}

function controlPlaneUrl(): string {
  return ALLOWED_ORIGIN ?? "https://www.braintrust.dev";
}

function trimPreview(rows: Record<string, unknown>[], preview_length: number) {
  if (preview_length < 0) {
    return rows;
  }
  return rows.map((row) => {
    const newRow: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(row)) {
      const stringValue =
        typeof value === "string" ? value : JSON.stringify(value);

      if (stringValue.length <= preview_length) {
        newRow[key] = value;
      } else {
        const truncatedValue = stringValue.substring(0, preview_length) + "...";
        newRow[key] = truncatedValue;
      }
    }
    return newRow;
  });
}

function makeFromClause({
  object_type,
  object_ids,
  shape,
}: {
  object_type: ObjectType;
  object_ids: string[];
  shape: z.infer<typeof btqlQueryToolParams.shape.shape>;
}): string {
  return sql`from: ${snippet(object_type)}(${join(
    object_ids.map((id) => sql`${id}`),
    ", ",
  )}) ${shape ? snippet(shape) : sql``}`.toPlainStringQuery();
}

async function runBtqlQuery({
  accessToken,
  query,
}: {
  accessToken: string;
  query: string;
}) {
  const body: Omit<BtqlRequest, "fmt" | "api_version"> = {
    query,
    use_brainstore: true,
    query_timeout_seconds: 30,
    brainstore_realtime: true,
  };

  return await runBtql({
    body,
    appOrigin: controlPlaneUrl(),
    ctxToken: accessToken,
  });
}

function needsEscaping(component: string): boolean {
  if (component.includes(".")) {
    return true;
  }
  const parser = new Parser(component);
  try {
    const expr = parser.parseIdent({ allowInitialDoubleQuoted: true });
    return !(expr.op === "ident" && parser.finished());
  } catch {
    return true;
  }
}

function escapeIdentPath(path: (string | number)[]): string {
  let ret = "";
  for (const [i, p] of path.entries()) {
    if (typeof p === "string") {
      if (i > 0) {
        ret += ".";
      }
      ret += needsEscaping(p) ? doubleQuote(p) : p;
    } else {
      ret += `[${p}]`;
    }
  }
  return ret;
}

function safeSerializeJSON(value: unknown): string {
  return typeof value === "string" ? value : JSON.stringify(value ?? null);
}
