import dotenv from "dotenv";
import * as fs from "fs";
import path from "path";

for (const reldir of ["../app", "./api-ts", "."]) {
  for (const envfile of [".env", ".env.development", ".env.local"]) {
    const fullpath = path.join(reldir, envfile);
    if (fs.existsSync(fullpath)) {
      dotenv.config({ path: fullpath, override: true });
    }
  }
}

export let ORG_NAME = process.env.ORG_NAME;
export const PRIMARY_ORG_NAME = process.env.PRIMARY_ORG_NAME;
export const TESTING_ONLY_ALLOW_ORG_NAME_HEADER_OVERRIDE =
  process.env.TESTING_ONLY_ALLOW_ORG_NAME_HEADER_OVERRIDE;
// internal flag to identify our data plane
export const BRAINTRUST_HOSTED_DATA_PLANE = parseBooleanEnv(
  process.env.BRAINTRUST_HOSTED_DATA_PLANE,
);
export const DISABLE_SYSADMIN_TELEMETRY = parseBooleanEnv(
  process.env.DISABLE_SYSADMIN_TELEMETRY,
);

export const LOG_LEVEL = process.env.LOG_LEVEL;
export const GIT_COMMIT = process.env.GIT_COMMIT;
export const DEPLOYMENT_MODE = process.env.DEPLOYMENT_MODE;

export const ELASTICACHE_CLUSTER_ID = process.env.ELASTICACHE_CLUSTER_ID;
export let PROXY_URL = process.env.PROXY_URL;
export let REALTIME_URL = process.env.REALTIME_URL;
export let ALLOWED_ORIGIN = process.env.ALLOWED_ORIGIN;
export let PUBLIC_ALLOWED_ORIGIN = process.env.PUBLIC_ALLOWED_ORIGIN;

export let PG_URL = process.env.PG_URL;
export const PG_URL_NOMODIFY = process.env.PG_URL_NOMODIFY
  ? parseBooleanEnv(process.env.PG_URL_NOMODIFY)
  : false;
export const PG_LOGS_TABLE = process.env.PG_LOGS_TABLE || "logs";
export const PG_LOGS2_TABLE = process.env.PG_LOGS2_TABLE || "logs2";
export const PG_COMMENTS_TABLE = process.env.PG_COMMENTS_TABLE || "comments";
export const PG_ORG_PROMPT_LOG_ROWS_TABLE =
  process.env.PG_ORG_PROMPT_LOG_ROWS_TABLE || "org_prompt_log_rows";
export const MIGRATION_VERSION_TABLE =
  process.env.MIGRATION_VERSION_TABLE || "migration_version";
export const PG_POOL_CONFIG_MAX_NUM_CLIENTS = process.env
  .PG_POOL_CONFIG_MAX_NUM_CLIENTS
  ? parseInt(process.env.PG_POOL_CONFIG_MAX_NUM_CLIENTS)
  : undefined;
// NOTE: Enabling this requires setting up the `logs2` table in the database
// with pg_partman. This means the `INSERT_LOGS2` environment variable must be
// set to true when running `api-schema/lambda_function.py`, or invoking the
// migration database lambda function. We do this by default in the cloud-formation
// template and docker containers.
//
// If you don't run the migrations, then insert into logs2 should fail because
// the default migration only creates the "virtual" partition parent, but no
// actual partitions.
export const INSERT_LOGS2 = process.env.INSERT_LOGS2
  ? parseBooleanEnv(process.env.INSERT_LOGS2)
  : false;

export const REDIS_URL = process.env.REDIS_URL;
export let REDIS_HOST = process.env.REDIS_HOST;
export let REDIS_PORT = process.env.REDIS_PORT
  ? parseInt(process.env.REDIS_PORT)
  : undefined;

const LOCAL_PG_URL = "postgres://postgres:postgres@localhost:5532/postgres";
const LOCAL_REDIS_HOST = "localhost";
const LOCAL_REDIS_PORT = 6479;

export const PROD_ALLOWED_ORIGIN = "https://www.braintrust.dev";
const PROD_REALTIME_URL = "https://realtime.braintrustapi.com";
const PROD_PROXY_URL = "https://api.braintrust.dev/v1/proxy";

// It's actually ok for this to be undefined, since we only use it to override
// the origin if it has been explicitly specified.
export const BRAINTRUST_APP_URL = process.env.BRAINTRUST_APP_URL;

// This URL is used to inform clients about how to reach the API as an MCP server.
// If not set, the server will discover its own URL at runtime from the Host header.
export const MCP_SERVER_URL = process.env.MCP_SERVER_URL;

export const PROXY_ALLOW_PASSTHROUGH_CREDENTIALS = process.env
  .PROXY_ALLOW_PASSTHROUGH_CREDENTIALS
  ? parseBooleanEnv(process.env.PROXY_ALLOW_PASSTHROUGH_CREDENTIALS)
  : false;

export let RESPONSE_BUCKET_NAME = process.env.RESPONSE_BUCKET;
export const RESPONSE_BUCKET_PREFIX =
  process.env.RESPONSE_BUCKET_PREFIX ?? "responses/";
export let RESPONSE_BUCKET_ACCESS_KEY_ID =
  process.env.RESPONSE_BUCKET_ACCESS_KEY_ID;
export let RESPONSE_BUCKET_SECRET_ACCESS_KEY =
  process.env.RESPONSE_BUCKET_SECRET_ACCESS_KEY;
export const RESPONSE_BUCKET_SESSION_TOKEN =
  process.env.RESPONSE_BUCKET_SESSION_TOKEN;
export let RESPONSE_BUCKET_S3_ENDPOINT =
  process.env.RESPONSE_BUCKET_S3_ENDPOINT;
export let RESPONSE_BUCKET_REGION = process.env.RESPONSE_BUCKET_REGION;
export let IS_RESPONSE_BUCKET_LOCAL = false;
export const RESPONSE_BUCKET_OVERFLOW_THRESHOLD = process.env
  .RESPONSE_BUCKET_OVERFLOW_THRESHOLD
  ? parseInt(process.env.RESPONSE_BUCKET_OVERFLOW_THRESHOLD)
  : 4 * 1024 * 1024; // We believe it's 10 MB, but use a smaller number to be safe.

// Providing this will override the AZURE_STORAGE_CONNECTION_STRING and instead
// use "managed identity", which is the recommended way to access Azure Blob Storage.
export const AZURE_STORAGE_ACCOUNT_NAME =
  process.env.AZURE_STORAGE_ACCOUNT_NAME;

// Using a connection string with a shared key is not recommended beyond testing purposes.
export const AZURE_STORAGE_CONNECTION_STRING =
  process.env.AZURE_STORAGE_CONNECTION_STRING;

export const STRICT_VALIDATION_MODE = process.env.STRICT_VALIDATION_MODE
  ? Boolean(parseInt(process.env.STRICT_VALIDATION_MODE))
  : undefined;

const LOCAL_RESPONSE_BUCKET = "";
const LOCAL_RESPONSE_BUCKET_REGION = "us-east-1"; // Minio is hardcoded to this
const LOCAL_RESPONSE_BUCKET_ACCESS_KEY_ID = "minio_root_user";
const LOCAL_RESPONSE_BUCKET_SECRET_ACCESS_KEY = "minio_root_password";
const LOCAL_RESPONSE_BUCKET_S3_ENDPOINT = "http://localhost:10000";

export let CODE_BUNDLE_BUCKET_NAME = process.env.CODE_BUNDLE_BUCKET;
export const CODE_BUNDLE_BUCKET_PREFIX =
  process.env.CODE_BUNDLE_BUCKET_PREFIX ?? "bundles/";

const LOCAL_CODE_BUNDLE_BUCKET = "";

// Default to storing attachments in code bundle bucket for easy deployment.
export const ATTACHMENT_BUCKET_NAME =
  process.env.ATTACHMENT_BUCKET ?? CODE_BUNDLE_BUCKET_NAME;
export const ATTACHMENT_BUCKET_PREFIX =
  process.env.ATTACHMENT_BUCKET_PREFIX ?? "attachments/";

export const DISABLE_ATTACHMENT_OPTIMIZATION = parseBooleanEnv(
  process.env.DISABLE_ATTACHMENT_OPTIMIZATION,
);

export const ALLOW_CODE_FUNCTION_EXECUTION =
  parseBooleanEnv(process.env.ALLOW_CODE_FUNCTION_EXECUTION) ?? false;

export const CODE_FUNCTION_EXECUTION_TIMEOUT_S = process.env
  .CODE_FUNCTION_EXECUTION_TIMEOUT_S
  ? parseInt(process.env.CODE_FUNCTION_EXECUTION_TIMEOUT_S)
  : 30;

export const BRAINSTORE_ENABLED =
  parseBooleanEnv(process.env.BRAINSTORE_ENABLED) ?? false;

export const BRAINSTORE_DEFAULT = parseBrainstoreDefault(
  process.env.BRAINSTORE_DEFAULT,
);

export let BRAINSTORE_URL = process.env.BRAINSTORE_URL;
export const BRAINSTORE_WRITER_URL =
  process.env.BRAINSTORE_WRITER_URL &&
  process.env.BRAINSTORE_WRITER_URL.trim() !== ""
    ? process.env.BRAINSTORE_WRITER_URL
    : undefined;

export const BRAINSTORE_LOG_REPRO_COMMANDS =
  parseBooleanEnv(process.env.BRAINSTORE_LOG_REPRO_COMMANDS) ?? false;
export const BRAINSTORE_DISABLE_ETL_LOOP =
  parseBooleanEnv(process.env.BRAINSTORE_DISABLE_ETL_LOOP) ?? false;
export const BRAINSTORE_DISABLE_COMPACTION =
  parseBooleanEnv(process.env.BRAINSTORE_DISABLE_COMPACTION) ?? false;
export const BRAINSTORE_BACKFILL_DISABLE_LOGS =
  parseBooleanEnv(process.env.BRAINSTORE_BACKFILL_DISABLE_LOGS) ?? false;
// We need to resolve some bugs in the comments backfill before we can re-enable
// this by default.
export const BRAINSTORE_BACKFILL_DISABLE_COMMENTS =
  parseBooleanEnv(process.env.BRAINSTORE_BACKFILL_DISABLE_COMMENTS) ?? true;
export const BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE = process.env
  .BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE
  ? parseInt(process.env.BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE)
  : 40 * 1000;
export const BRAINSTORE_DISABLE_REALTIME_QUERIES = process.env
  .BRAINSTORE_DISABLE_REALTIME_QUERIES
  ? parseBooleanEnv(process.env.BRAINSTORE_DISABLE_REALTIME_QUERIES)
  : false;
export const BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS = process.env
  .BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS
  ? parseInt(process.env.BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS)
  : 500;

export const BRAINSTORE_INSERT_ROW_REFS = process.env.BRAINSTORE_INSERT_ROW_REFS
  ? parseBooleanEnv(process.env.BRAINSTORE_INSERT_ROW_REFS)
  : false;

export const BRAINSTORE_REALTIME_WAL_BUCKET_NAME =
  process.env.BRAINSTORE_REALTIME_WAL_BUCKET;
export const BRAINSTORE_REALTIME_WAL_BUCKET_PREFIX =
  process.env.BRAINSTORE_REALTIME_WAL_BUCKET_PREFIX ?? "brainstore/wal";

const LOCAL_BRAINSTORE_URL = "http://localhost:4000/";

export const CATCHUP_ETL_ARN = process.env.CATCHUP_ETL_ARN || null; // Make empty string None
export const AI_PROXY_FN_ARN = process.env.AI_PROXY_FN_ARN || null;
export const AI_PROXY_FN_URL = process.env.AI_PROXY_FN_URL || null;
export const AI_PROXY_INVOKE_ROLE = process.env.AI_PROXY_INVOKE_ROLE;

const _WHITELISTED_ORIGINS_VAR = process.env.WHITELISTED_ORIGINS;
export let WHITELISTED_ORIGINS: (string | RegExp)[] = [];

export const OUTBOUND_RATE_LIMIT_WINDOW_MINUTES = process.env
  .OUTBOUND_RATE_LIMIT_WINDOW_MINUTES
  ? parseInt(process.env.OUTBOUND_RATE_LIMIT_WINDOW_MINUTES)
  : 1;
// Note: setting to 0 will disable rate limits.
export const OUTBOUND_RATE_LIMIT_MAX_REQUESTS = process.env
  .OUTBOUND_RATE_LIMIT_MAX_REQUESTS
  ? parseInt(process.env.OUTBOUND_RATE_LIMIT_MAX_REQUESTS)
  : 0;

export const ENABLE_INVOKE_RATE_LIMIT =
  parseBooleanEnv(process.env.ENABLE_INVOKE_RATE_LIMIT) ?? true;

export const INVOKE_RATE_LIMIT_PER_10S = process.env.INVOKE_RATE_LIMIT_PER_10S
  ? parseInt(process.env.INVOKE_RATE_LIMIT_PER_10S)
  : 100;

export const QUARANTINE_FUNCTIONS: Record<
  string,
  Record<string, string | undefined>
> = {
  node: {
    "20": process.env.QUARANTINE_NODEJS_20_ARN,
    "21": process.env.QUARANTINE_NODEJS_20_ARN,
    "22": process.env.QUARANTINE_NODEJS_20_ARN,
  },
};

export const QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME = process.env
  .QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME
  ? parseInt(process.env.QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME)
  : 3;

// All of these variables must be set to use the lambda-based quarantine.
export const API_HANDLER_ROLE = process.env.API_HANDLER_ROLE;
export const QUARANTINE_FUNCTION_ROLE = process.env.QUARANTINE_FUNCTION_ROLE;
export const QUARANTINE_INVOKE_ROLE = process.env.QUARANTINE_INVOKE_ROLE;
export const QUARANTINE_REGION = process.env.QUARANTINE_REGION;
export const QUARANTINE_PRIVATE_SUBNET_1_ID =
  process.env.QUARANTINE_PRIVATE_SUBNET_1_ID;
export const QUARANTINE_PRIVATE_SUBNET_2_ID =
  process.env.QUARANTINE_PRIVATE_SUBNET_2_ID;
export const QUARANTINE_PRIVATE_SUBNET_3_ID =
  process.env.QUARANTINE_PRIVATE_SUBNET_3_ID;
export const QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP =
  process.env.QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP;
export const QUARANTINE_PUB_PRIVATE_VPC_ID =
  process.env.QUARANTINE_PUB_PRIVATE_VPC_ID;

// If these are set, we'll use them to access the quarantine VPC rather than the built-in roles.
export const QUARANTINE_USER_ACCESS_KEY =
  process.env.QUARANTINE_USER_ACCESS_KEY;
export const QUARANTINE_USER_SECRET_ACCESS_KEY =
  process.env.QUARANTINE_USER_SECRET_ACCESS_KEY;

export const TS_API_HOST = process.env.TS_API_HOST || "0.0.0.0";
export const TS_API_PORT = parseInt(process.env.TS_API_PORT || "8000");

export const FUNCTION_SECRET_KEY = process.env.FUNCTION_SECRET_KEY;

// This is really only separated so that staging can use the same secret key as prod.
export const SERVICE_TOKEN_SECRET_KEY =
  process.env.SERVICE_TOKEN_SECRET_KEY ?? FUNCTION_SECRET_KEY;

export const DISABLE_ASYNC_SCORING = process.env.DISABLE_ASYNC_SCORING
  ? parseBooleanEnv(process.env.DISABLE_ASYNC_SCORING)
  : false;
export const DISABLE_ASYNC_SCORING_OBJECT_IDS =
  process.env.DISABLE_ASYNC_SCORING_OBJECT_IDS;

export let ASYNC_SCORING_FETCH_PAYLOAD = false;
// https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html
export const ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD = process.env
  .ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD
  ? parseInt(process.env.ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD)
  : 256 * 1024; // 256KB

export const CONTROL_PLANE_TELEMETRY =
  process.env.CONTROL_PLANE_TELEMETRY || "";
export const TELEMETRY_ENABLED = process.env.TELEMETRY_ENABLED
  ? parseBooleanEnv(process.env.TELEMETRY_ENABLED)
  : CONTROL_PLANE_TELEMETRY.includes("usage");
export const TELEMETRY_URL =
  process.env.TELEMETRY_URL ||
  `${PUBLIC_ALLOWED_ORIGIN || "https://www.braintrust.dev"}/api/billing/telemetry/v1/events`;

export const TELEMETRY_TOKEN = process.env.TELEMETRY_TOKEN;
export const TELEMETRY_LOG_LEVEL = process.env.TELEMETRY_LOG_LEVEL ?? "error";

export const OTLP_HTTP_ENDPOINT = process.env.OTLP_HTTP_ENDPOINT;
export const OTLP_METRICS_EXPORT_INTERVAL_MS = process.env
  .OTLP_METRICS_EXPORT_INTERVAL_MS
  ? parseInt(process.env.OTLP_METRICS_EXPORT_INTERVAL_MS)
  : 60000; // 1 minute
export const PRETTY_LOGGING = parseBooleanEnv(process.env.PRETTY_LOGGING);

export const TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG = process.env
  .TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG
  ? parseBooleanEnv(process.env.TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG)
  : false;
export const TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE = process.env
  .TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE
  ? parseBooleanEnv(process.env.TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE)
  : false;

export const MAX_LIMIT_FOR_QUERIES = process.env.MAX_LIMIT_FOR_QUERIES
  ? parseInt(process.env.MAX_LIMIT_FOR_QUERIES)
  : 0;
export const TESTING_ONLY_SKIP_LIMIT_CHECKS = process.env
  .TESTING_ONLY_SKIP_LIMIT_CHECKS
  ? parseBooleanEnv(process.env.TESTING_ONLY_SKIP_LIMIT_CHECKS)
  : false;

export let IS_LOCAL = false;

export function initializeEnv(isLocal: boolean) {
  if (isLocal || process.env.CHALICE_LOCAL_ENV) {
    IS_LOCAL = true;
    ASYNC_SCORING_FETCH_PAYLOAD =
      parseBooleanEnv(process.env.ASYNC_SCORING_FETCH_PAYLOAD) ?? false;
    if (
      process.env.CHALICE_LOCAL_USE_LOCAL_ENV === undefined ||
      process.env.CHALICE_LOCAL_USE_LOCAL_ENV === "0"
    ) {
      if (!ALLOWED_ORIGIN) {
        ALLOWED_ORIGIN = PROD_ALLOWED_ORIGIN;
      }
      if (PUBLIC_ALLOWED_ORIGIN === undefined) {
        PUBLIC_ALLOWED_ORIGIN = ALLOWED_ORIGIN;
      }
      if (REALTIME_URL === undefined) {
        REALTIME_URL = PROD_REALTIME_URL;
      }
      if (PROXY_URL === undefined) {
        PROXY_URL = PROD_PROXY_URL;
      }
      // Often times we want to run prod chalice against local postgres/redis.
      if (PG_URL === undefined) {
        PG_URL = LOCAL_PG_URL;
      }
      if (REDIS_HOST === undefined) {
        REDIS_HOST = LOCAL_REDIS_HOST;
      }
      if (REDIS_PORT === undefined) {
        REDIS_PORT = LOCAL_REDIS_PORT;
      }
    } else {
      if (!ALLOWED_ORIGIN) {
        ALLOWED_ORIGIN = "http://localhost:3000";
      }
      if (PUBLIC_ALLOWED_ORIGIN === undefined) {
        PUBLIC_ALLOWED_ORIGIN = ALLOWED_ORIGIN;
      }
      if (REALTIME_URL === undefined) {
        REALTIME_URL = "http://localhost:8788";
      }
      if (PROXY_URL === undefined) {
        PROXY_URL = "http://localhost:8787/v1";
      }
      if (PG_URL === undefined) {
        PG_URL = LOCAL_PG_URL;
      }
      if (REDIS_HOST === undefined) {
        REDIS_HOST = LOCAL_REDIS_HOST;
      }
      if (REDIS_PORT === undefined) {
        REDIS_PORT = LOCAL_REDIS_PORT;
      }
      if (RESPONSE_BUCKET_NAME === undefined) {
        RESPONSE_BUCKET_NAME = LOCAL_RESPONSE_BUCKET;
      }
      if (RESPONSE_BUCKET_ACCESS_KEY_ID === undefined) {
        RESPONSE_BUCKET_ACCESS_KEY_ID = LOCAL_RESPONSE_BUCKET_ACCESS_KEY_ID;
      }
      if (RESPONSE_BUCKET_SECRET_ACCESS_KEY === undefined) {
        RESPONSE_BUCKET_SECRET_ACCESS_KEY =
          LOCAL_RESPONSE_BUCKET_SECRET_ACCESS_KEY;
      }
      if (RESPONSE_BUCKET_REGION === undefined) {
        RESPONSE_BUCKET_REGION = LOCAL_RESPONSE_BUCKET_REGION;
      }
      if (RESPONSE_BUCKET_S3_ENDPOINT === undefined) {
        RESPONSE_BUCKET_S3_ENDPOINT = LOCAL_RESPONSE_BUCKET_S3_ENDPOINT;
      }
      if (CODE_BUNDLE_BUCKET_NAME === undefined) {
        CODE_BUNDLE_BUCKET_NAME = LOCAL_CODE_BUNDLE_BUCKET;
      }
      IS_RESPONSE_BUCKET_LOCAL = true;
    }
    if (ORG_NAME === undefined) {
      ORG_NAME = "*";
    }
    if (BRAINSTORE_URL === undefined) {
      BRAINSTORE_URL = LOCAL_BRAINSTORE_URL;
    }
    // By default, we want any SDK code which logs to the API service to use the
    // loopback URL, instead of the public API url.
    if (!process.env.BRAINTRUST_API_URL) {
      process.env.BRAINTRUST_API_URL = `http://127.0.0.1:${TS_API_PORT}`;
    }
  } else {
    IS_LOCAL = false;
    ASYNC_SCORING_FETCH_PAYLOAD = true;
    if (!ALLOWED_ORIGIN) {
      ALLOWED_ORIGIN = PROD_ALLOWED_ORIGIN;
    }
    if (!PUBLIC_ALLOWED_ORIGIN) {
      PUBLIC_ALLOWED_ORIGIN = ALLOWED_ORIGIN;
    }
    if (REALTIME_URL === undefined) {
      REALTIME_URL = PROD_REALTIME_URL;
    }
    if (PROXY_URL === undefined) {
      PROXY_URL = PROD_PROXY_URL;
    }
  }

  WHITELISTED_ORIGINS = [
    PUBLIC_ALLOWED_ORIGIN,
    "https://www.braintrustdata.com",
    new RegExp("https://.*.preview.braintrust.dev"),
    ...(_WHITELISTED_ORIGINS_VAR
      ? _WHITELISTED_ORIGINS_VAR
          .split(",")
          .map((x) => x.trim())
          .filter((x) => x)
      : []),
  ];
}

export function parseBooleanEnv(
  value: string | undefined,
): boolean | undefined {
  if (value === undefined) {
    return undefined;
  }
  return ["true", "1", "yes", "on"].includes(value.toLowerCase());
}

export function getApiPort(): number {
  return TS_API_PORT;
}

function parseBrainstoreDefault(
  value: string | undefined,
): "default" | "force" | undefined {
  if (parseBooleanEnv(value) || value === "default") {
    return "default";
  } else if (value === "force") {
    return "force";
  }
  return undefined;
}
