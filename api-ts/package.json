{"name": "@braintrust/api-ts", "version": "1.0.0", "description": "", "main": "./dist/index.js", "binary": true, "scripts": {"build": "run-p build:* lint", "build:typecheck": "tsc --noEmit", "build:main": "tsx scripts/build.ts", "build:wrapper": "GIT_COMMIT=${GIT_COMMIT_HASH:-$(git rev-parse HEAD)} && esbuild --platform=node --bundle src/wrapper/index.ts --outfile=dist/wrapper.js --minify --sourcemap --target=es2020 --define:process.env.GIT_COMMIT='\"'\"$GIT_COMMIT\"'\"' --define:process.env.DEPLOYMENT_MODE='\"local\"'", "build:lambda_wrapper_py": "mkdir -p dist/proxy/vm && cp py/wrapper-bundle.py dist/proxy/vm/wrapper-bundle.py", "build:lambda_wrapper_inline_py": "mkdir -p dist/proxy/vm && cp py/wrapper-inline.py dist/proxy/vm/wrapper-inline.py", "build:wrapper_inline_py": "mkdir -p dist/local/vm && cp py/wrapper-inline.py dist/local/vm/wrapper-inline.py", "build:wrapper_bundle_py": "mkdir -p dist/local/vm && cp py/wrapper-bundle.py dist/local/vm/wrapper-bundle.py", "build:lambda_wrapper_inline_py_deps": "mkdir -p dist/proxy/vm/pkg && uv pip install --force-reinstall -r requirements.txt --target dist/proxy/vm/pkg --python-platform linux --python-version 3.12", "build:lambda_warmup_quarantine": "GIT_COMMIT=${GIT_COMMIT_HASH:-$(git rev-parse HEAD)} && esbuild --platform=node --external:@aws-sdk/* --bundle src/lambda-quarantine/warmup-lambda.ts --outfile=dist/lambda-quarantine/warmup-lambda/index.js --minify --sourcemap --target=es2020 --define:process.env.GIT_COMMIT='\"'\"$GIT_COMMIT\"'\"' --define:process.env.DEPLOYMENT_MODE='\"lambda\"'", "lint": "eslint src", "watch": "run-p watch:*", "watch:main": "tsx scripts/build.ts --watch", "watch:wrapper": "GIT_COMMIT=${GIT_COMMIT_HASH:-$(git rev-parse HEAD)} && esbuild --platform=node --bundle src/wrapper/index.ts --outfile=dist/wrapper.js --sourcemap --target=es2020 --watch=forever --define:process.env.GIT_COMMIT='\"'\"$GIT_COMMIT\"'\"' --define:process.env.DEPLOYMENT_MODE='\"local\"'", "watch:lambda_wrapper_py": "chokidar \"py/wrapper-bundle.py\" -c \"mkdir -p dist/lambda && cp py/wrapper-bundle.py dist/lambda/wrapper-bundle.py\"", "watch:lambda_wrapper_inline_py": "chokidar \"py/wrapper-inline.py\" -c \"mkdir -p dist/lambda && cp py/wrapper-inline.py dist/lambda/wrapper-inline.py\"", "watch:wrapper_inline_py": "chokidar \"py/wrapper-inline.py\" -c \"mkdir -p dist/local/vm && cp py/wrapper-inline.py dist/local/vm/wrapper-inline.py\"", "watch:wrapper_bundle_py": "chokidar \"py/wrapper-bundle.py\" -c \"mkdir -p dist/local/vm && cp py/wrapper-bundle.py dist/local/vm/wrapper-bundle.py\"", "dev": "run-p build:* && run-p watch:* dev:*", "dev:serve": "nodemon ${DEBUG:+--inspect=9240} --enable-source-maps --watch dist/local/local.js dist/local/local.js", "run:test-proxy": "nodemon ${DEBUG:+--inspect=9250} --enable-source-maps --watch dist/test-proxy/index.js dist/test-proxy/index.js", "test-proxy": "run-s build:* run:test-proxy", "postbuild": "./scripts/postbuild.sh", "test": "vitest run", "build-docker": "run-p build:typecheck build:main build:wrapper* lint"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-lambda": "^3.750.0", "@aws-sdk/client-s3": "^3.676.0", "@aws-sdk/client-sts": "^3.592.0", "@aws-sdk/lib-storage": "^3.565.0", "@azure/identity": "^4.9.1", "@azure/storage-blob": "^12.26.0", "@braintrust/btql": "workspace:*", "@braintrust/btql-wasm": "workspace:*", "@braintrust/local": "workspace:*", "@braintrust/proxy": "workspace:*", "@braintrust/typespecs": "workspace:*", "@mapbox/node-pre-gyp": "^1.0.11", "@modelcontextprotocol/sdk": "^1.17.4", "@opentelemetry/api": "1.8.0", "@opentelemetry/auto-instrumentations-node": "^0.58.0", "@opentelemetry/core": "^2.0.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.202.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.200.0", "@opentelemetry/instrumentation-express": "^0.48.1", "@opentelemetry/instrumentation-http": "^0.200.0", "@opentelemetry/otlp-transformer": "^0.200.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@types/oauth2-server": "^3.0.18", "@vercel/otel": "^1.10.0", "JSONStream": "^1.3.5", "amazon-s3-url": "^1.0.3", "archiver": "^7.0.1", "autoevals": "workspace:*", "body-parser": "^1.20.3", "braintrust": "workspace:*", "compression": "^1.7.4", "content-disposition": "^0.5.4", "cors": "^2.8.5", "datadog-lambda-js": "^11.125.0", "date-fns": "^4.1.0", "dd-trace": "^5.49.0", "dockerode": "^4.0.6", "dotenv": "^16.3.1", "duckdb": "^1.2.1", "eventsource-parser": "^1.1.1", "exponential-backoff": "^3.1.2", "express": "^4.19.2", "express-async-errors": "^3.1.1", "http-proxy-middleware": "^3.0.5", "lambda-stream": "^0.5.0", "lodash": "^4.17.21", "long": "^5.2.3", "node-stream-zip": "^1.15.0", "oauth2-server": "^3.1.1", "pg": "^8.11.3", "pg-error-enum": "^0.7.1", "pg-query-stream": "^4.5.5", "pino": "^9.6.0", "pino-lambda": "^4.4.1", "pino-pretty": "7.0.0", "protobufjs": "^7.4.0", "rate-limiter-flexible": "^7.1.1", "redis": "^4.6.8", "serverless-http": "^3.2.0", "siphash": "^1.1.0", "slugify": "^1.6.6", "stream-chain": "^2.2.5", "stream-json": "^1.8.0", "string.prototype.towellformed": "^1.0.2", "sucrase": "^3.35.0", "tmp": "^0.2.3", "undici": "^6.21.1", "uuid": "^9.0.1", "zod": "^3.25.34", "zod-to-json-schema": "^3.22.5"}, "devDependencies": {"@aws-sdk/s3-request-presigner": "^3.676.0", "@types/archiver": "^6.0.2", "@types/aws-lambda": "^8.10.119", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.5", "@types/content-disposition": "^0.5.8", "@types/cors": "^2.8.17", "@types/dockerode": "^3.3.29", "@types/express": "^4.17.17", "@types/lodash": "^4.17.7", "@types/node": "^20.5.0", "@types/pg": "^8.11.14", "@types/stream-chain": "^2.1.0", "@types/stream-json": "^1.7.7", "@types/tmp": "^0.2.6", "@typescript-eslint/eslint-plugin": "^8.11.0", "Pick": "link:@types/stream-json/filters/Pick", "StreamArray": "link:@types/stream-json/streamers/StreamArray", "argparse": "^2.0.1", "chokidar-cli": "^3.0.0", "esbuild": "^0.25.3", "esbuild-plugin-pino": "^2.2.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "mock-aws-s3": "^4.0.2", "nodemon": "^3.0.1", "npm-run-all": "^4.1.5", "typescript": "^5.0.4", "vitest": "^2.1.9"}}