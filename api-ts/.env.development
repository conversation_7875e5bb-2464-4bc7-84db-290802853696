# Keep this file in sync with
# local/py/src/braintrust_local/generate_docker_compose.py.

BRAINTRUST_HOSTED_DATA_PLANE=true

RESPONSE_BUCKET=responses
CODE_BUNDLE_BUCKET=code-bundles
ALLOW_CODE_FUNCTION_EXECUTION=true
STRICT_VALIDATION_MODE=1
TS_API_HEALTHSERVER_HOST=localhost
TS_API_HEALTHSERVER_PORT=8790
REDIS_HOST=localhost
REDIS_PORT=6479
# Replace REDIS_HOST and REDIS_PORT with this URL in order to connect over SSL
# REDIS_URL=rediss://localhost:6479/0?ssl_cert_reqs=none
PROXY_ALLOW_PASSTHROUGH_CREDENTIALS=true
CHALICE_LOCAL_USE_LOCAL_ENV=1
TS_API_ASYNC_SCORING_PROXY_URL=http://127.0.0.1:8001
ASYNC_SCORING_FETCH_PAYLOAD=true
# NOTE: keep this in sync with tests/bt_services/test_async_scoring.py and local/py/src/braintrust_local/generate_docker_compose.py
ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD=65536
PRETTY_LOGGING=true

# must include usage to enable telemetry for tests and local dev
CONTROL_PLANE_TELEMETRY=usage
TELEMETRY_URL=http://127.0.0.1:8001/events
TELEMETRY_TOKEN=foobar

# https://app.withorb.com/api-keys (in 1p)
# ORB_API_KEY=

# https://app.withorb.com/webhooks (in 1p)
# ORB_WEBHOOK_SECRET=

# https://dashboard.stripe.com/apikeys (in 1p)
# STRIPE_SECRET_KEY=f

FUNCTION_SECRET_KEY=foobar

BRAINSTORE_ENABLED=true
BRAINSTORE_DEFAULT=force
BRAINSTORE_REALTIME_WAL_BUCKET=code-bundles
# This is very noisy
# BRAINSTORE_LOG_REPRO_COMMANDS=true
BRAINSTORE_INSERT_ROW_REFS=true
INSERT_LOGS2=true
# For local dev, set the timeout to 0 so that we don't timeout spuriously in
# tests.
BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS=0

# This is much more frequent than we'd want to set in prod. It's just useful while
# developing metrics to see them update quickly.
OTLP_METRICS_EXPORT_INTERVAL_MS=1000

MAX_LIMIT_FOR_QUERIES = 1000

TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG=true
TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE=true
TESTING_ONLY_ALLOW_ORG_NAME_HEADER_OVERRIDE=true
TESTING_ONLY_SKIP_LIMIT_CHECKS=true

# used in test_api_rate_limits.py
RATELIMIT_API_LOGS_ORG=c5292878-410f-4bbb-bffc-c61f9e624805=5
RATELIMIT_API_LOGS_ORG_WINDOW_SECS=10
RATELIMIT_API_LOGS_ORG_ENFORCE=true

# For local testing
# QUARANTINE_REGION=us-east-1
# API_HANDLER_ROLE=arn:aws:iam::872608195481:role/Dev-Quarantine-APIHandlerRole-iRS8ejcTGAtY
# QUARANTINE_INVOKE_ROLE=arn:aws:iam::872608195481:role/Dev-Quarantine-QuarantineInvokeRole-X4oIEoB2E0K8
# QUARANTINE_FUNCTION_ROLE=arn:aws:iam::872608195481:role/Dev-Quarantine-QuarantineFunctionRole-EndiDi10DJJk
# QUARANTINE_PRIVATE_SUBNET_1_ID=subnet-0a6f2b75988f81315
# QUARANTINE_PRIVATE_SUBNET_2_ID=subnet-07c928a8d0d2e0bc5
# QUARANTINE_PRIVATE_SUBNET_3_ID=subnet-0e6e502f7f2d7c41c
# QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP=sg-011a1dd20481b3959
# QUARANTINE_PUB_PRIVATE_VPC_ID=vpc-0f0344bff7eb87100

# Uncomment to run a self-hosted data plane:
# BRAINTRUST_HOSTED_DATA_PLANE=false
# ORG_NAME=braintrustdata.com

# Uncomment to run a self-hosted data plane with multiple orgs:
# BRAINTRUST_HOSTED_DATA_PLANE=false
# ORG_NAME=*
# PRIMARY_ORG_NAME=braintrustdata.com

ENABLE_INVOKE_RATE_LIMIT=false
MCP_API_URL=http://localhost:8000
