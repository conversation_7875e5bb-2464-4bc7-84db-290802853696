import { type Too<PERSON><PERSON><PERSON> } from "./registry";

export type PageKey =
  | "experiments"
  | "playground"
  | "logs"
  | "dataset"
  | "loop"
  | "btql"
  | "unknown";

/**
 * Mapping from page contexts to the tools that should be available in each context.
 * This allows different pages to have different tool capabilities based on their purpose.
 */
export const PAGE_TOOL_CONFIG: Record<PageKey, ToolName[]> = {
  playground: [
    "edit_task",
    "get_summary",
    "get_results",
    "run_task",
    "edit_data",
    "get_available_scorers",
    "edit_scorers",
    "create_code_scorer",
    "create_llm_scorer",
    "infer_schema",
    "btql_query",
    "continue_execution",
    "search_docs",
  ],

  experiments: [
    "get_summary",
    "get_results",
    "infer_schema",
    "btql_query",
    "get_available_scorers",
    "continue_execution",
    "search_docs",
  ],

  logs: ["infer_schema", "btql_query", "continue_execution", "search_docs"],

  dataset: [
    "get_results",
    "edit_data",
    "infer_schema",
    "btql_query",
    "continue_execution",
    "search_docs",
  ],

  btql: ["infer_schema", "run_btql", "continue_execution", "search_docs"],

  loop: [
    "edit_task",
    "get_summary",
    "get_results",
    "run_task",
    "edit_data",
    "get_available_scorers",
    "edit_scorers",
    "create_code_scorer",
    "create_llm_scorer",
    "infer_schema",
    "btql_query",
    "continue_execution",
    "search_docs",
  ],

  unknown: [
    "get_summary",
    "get_results",
    "infer_schema",
    "btql_query",
    "continue_execution",
    "search_docs",
  ],
};

/**
 * Get the allowed tools for a specific page context.
 * Falls back to "unknown" page tools if the page key is not recognized.
 */
export function getToolsForPage(pageKey: PageKey): ToolName[] {
  return PAGE_TOOL_CONFIG[pageKey] ?? PAGE_TOOL_CONFIG.unknown;
}

/**
 * Check if a specific tool is allowed in a given page context.
 */
export function isToolAllowedForPage(
  toolName: ToolName,
  pageKey: PageKey,
): boolean {
  const allowedTools = getToolsForPage(pageKey);
  return allowedTools.includes(toolName);
}
