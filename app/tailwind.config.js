/** @type {import('tailwindcss').Config} */

import { createPreset } from "fumadocs-ui/tailwind-plugin";
import { MONO_FONT_STACK } from "./ui/fontstack";

module.exports = {
  presets: [
    createPreset({
      prefix: "docs",
      cssPrefix: "docs",
      addGlobalColors: true,
    }),
  ],
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./ui/**/*.{js,ts,jsx,tsx}",

    // fumadocs
    "./content/**/*.mdx",
    "./node_modules/fumadocs-ui/dist/**/*.js",
    "./node_modules/fumadocs-openapi/dist/**/*.js",
  ],
  future: {
    hoverOnlyWhenSupported: true,
  },
  darkMode: "class",
  theme: {
    extend: {
      fontFamily: {
        mono: [MONO_FONT_STACK],
        inter: ["var(--font-inter)"],
        display: ["var(--font-display)"],
        suisse: ["var(--font-suisse)"],
      },
      colors: {
        primary: {
          50: "rgb(var(--primary-50) / <alpha-value>)",
          100: "rgb(var(--primary-100) / <alpha-value>)",
          200: "rgb(var(--primary-200) / <alpha-value>)",
          300: "rgb(var(--primary-300) / <alpha-value>)",
          400: "rgb(var(--primary-400) / <alpha-value>)",
          500: "rgb(var(--primary-500) / <alpha-value>)",
          600: "rgb(var(--primary-600) / <alpha-value>)",
          700: "rgb(var(--primary-700) / <alpha-value>)",
          800: "rgb(var(--primary-800) / <alpha-value>)",
          900: "rgb(var(--primary-900) / <alpha-value>)",
          950: "rgb(var(--primary-950) / <alpha-value>)",
        },
        accent: {
          50: "rgb(var(--accent-50) / <alpha-value>)",
          100: "rgb(var(--accent-100) / <alpha-value>)",
          200: "rgb(var(--accent-200) / <alpha-value>)",
          300: "rgb(var(--accent-300) / <alpha-value>)",
          400: "rgb(var(--accent-400) / <alpha-value>)",
          500: "rgb(var(--accent-500) / <alpha-value>)",
          600: "rgb(var(--accent-600) / <alpha-value>)",
          700: "rgb(var(--accent-700) / <alpha-value>)",
          800: "rgb(var(--accent-800) / <alpha-value>)",
          900: "rgb(var(--accent-900) / <alpha-value>)",
          950: "rgb(var(--accent-950) / <alpha-value>)",
        },
        comparison: {
          50: "rgb(var(--comparison-50) / <alpha-value>)",
          100: "rgb(var(--comparison-100) / <alpha-value>)",
          200: "rgb(var(--comparison-200) / <alpha-value>)",
          300: "rgb(var(--comparison-300) / <alpha-value>)",
          400: "rgb(var(--comparison-400) / <alpha-value>)",
          500: "rgb(var(--comparison-500) / <alpha-value>)",
          600: "rgb(var(--comparison-600) / <alpha-value>)",
          700: "rgb(var(--comparison-700) / <alpha-value>)",
          800: "rgb(var(--comparison-800) / <alpha-value>)",
          900: "rgb(var(--comparison-900) / <alpha-value>)",
          950: "rgb(var(--comparison-950) / <alpha-value>)",
        },
        good: {
          50: "rgb(var(--good-50) / <alpha-value>)",
          100: "rgb(var(--good-100) / <alpha-value>)",
          200: "rgb(var(--good-200) / <alpha-value>)",
          300: "rgb(var(--good-300) / <alpha-value>)",
          400: "rgb(var(--good-400) / <alpha-value>)",
          500: "rgb(var(--good-500) / <alpha-value>)",
          600: "rgb(var(--good-600) / <alpha-value>)",
          700: "rgb(var(--good-700) / <alpha-value>)",
          800: "rgb(var(--good-800) / <alpha-value>)",
          900: "rgb(var(--good-900) / <alpha-value>)",
          950: "rgb(var(--good-950) / <alpha-value>)",
        },
        bad: {
          50: "rgb(var(--bad-50) / <alpha-value>)",
          100: "rgb(var(--bad-100) / <alpha-value>)",
          200: "rgb(var(--bad-200) / <alpha-value>)",
          300: "rgb(var(--bad-300) / <alpha-value>)",
          400: "rgb(var(--bad-400) / <alpha-value>)",
          500: "rgb(var(--bad-500) / <alpha-value>)",
          600: "rgb(var(--bad-600) / <alpha-value>)",
          700: "rgb(var(--bad-700) / <alpha-value>)",
          800: "rgb(var(--bad-800) / <alpha-value>)",
          900: "rgb(var(--bad-900) / <alpha-value>)",
          950: "rgb(var(--bad-950) / <alpha-value>)",
        },
        blue: {
          50: "rgb(var(--blue-50) / <alpha-value>)",
          100: "rgb(var(--blue-100) / <alpha-value>)",
          200: "rgb(var(--blue-200) / <alpha-value>)",
          300: "rgb(var(--blue-300) / <alpha-value>)",
          400: "rgb(var(--blue-400) / <alpha-value>)",
          500: "rgb(var(--blue-500) / <alpha-value>)",
          600: "rgb(var(--blue-600) / <alpha-value>)",
          700: "rgb(var(--blue-700) / <alpha-value>)",
          800: "rgb(var(--blue-800) / <alpha-value>)",
          900: "rgb(var(--blue-900) / <alpha-value>)",
          950: "rgb(var(--blue-950) / <alpha-value>)",
        },
        violet: {
          50: "rgb(var(--violet-50) / <alpha-value>)",
          100: "rgb(var(--violet-100) / <alpha-value>)",
          200: "rgb(var(--violet-200) / <alpha-value>)",
          300: "rgb(var(--violet-300) / <alpha-value>)",
          400: "rgb(var(--violet-400) / <alpha-value>)",
          500: "rgb(var(--violet-500) / <alpha-value>)",
          600: "rgb(var(--violet-600) / <alpha-value>)",
          700: "rgb(var(--violet-700) / <alpha-value>)",
          800: "rgb(var(--violet-800) / <alpha-value>)",
          900: "rgb(var(--violet-900) / <alpha-value>)",
          950: "rgb(var(--violet-950) / <alpha-value>)",
        },
        green: {
          50: "rgb(var(--green-50) / <alpha-value>)",
          100: "rgb(var(--green-100) / <alpha-value>)",
          200: "rgb(var(--green-200) / <alpha-value>)",
          300: "rgb(var(--green-300) / <alpha-value>)",
          400: "rgb(var(--green-400) / <alpha-value>)",
          500: "rgb(var(--green-500) / <alpha-value>)",
          600: "rgb(var(--green-600) / <alpha-value>)",
          700: "rgb(var(--green-700) / <alpha-value>)",
          800: "rgb(var(--green-800) / <alpha-value>)",
          900: "rgb(var(--green-900) / <alpha-value>)",
          950: "rgb(var(--green-950) / <alpha-value>)",
        },
        red: {
          50: "rgb(var(--red-50) / <alpha-value>)",
          100: "rgb(var(--red-100) / <alpha-value>)",
          200: "rgb(var(--red-200) / <alpha-value>)",
          300: "rgb(var(--red-300) / <alpha-value>)",
          400: "rgb(var(--red-400) / <alpha-value>)",
          500: "rgb(var(--red-500) / <alpha-value>)",
          600: "rgb(var(--red-600) / <alpha-value>)",
          700: "rgb(var(--red-700) / <alpha-value>)",
          800: "rgb(var(--red-800) / <alpha-value>)",
          900: "rgb(var(--red-900) / <alpha-value>)",
          950: "rgb(var(--red-950) / <alpha-value>)",
        },
        emerald: {
          50: "rgb(var(--emerald-50) / <alpha-value>)",
          100: "rgb(var(--emerald-100) / <alpha-value>)",
          200: "rgb(var(--emerald-200) / <alpha-value>)",
          300: "rgb(var(--emerald-300) / <alpha-value>)",
          400: "rgb(var(--emerald-400) / <alpha-value>)",
          500: "rgb(var(--emerald-500) / <alpha-value>)",
          600: "rgb(var(--emerald-600) / <alpha-value>)",
          700: "rgb(var(--emerald-700) / <alpha-value>)",
          800: "rgb(var(--emerald-800) / <alpha-value>)",
          900: "rgb(var(--emerald-900) / <alpha-value>)",
          950: "rgb(var(--emerald-950) / <alpha-value>)",
        },
        cyan: {
          50: "rgb(var(--cyan-50) / <alpha-value>)",
          100: "rgb(var(--cyan-100) / <alpha-value>)",
          200: "rgb(var(--cyan-200) / <alpha-value>)",
          300: "rgb(var(--cyan-300) / <alpha-value>)",
          400: "rgb(var(--cyan-400) / <alpha-value>)",
          500: "rgb(var(--cyan-500) / <alpha-value>)",
          600: "rgb(var(--cyan-600) / <alpha-value>)",
          700: "rgb(var(--cyan-700) / <alpha-value>)",
          800: "rgb(var(--cyan-800) / <alpha-value>)",
          900: "rgb(var(--cyan-900) / <alpha-value>)",
          950: "rgb(var(--cyan-950) / <alpha-value>)",
        },
        orange: {
          50: "rgb(var(--orange-50) / <alpha-value>)",
          100: "rgb(var(--orange-100) / <alpha-value>)",
          200: "rgb(var(--orange-200) / <alpha-value>)",
          300: "rgb(var(--orange-300) / <alpha-value>)",
          400: "rgb(var(--orange-400) / <alpha-value>)",
          500: "rgb(var(--orange-500) / <alpha-value>)",
          600: "rgb(var(--orange-600) / <alpha-value>)",
          700: "rgb(var(--orange-700) / <alpha-value>)",
          800: "rgb(var(--orange-800) / <alpha-value>)",
          900: "rgb(var(--orange-900) / <alpha-value>)",
          950: "rgb(var(--orange-950) / <alpha-value>)",
        },
        pink: {
          50: "rgb(var(--pink-50) / <alpha-value>)",
          100: "rgb(var(--pink-100) / <alpha-value>)",
          200: "rgb(var(--pink-200) / <alpha-value>)",
          300: "rgb(var(--pink-300) / <alpha-value>)",
          400: "rgb(var(--pink-400) / <alpha-value>)",
          500: "rgb(var(--pink-500) / <alpha-value>)",
          600: "rgb(var(--pink-600) / <alpha-value>)",
          700: "rgb(var(--pink-700) / <alpha-value>)",
          800: "rgb(var(--pink-800) / <alpha-value>)",
          900: "rgb(var(--pink-900) / <alpha-value>)",
          950: "rgb(var(--pink-950) / <alpha-value>)",
        },
        destructive: "rgb(var(--destructive))",
        "destructive-foreground": "hsl(var(--destructive-foreground))",
        muted: "rgb(var(--primary-100) / <alpha-value>)",
        "muted-foreground": "var(--muted-foreground)",
        background: "var(--background)",
        border: "rgb(var(--primary-200) / <alpha-value>)",
        popover: "var(--popover)",
        "popover-foreground": "hsl(var(--popover-foreground))",

        // brand palette - for marketing only!
        brandBlue: "rgb(44 31 234 / <alpha-value>)",
        brandBlue2: "rgb(99 140 243 / <alpha-value>)",
        brandBlue3: "rgb(197 207 232 / <alpha-value>)",
        brandForest: "rgb(9 65 53 / <alpha-value>)",
        brandGreen: "rgb(204 255 0 / <alpha-value>)",
        brandOrange: "rgb(255 128 0 / <alpha-value>)",
        brandBrown: "rgb(51 39 27 / <alpha-value>)",
        brandBlood: "rgb(67 16 29 / <alpha-value>)",
        brandTerracotta: "rgb(201 90 76 / <alpha-value>)",
        brandPeach: "rgb(239 146 130 / <alpha-value>)",
      },
      backgroundImage: ({ theme }) => ({
        "vc-border-gradient": `radial-gradient(at left top, ${theme(
          "colors.gray.500",
        )}, 50px, ${theme("colors.gray.800")} 50%)`,
      }),
      transitionDuration: {
        2000: "2000ms",
      },
      animation: {
        loading: "loading 1s ease-in-out infinite",
        "fade-to-transparent": "fadeToTransparent 1s ease-in-out forwards",
        shine: "shimmer 2s infinite",
        textShimmer: "textShimmer 1.5s infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "smooth-spin":
          "smoothSpin 1.5s cubic-bezier(0.72, 0.29, 0.22, 0.65) infinite",
        // briefly highlight a scrolled-to column header
        "column-flash": "columnFlash 1.2s ease-out",
      },
      keyframes: () => ({
        loading: {
          "0%": {
            opacity: ".2",
          },
          "20%": {
            opacity: "1",
            transform: "translateX(1px)",
          },
          to: {
            opacity: ".2",
          },
        },
        shimmer: {
          "100%": {
            transform: "translateX(100%)",
          },
        },
        textShimmer: {
          "0%": {
            "background-size": "200% 200%",
            "background-position": "200% 50%",
          },
          "100%": {
            "background-size": "200% 200%",
            "background-position": "0% 50%",
          },
        },
        translateXReset: {
          "100%": {
            transform: "translateX(0)",
          },
        },
        fadeToTransparent: {
          "0%": {
            opacity: 1,
          },
          "40%": {
            opacity: 1,
          },
          "100%": {
            opacity: 0,
          },
        },
        columnFlash: {
          "0%": {
            backgroundColor: "transparent",
          },
          "15%": {
            backgroundColor: "rgb(var(--accent-100) / 1)",
          },
          "85%": {
            backgroundColor: "transparent",
          },
          "100%": {
            backgroundColor: "transparent",
          },
        },
        // Override tailwindcss-animate enter animation to include a "to" - this is necessary to prevent certain radix animations from flickering if there's a re-render while animating in
        enter: {
          from: {
            opacity: "var(--tw-enter-opacity, 1)",
            transform:
              "translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))",
          },
          to: {
            opacity: 1,
            transform: "translate3d(0, 0, 0) scale3d(1, 1, 1) rotate(0)",
          },
        },
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        smoothSpin: {
          from: {
            transform: "rotate(0deg)",
          },
          to: {
            transform: "rotate(360deg)",
          },
        },
      }),
      spacing: {
        13: "52px",
        15: "60px",
        18: "72px",
        19: "76px",
        30: "120px",
      },
      maxWidth: {
        landing: "1440px",
      },
      typography: () => ({
        DEFAULT: {
          css: {
            "--tw-prose-pre-bg": "transparent",
            "--tw-prose-invert-pre-bg": "transparent",
            "--tw-prose-pre-code": "inherit",
            "--tw-prose-invert-pre-code": "inherit",
          },
        },
        zinc: {
          css: {
            "--tw-prose-pre-bg": "transparent",
            "--tw-prose-invert-pre-bg": "transparent",
            "--tw-prose-pre-code": "inherit",
            "--tw-prose-invert-pre-code": "inherit",
          },
        },
      }),
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
    require("@tailwindcss/forms"),
    require("@tailwindcss/container-queries"),
  ],
  variants: {
    extend: {
      opacity: ["group-hover"],
    },
  },
  // Force tw to include generated prompt variant color classes
  safelist: [
    {
      pattern:
        /bg-(blue|pink|green|purple|orange|teal|red|emerald)-(100|400|900)/,
      variants: ["dark"],
    },
    {
      pattern:
        /text-(blue|pink|green|purple|orange|teal|red|emerald)-(200|700)/,
      variants: ["dark"],
    },
  ],
};
