import {
  type LoadedBtSessionToken,
  sessionFetchProps,
} from "#/utils/auth/session-token";

export type AutomationTestResponse =
  | { kind: "success"; payload: Record<string, unknown> }
  | { kind: "error"; message: string };

export async function performAutomationTest({
  apiUrl,
  sessionToken,
  projectId,
  row,
  errorContext = "automation",
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  projectId: string;
  row: Record<string, unknown>;
  errorContext?: string;
}): Promise<AutomationTestResponse> {
  const resp = await (() => {
    const { sessionHeaders, sessionExtraFetchProps } =
      sessionFetchProps(sessionToken);
    return fetch(`${apiUrl}/test-automation`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...sessionHeaders,
      },
      body: JSON.stringify({
        project_id: projectId,
        ...row,
      }),
      ...sessionExtraFetchProps,
    });
  })();

  if (resp.ok) {
    return await resp.json();
  } else {
    return {
      kind: "error",
      message: `Error while testing ${errorContext}: ${await resp.text()}`,
    };
  }
}
