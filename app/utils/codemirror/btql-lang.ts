import { type Extension } from "@codemirror/state";
import { linter, type Diagnostic } from "@codemirror/lint";
import {
  autocompletion,
  type CompletionContext,
  type CompletionResult,
  type Completion,
  startCompletion,
} from "@codemirror/autocomplete";
import { StreamLanguage } from "@codemirror/language";
import {
  ParserError as ActualParserError,
  Tokenizer,
  TokenizerError as ActualTokenizerError,
  type TokenInfo as BTQLTokenInfo,
  reservedWords as btqlReservedWords,
  keywords as btqlOtherKeywords,
  parseQuery,
  parseExpr,
} from "@braintrust/btql/parser";

import { FUNCTION_NAMES } from "@braintrust/btql/binder";
import { singleQuote } from "#/utils/sql-utils";
import { type EditorView } from "@codemirror/view";
import { isDataObjectType, type DataObjectType } from "#/utils/btapi/btapi";
import { dataObjectTypes } from "#/utils/btapi/load-bt-cache-db";

// Old ProjectInfo might be replaced or augmented by SearchableItemInfo
// export interface ProjectInfo { name: string; id: string; description?: string; }

export interface SearchableItemInfo {
  id: string;
  name: string;
  type:
    | "project"
    | "dataset"
    | "experiment"
    | "playground_session"
    | "org"
    | "prompt_session";
  description?: string;
  projectId?: string; // For items within a project
  projectName?: string; // For display
}

// Functions that will trigger contextual item search (datasets, experiments, etc.)
export const contextualSearchFunctions = [
  "project_logs" as const, // Technically searches for projects
  "dataset" as const,
  "experiment" as const,
  "playground_logs" as const,
  "org_prompts" as const, // searches for orgs
  "org_functions" as const, // searches for orgs
  "project_functions" as const, // searches for projects
  "project_prompts" as const, // searches for projects
  "prompt_session" as const,
];

export type ContextualSearchFunction =
  (typeof contextualSearchFunctions)[number];

const allBtqlKeywordsAndFunctions = [
  ...btqlReservedWords,
  ...btqlOtherKeywords,
  ...dataObjectTypes,
  ...FUNCTION_NAMES,
];

const btqlKeywords = allBtqlKeywordsAndFunctions;
const btqlFunctionNamesSet = new Set<string>(FUNCTION_NAMES);

// --- BTQL Tokenizer for StreamLanguage ---
// Mapping from your lexer token types to CodeMirror style tags
// Note: Adjust these based on your Tokenizer's actual output and desired highlighting
const tokenTypeToStyleTag: Partial<Record<string, string>> = {
  // Keywords and Reserved Words
  true: "keyword.literal.boolean", // More specific tag
  false: "keyword.literal.boolean",
  null: "keyword.literal.null", // More specific tag
  filter: "keyword.control",
  pivot: "keyword.control",
  unpivot: "keyword.control",
  dimensions: "keyword.control",
  measures: "keyword.control",
  select: "keyword.control",
  infer: "keyword.control",
  from: "keyword.control",
  sort: "keyword.control",
  limit: "keyword.control",
  cursor: "keyword.control",
  comparison_key: "keyword",
  weighted_scores: "keyword",
  custom_columns: "keyword",
  preview_length: "keyword",
  as: "keyword.operator",
  and: "keyword.operator.logic",
  or: "keyword.operator.logic",
  not: "keyword.operator.unary",
  is: "keyword.operator.comparison",
  like: "keyword.operator.comparison",
  ilike: "keyword.operator.comparison",
  includes: "keyword.operator.comparison",
  contains: "keyword.operator.comparison",
  match: "keyword.operator.comparison",
  interval: "keyword",
  asc: "keyword.modifier",
  desc: "keyword.modifier",
  year: "keyword.unit",
  month: "keyword.unit",
  day: "keyword.unit",
  hour: "keyword.unit",
  minute: "keyword.unit",
  second: "keyword.unit",
  millisecond: "keyword.unit",
  microsecond: "keyword.unit",
  spans: "keyword.type",
  traces: "keyword.type",

  // Punctuation & Operators
  lcurly: "punctuation.brace",
  rcurly: "punctuation.brace",
  lsquare: "punctuation.bracket",
  rsquare: "punctuation.bracket",
  lparen: "punctuation.parenthesis",
  rparen: "punctuation.parenthesis",
  comma: "punctuation.separator",
  colon: "punctuation.separator",
  period: "operator.accessor", // or just "operator"
  equalOp: "operator.comparison",
  notEqualOp: "operator.comparison",
  doubleAngle: "operator.comparison",
  lessThanOrEqual: "operator.comparison",
  lessThan: "operator.comparison",
  greaterThanOrEqual: "operator.comparison",
  greaterThan: "operator.comparison",
  plus: "operator.arithmetic",
  minus: "operator.arithmetic",
  star: "operator.arithmetic", // Could also be a wildcard in SELECT *
  slash: "operator.arithmetic",
  percent: "operator.arithmetic",
  pipe: "operator",
  question: "operator",

  // Literals
  word: "variableName", // Default for words; functions will be handled separately if possible
  singleQuotedString: "string.single",
  doubleQuotedString: "string.double",
  backtickQuotedString: "string.template", // Or just "string"
  percentNumber: "number.percentage",
  number: "number",

  // Comments (handled by the tokenizer logic in StreamLanguage)
  slashStar: "comment.block",
  doubleSlash: "comment.line",
  doubleDash: "comment.line",
  // whiteSpace: null, // Typically ignored by StreamLanguage token method
};

const btqlLanguage = StreamLanguage.define<{
  tokenizer: Tokenizer | null;
  currentLineString: string;
}>({
  startState: () => ({
    tokenizer: null,
    currentLineString: "",
  }),
  token: (stream, state) => {
    if (stream.pos === 0 || !state.tokenizer) {
      // We need to re-initialize tokenizer for each line for StreamLanguage
      // or find a way to make the tokenizer work across multiple calls to `token`
      // For simplicity with the current Tokenizer, let's re-tokenize the line
      // This assumes your Tokenizer can handle partial input or be reset easily.
      // A more robust solution might involve adapting the Tokenizer or using a Lezer grammar.
      state.currentLineString = stream.string.slice(stream.start);
      state.tokenizer = new Tokenizer(state.currentLineString);
    }

    if (stream.eol()) {
      state.tokenizer = null; // Reset for next line or if stream is truly done
      return null;
    }

    // Align stream position with tokenizer's internal cursor if necessary.
    // This is tricky. StreamLanguage expects `token` to consume from `stream`.
    // Your current Tokenizer processes the whole string given to it.

    // We need to make the Tokenizer consume from the stream's current position.
    // A temporary workaround: advance the tokenizer to the stream's current relative position
    // This is inefficient as it re-scans from the beginning of the line up to stream.pos
    // for each token.
    let tempTokenizer = new Tokenizer(state.currentLineString);
    let currentTokenInfo = null;

    // Simulate advancing the tokenizer to the stream's current position within the line
    // The stream.pos is relative to the start of the current line being processed by token().
    // state.currentLineString is the full current line.
    // The tokenizer needs to effectively start from stream.pos within state.currentLineString.

    // Simplified approach: Get the next token based on the remainder of the stream
    const streamRemainder = stream.string.slice(stream.pos);
    if (!streamRemainder.trim()) {
      stream.skipToEnd();
      return null; // Consumed whitespace
    }

    tempTokenizer = new Tokenizer(streamRemainder);

    try {
      currentTokenInfo = tempTokenizer.nextToken();
    } catch (e) {
      if (e instanceof ActualTokenizerError) {
        // Lexer error (ActualTokenizerError)
        stream.next(); // Consume one char to avoid infinite loop
        return "invalid";
      }
      // For other types of errors, we might want to rethrow or log differently,
      // but for now, treat as generic lexer error for tokenization.
      console.error("BTQL Tokenizer: Unexpected error:", e);
      stream.next(); // Consume one char to avoid infinite loop
      return "invalid";
    }

    if (currentTokenInfo) {
      // Consume the token in the CodeMirror stream
      for (let i = 0; i < currentTokenInfo.value.length + 1; i++) {
        stream.next();
      }

      let style = tokenTypeToStyleTag[currentTokenInfo.type];

      // Special handling for function calls if `word` is a function name
      if (
        currentTokenInfo.type === "word" &&
        btqlFunctionNamesSet.has(currentTokenInfo.value)
      ) {
        // Peek ahead for '('
        // This is a simplified check. A proper grammar would handle this better.
        if (stream.match("(", false) || stream.match(/^\s*\(/, false)) {
          // match, no consume
          style = "functionName";
        }
      }

      // If the token is a reserved word or keyword, ensure it gets a keyword style
      // This overrides the default 'variableName' for 'word' if it's a keyword.
      if (
        currentTokenInfo.type === "word" &&
        allBtqlKeywordsAndFunctions
          .map((k) => k.toLowerCase())
          .includes(currentTokenInfo.value.toLowerCase())
      ) {
        style =
          tokenTypeToStyleTag[currentTokenInfo.value.toLowerCase()] ||
          "keyword";
      }

      return style || null;
    } else {
      // No token found, but not at end of line (e.g. could be leading/trailing whitespace not handled by tokenizer.nextToken)
      // or an unhandled sequence by the tokenizer itself.
      if (!stream.eol()) {
        stream.next(); // Consume one char to avoid infinite loop on unrecognized input
      }
      return null; // Or a generic error tag like "invalid"
    }
  },
});

// --- BTQL Linter ---
const btqlLinter = (
  mode: BtqlMode,
  onFixWithLoop?: (message: string) => void,
) =>
  linter((view) => {
    const diagnostics: Diagnostic[] = [];
    const { state } = view;
    const code = state.doc.toString();

    if (code.trim() === "") {
      return diagnostics; // No errors if the editor is empty
    }

    try {
      if (mode === "query") {
        parseQuery(code);
      } else {
        parseExpr(code);
      }
    } catch (e: unknown) {
      // Check if the error is an instance of your ParserError
      // This check relies on 'ActualParserError' being the constructor of your error class.
      if (e instanceof ActualParserError) {
        const err = e; // Type cast for easier property access

        let from = 0;
        let to = 0;
        let offendingTokenLength = 1; // Default length

        try {
          // ParserError line and col are 1-indexed
          const line = state.doc.line(err.line);
          from = line.from + err.col - 1;

          // Attempt to determine the length of the offending token
          // For better accuracy, consider modifying your ParserError to include the offending token's text or length.
          // The 'fail' method in your parser has access to 'currentToken.value'.
          // For now, we try to extract it from the error's suffix property if structured predictably,
          // or default to a length of 1.
          if (err.suffix) {
            // Example: suffix might be "at 'TOKEN' near..."
            const match = err.suffix.match(/^at\s*'([^']+)'/);
            if (match && match[1]) {
              offendingTokenLength = match[1].length;
            } else {
              // Fallback: if suffix is just the token, e.g. from currentToken().value
              const firstWordMatch = err.suffix.split(
                /\s|\(|\[|\]|\.|,|;|'|"|=|>|<|!/,
              )[0];
              if (firstWordMatch) {
                offendingTokenLength = firstWordMatch.length;
              }
            }
          }

          to = from + Math.max(1, offendingTokenLength);

          // Ensure 'to' does not exceed document length or 'from'
          if (to > state.doc.length) to = state.doc.length;
          if (from >= to && from > 0) from = to - 1;
          if (from < 0) from = 0;
        } catch (lineError) {
          // Fallback if line number is out of bounds or other error during position calculation
          from = 0;
          to = Math.min(1, state.doc.length); // Highlight first char or empty doc
          console.error(
            "BTQL Linter: Error resolving diagnostic position:",
            lineError,
          );
        }

        const diagnostic: Diagnostic = {
          from,
          to,
          severity: "error",
          message: err.message,
          source: "BTQL parser",
        };

        diagnostics.push(diagnostic);
      } else if (e instanceof ActualTokenizerError) {
        const err = e; // Type cast for easier property access

        let from = 0;
        let to = 0;

        try {
          // TokenizerError line and col are 1-indexed
          const line = state.doc.line(err.line);
          from = line.from + err.col - 1;
          // For tokenizer errors, the error often points to a single character or position.
          // We'll highlight a single character at the error location.
          to = from + 1;

          // Ensure 'to' does not exceed document length or 'from'
          if (to > state.doc.length) to = state.doc.length;
          if (from >= to && from > 0) from = to - 1; // Should not happen if to = from + 1 initially
          if (from < 0) from = 0;
        } catch (lineError) {
          // Fallback if line number is out of bounds or other error during position calculation
          from = 0;
          to = Math.min(1, state.doc.length); // Highlight first char or empty doc
          console.error(
            "BTQL linter (tokenizer): Error resolving diagnostic position:",
            lineError,
          );
        }

        const diagnostic: Diagnostic = {
          from,
          to,
          severity: "error",
          message: err.message, // TokenizerError has a 'msg' property, but its constructor sets 'message'
          source: "BTQL tokenizer",
        };
        diagnostics.push(diagnostic);
      } else {
        // Log unexpected errors if they are not from the BTQL parser or tokenizer
        console.error("BTQL linter unexpected error:", e);
      }
    }
    return onFixWithLoop
      ? diagnostics.map((d) => ({
          ...d,
          actions: [
            {
              name: "Fix with Loop",
              apply: () =>
                onFixWithLoop(
                  `For the following BTQL query: \n\n${code}\n\n Here is the ${d.source} error: \n\n${d.message}\n\nPlease look at the query and the error and fix the issue. Be concise and to the point.`,
                ),
            },
          ],
        }))
      : diagnostics;
  });

export type BtqlMode = "query" | "expr";

export function btqlSupport({
  mode,
  searchItems,
  onFixWithLoop,
}: {
  mode: BtqlMode;
  searchItems?: (context: {
    objectType: DataObjectType;
    currentPrefix: string;
    projectScope?: string; // Project ID or name, if provided in syntax like datasets(project_scope/item_prefix)
  }) => Promise<SearchableItemInfo[]>;
  onFixWithLoop?: (message: string) => void;
}): Extension[] {
  const extensions: Extension[] = [
    btqlLanguage,
    btqlLinter(mode, onFixWithLoop),
    autocompletion({
      activateOnTyping: true,
      override: [
        async (
          context: CompletionContext,
        ): Promise<CompletionResult | null> => {
          const textBeforeGlobalCursor = context.state.doc.sliceString(
            Math.max(0, context.pos - 100), // Look back further for complex patterns
            context.pos,
          );

          // Context 1: Inside specific function calls for item search (project_logs, datasets, experiments, etc.)
          // Regex: func_name( [project_scope /] item_prefix
          const itemSearchContextRegex = new RegExp(
            `(${contextualSearchFunctions.join("|")})\\(\\s*(?:([\\w.-]+)\\s*\\/)?\\s*([\\w.-]*)$`,
            "i",
          );
          const itemSearchContextMatch = textBeforeGlobalCursor.match(
            itemSearchContextRegex,
          );

          if (itemSearchContextMatch && searchItems) {
            const objectType = itemSearchContextMatch[1]; // e.g., "datasets"
            const projectScope = itemSearchContextMatch[2]; // Optional: e.g., "my_project_id" or "My Project Name"
            const currentItemPrefix = itemSearchContextMatch[3] || ""; // Prefix for the item itself

            if (!isDataObjectType(objectType)) {
              return null;
            }

            try {
              const items = await searchItems({
                objectType,
                currentPrefix: currentItemPrefix,
                projectScope,
              });

              if (items.length > 0) {
                const suggestions: Completion[] = items.map((item) => {
                  let label = item.name;
                  if (item.projectName && item.type !== "project") {
                    label = `${item.projectName} / ${item.name}`;
                  }
                  return {
                    label,
                    apply: singleQuote(item.id),
                    type: item.type === "project" ? "class" : "interface", // Adjust based on item.type
                    detail: `${item.type.charAt(0).toUpperCase() + item.type.slice(1)} ID: ${item.id}${item.description ? " - " + item.description : ""}`,
                    boost: item.type === "project" ? 10 : 5,
                  };
                });

                // Calculate `from` based on the full match (func_name + project_scope + item_prefix)
                const lengthOfTypedArgPart =
                  itemSearchContextMatch[0].length - (objectType.length + 1); // Length of "(project_scope/item_prefix" or "(item_prefix"
                return {
                  from: context.pos - lengthOfTypedArgPart,
                  options: suggestions,
                  validFor: /^[\w./-]*$/, // Valid for paths and identifiers
                };
              }
            } catch (err) {
              console.error("Error during contextual item search:", err);
              return null; // Don't block other autocompletion if search fails
            }
            // If searchItems was called but returned no items, or if there was an error,
            // we might want to fall through or return null. For now, return null to avoid keyword collision.
            return null;
          }

          // Context 2: After FROM (using tokenizer) - This logic remains mostly the same
          const currentWordMatchAfterFrom = context.matchBefore(/\w*$/);
          if (currentWordMatchAfterFrom) {
            const line = context.state.doc.lineAt(
              currentWordMatchAfterFrom.from,
            );
            const lineTextUptoWordStart = line.text.slice(
              0,
              currentWordMatchAfterFrom.from - line.from,
            );
            let isAfterFromContext = false;
            try {
              const tokenizer = new Tokenizer(
                lineTextUptoWordStart.trimRight(),
              );
              const tokensOnLine: BTQLTokenInfo[] = [];
              let token;
              while ((token = tokenizer.nextToken())) {
                tokensOnLine.push(token);
              }
              if (tokensOnLine.length > 0) {
                const lastToken = tokensOnLine[tokensOnLine.length - 1];
                if (lastToken.type.toLowerCase() === "from")
                  isAfterFromContext = true;
                else if (
                  lastToken.type === "colon" &&
                  tokensOnLine.length > 1 &&
                  tokensOnLine[tokensOnLine.length - 2].type.toLowerCase() ===
                    "from"
                )
                  isAfterFromContext = true;
              }
            } catch (e) {
              /* Tokenizer error, ignore */
            }

            if (isAfterFromContext) {
              const typedPrefix = currentWordMatchAfterFrom.text.toLowerCase();
              const suggestions = dataObjectTypes
                .filter((objType) =>
                  objType.toLowerCase().startsWith(typedPrefix),
                )
                .map((objType) => {
                  const shouldAddParens =
                    btqlFunctionNamesSet.has(objType) ||
                    objType.endsWith("_logs") ||
                    objType.endsWith("_functions");

                  return {
                    label: objType,
                    type: "type",
                    apply: (
                      view: EditorView,
                      completion: Completion,
                      from: number,
                      to: number,
                    ) => {
                      let textToApply = objType;
                      // Default cursor position is after the inserted object type
                      let finalCursorPos = from + objType.length;

                      if (shouldAddParens) {
                        textToApply += "()";
                        // Set cursor position inside the parentheses
                        // e.g., objType = "func", textToApply = "func()"
                        // cursor should be at from + "func".length + "(".length
                        finalCursorPos = from + objType.length + 1;
                      }

                      view.dispatch({
                        changes: { from, to, insert: textToApply },
                        selection: { anchor: finalCursorPos },
                      });
                      if (shouldAddParens) {
                        startCompletion(view);
                      }
                    },
                    boost: 15,
                  };
                });
              if (suggestions.length > 0)
                return {
                  from: currentWordMatchAfterFrom.from,
                  options: suggestions,
                  validFor: /^\w*$/,
                };
            }
          }

          // Context 3: Fallback to general keyword and function completion
          const fallbackWordMatch = context.matchBefore(/\w*$/);
          if (
            !fallbackWordMatch ||
            (fallbackWordMatch.from === fallbackWordMatch.to &&
              !context.explicit)
          ) {
            return null;
          }
          const currentWord = fallbackWordMatch.text.toLowerCase();
          const keywordSuggestions = btqlKeywords
            .filter((kw) => kw.toLowerCase().startsWith(currentWord))
            .map((kw) => ({
              label: kw,
              type: btqlFunctionNamesSet.has(kw)
                ? "function"
                : // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                  (dataObjectTypes as readonly string[]).includes(kw)
                  ? "type"
                  : "keyword",
              apply: kw + (btqlFunctionNamesSet.has(kw) ? "()" : ""),
            }));

          if (keywordSuggestions.length > 0)
            return {
              from: fallbackWordMatch.from,
              options: keywordSuggestions,
              validFor: /^\w*$/,
            };
          return null;
        },
      ],
    }),
  ];
  return extensions;
}
