@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --max-width: 1100px;
  --border-radius: 12px;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(
    #00000080,
    #00000040,
    #00000030,
    #00000020,
    #00000010,
    #00000010,
    #00000080
  );

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
  --primary-body: #222;
  --background: white;
  --destructive: 235, 66, 0;
  --destructive-foreground: 210 40% 98%;
  --muted: #f5f5f5;
  --muted-foreground: #555;
  --popover: var(--background);
  --popover-foreground: var(--foreground-rgb);

  --zinc-50: 250 250 250;
  --zinc-100: 244 244 245;
  --zinc-200: 228 228 231;
  --zinc-300: 212 212 216;
  --zinc-400: 159 159 169;
  --zinc-500: 113 113 123;
  --zinc-600: 82 82 92;
  --zinc-700: 63 63 70;
  --zinc-800: 39 39 42;
  --zinc-900: 24 24 27;
  --zinc-950: 9 9 11;

  --blue-50: 239 244 255;
  --blue-100: 225 234 255;
  --blue-200: 199 215 255;
  --blue-300: 158 182 255;
  --blue-400: 126 149 252;
  --blue-500: 87 112 255;
  --blue-600: 58 74 248;
  --blue-700: 43 30 235;
  --blue-800: 30 33 210;
  --blue-900: 32 30 159;
  --blue-950: 24 24 99;

  --violet-50: 246 243 255;
  --violet-100: 239 233 254;
  --violet-200: 225 214 254;
  --violet-300: 205 181 253;
  --violet-400: 184 138 250;
  --violet-500: 158 96 246;
  --violet-600: 133 55 235;
  --violet-700: 116 38 217;
  --violet-800: 94 28 181;
  --violet-900: 78 24 145;
  --violet-950: 48 15 97;

  --green-50: 247 254 231;
  --green-100: 236 252 203;
  --green-200: 217 249 157;
  --green-300: 190 242 100;
  --green-400: 163 230 53;
  --green-500: 132 204 22;
  --green-600: 101 163 13;
  --green-700: 77 124 15;
  --green-800: 63 98 18;
  --green-900: 54 83 20;
  --green-950: 26 46 5;

  --pink-50: 255 244 255;
  --pink-100: 255 232 255;
  --pink-200: 254 208 254;
  --pink-300: 252 171 252;
  --pink-400: 249 121 249;
  --pink-500: 239 70 239;
  --pink-600: 211 38 211;
  --pink-700: 175 28 175;
  --pink-800: 143 25 143;
  --pink-900: 117 26 117;
  --pink-950: 78 4 78;

  --cyan-50: 236 255 255;
  --cyan-100: 207 254 254;
  --cyan-200: 161 252 252;
  --cyan-300: 98 249 249;
  --cyan-400: 40 232 232;
  --cyan-500: 15 203 203;
  --cyan-600: 16 160 170;
  --cyan-700: 18 122 140;
  --cyan-800: 21 99 117;
  --cyan-900: 22 80 99;
  --cyan-950: 8 51 68;

  --orange-50: 255 247 237;
  --orange-100: 255 237 213;
  --orange-200: 254 215 170;
  --orange-300: 253 186 116;
  --orange-400: 251 146 60;
  --orange-500: 249 115 22;
  --orange-600: 234 88 12;
  --orange-700: 194 65 12;
  --orange-800: 154 52 18;
  --orange-900: 124 45 18;
  --orange-950: 67 20 7;

  --red-50: 254 242 245;
  --red-100: 254 226 232;
  --red-200: 254 202 213;
  --red-300: 252 165 184;
  --red-400: 248 113 142;
  --red-500: 239 68 105;
  --red-600: 220 38 77;
  --red-700: 185 28 62;
  --red-800: 153 27 54;
  --red-900: 127 29 50;
  --red-950: 69 10 23;

  --emerald-50: 236 253 245;
  --emerald-100: 208 250 229;
  --emerald-200: 164 244 207;
  --emerald-300: 94 233 181;
  --emerald-400: 0 212 146;
  --emerald-500: 0 188 125;
  --emerald-600: 0 153 102;
  --emerald-700: 0 122 85;
  --emerald-800: 0 96 69;
  --emerald-900: 0 79 59;
  --emerald-950: 0 44 34;

  --primary-50: var(--zinc-50);
  --primary-100: var(--zinc-100);
  --primary-200: var(--zinc-200);
  --primary-300: var(--zinc-300);
  --primary-400: var(--zinc-400);
  --primary-500: var(--zinc-500);
  --primary-600: var(--zinc-600);
  --primary-700: var(--zinc-700);
  --primary-800: var(--zinc-800);
  --primary-900: var(--zinc-900);
  --primary-950: var(--zinc-950);

  --accent-50: var(--blue-50);
  --accent-100: var(--blue-100);
  --accent-200: var(--blue-200);
  --accent-300: var(--blue-300);
  --accent-400: var(--blue-400);
  --accent-500: var(--blue-500);
  --accent-600: var(--blue-600);
  --accent-700: var(--blue-700);
  --accent-800: var(--blue-800);
  --accent-900: var(--blue-900);
  --accent-950: var(--blue-950);

  --comparison-50: var(--violet-50);
  --comparison-100: var(--violet-100);
  --comparison-200: var(--violet-200);
  --comparison-300: var(--violet-300);
  --comparison-400: var(--violet-400);
  --comparison-500: var(--violet-500);
  --comparison-600: var(--violet-600);
  --comparison-700: var(--violet-700);
  --comparison-800: var(--violet-800);
  --comparison-900: var(--violet-900);
  --comparison-950: var(--violet-950);

  --good-50: var(--emerald-50);
  --good-100: var(--emerald-100);
  --good-200: var(--emerald-200);
  --good-300: var(--emerald-300);
  --good-400: var(--emerald-400);
  --good-500: var(--emerald-500);
  --good-600: var(--emerald-600);
  --good-700: var(--emerald-700);
  --good-800: var(--emerald-800);
  --good-900: var(--emerald-900);
  --good-950: var(--emerald-950);

  --bad-50: var(--red-50);
  --bad-100: var(--red-100);
  --bad-200: var(--red-200);
  --bad-300: var(--red-300);
  --bad-400: var(--red-400);
  --bad-500: var(--red-500);
  --bad-600: var(--red-600);
  --bad-700: var(--red-700);
  --bad-800: var(--red-800);
  --bad-900: var(--red-900);
  --bad-950: var(--red-950);
}

html.dark {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;

  --primary-glow: radial-gradient(rgba(1, 65, 255, 0.4), rgba(1, 65, 255, 0));
  --secondary-glow: linear-gradient(
    to bottom right,
    rgba(1, 65, 255, 0),
    rgba(1, 65, 255, 0),
    rgba(1, 65, 255, 0.3)
  );

  --tile-start-rgb: 2, 13, 46;
  --tile-end-rgb: 2, 5, 19;
  --tile-border: conic-gradient(
    #ffffff80,
    #ffffff40,
    #ffffff30,
    #ffffff20,
    #ffffff10,
    #ffffff10,
    #ffffff80
  );

  --callout-rgb: 20, 20, 20;
  --callout-border-rgb: 108, 108, 108;
  --card-rgb: 100, 100, 100;
  --card-border-rgb: 200, 200, 200;
  --background: #000;
  --destructive: 235, 66, 0;
  --destructive-foreground: 210 40% 98%;
  --muted: #393939;
  --muted-foreground: #aaa;
  --popover: var(--background);
  --popover-foreground: var(--foreground-rgb);

  --primary-50: var(--zinc-950);
  --primary-100: var(--zinc-900);
  --primary-200: var(--zinc-800);
  --primary-300: var(--zinc-700);
  --primary-400: var(--zinc-600);
  --primary-500: var(--zinc-500);
  --primary-600: var(--zinc-400);
  --primary-700: var(--zinc-300);
  --primary-800: var(--zinc-200);
  --primary-900: var(--zinc-100);
  --primary-950: var(--zinc-50);

  --accent-50: var(--blue-950);
  --accent-100: var(--blue-900);
  --accent-200: var(--blue-800);
  --accent-300: var(--blue-700);
  --accent-400: var(--blue-600);
  --accent-500: var(--blue-500);
  --accent-600: var(--blue-400);
  --accent-700: var(--blue-300);
  --accent-800: var(--blue-200);
  --accent-900: var(--blue-100);
  --accent-950: var(--blue-50);

  --comparison-50: var(--violet-950);
  --comparison-100: var(--violet-900);
  --comparison-200: var(--violet-800);
  --comparison-300: var(--violet-700);
  --comparison-400: var(--violet-600);
  --comparison-500: var(--violet-500);
  --comparison-600: var(--violet-400);
  --comparison-700: var(--violet-300);
  --comparison-800: var(--violet-200);
  --comparison-900: var(--violet-100);
  --comparison-950: var(--violet-50);

  --good-50: var(--emerald-950);
  --good-100: var(--emerald-900);
  --good-200: var(--emerald-800);
  --good-300: var(--emerald-700);
  --good-400: var(--emerald-600);
  --good-500: var(--emerald-500);
  --good-600: var(--emerald-400);
  --good-700: var(--emerald-300);
  --good-800: var(--emerald-200);
  --good-900: var(--emerald-100);
  --good-950: var(--emerald-50);

  --bad-50: var(--red-950);
  --bad-100: var(--red-900);
  --bad-200: var(--red-800);
  --bad-300: var(--red-700);
  --bad-400: var(--red-600);
  --bad-500: var(--red-500);
  --bad-600: var(--red-400);
  --bad-700: var(--red-300);
  --bad-800: var(--red-200);
  --bad-900: var(--red-100);
  --bad-950: var(--red-50);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  @apply font-inter bg-background;
  overscroll-behavior: none;
}

a {
  color: inherit;
  text-decoration: none;
}

html.dark {
  color-scheme: dark;
  color: #ededed;

  body {
    color-scheme: dark;
    color: #ededed;
  }
}

@layer base {
  *,
  ::before,
  ::after {
    @apply border-primary-200;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* This corresponds to our use of the react-image-crop
 * library, and it prevents a big blue outline from appearing
  * when the crop selection is focused.
  */
.ReactCrop__crop-selection:focus {
  outline: none !important;
}

.prose h1 > a,
.prose h2 > a,
.prose h3 > a,
.prose h4 > a,
.prose h5 > a,
.prose h6 > a {
  text-decoration: none !important;
  font-weight: 600 !important;
}

.prose li::before {
  flex: none;
}

.prose li > p:only-child {
  margin: 0;
  display: inline;
}

.blog-article .prose h1 > a {
  font-weight: 400 !important;
}

.shiki,
.shiki span {
  background-color: transparent !important;
}
pre.shiki {
  white-space: pre-wrap;
}

.shiki .line.highlighted {
  @apply !bg-orange-50 dark:!bg-orange-950/30;
}

html.dark .shiki:not(.disable-shiki-dark-override),
html.dark .shiki:not(.disable-shiki-dark-override) span {
  color: var(--shiki-dark) !important;
  background-color: transparent !important;
  /* Optional, if you also want font styles */
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

.dark .customer-logos {
  opacity: 0.8;
}
.dark .customer-logos svg * {
  fill: currentColor !important;
  opacity: 1 !important;
}

.cursor-grabbing-global * {
  cursor: grabbing !important;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(3px);
  }
  75% {
    transform: translateX(-3px);
  }
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 25s linear infinite;
}

.cl-formFieldInput {
  @apply placeholder:text-primary-500;
}
.cl-footerActionText,
.cl-formFieldInfoText,
.cl-formFieldSuccessText,
.cl-identityPreviewText {
  @apply text-primary-500;
}
