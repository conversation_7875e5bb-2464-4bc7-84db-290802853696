import { DocsPage, DocsBody } from "fumadocs-ui/page";
import { notFound } from "next/navigation";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { getPage, getPages, openapi } from "../source";
import Footer from "#/ui/landing/footer";
import { Breadcrumb } from "./Breadcrumb";
import { buildMetadata } from "#/app/metadata";
import { type TableOfContents } from "fumadocs-core/server";
import { z } from "zod";
import { LLMCopyButton } from "./LLMCopyButton";
import { StructuredData } from "#/ui/structured-data";
import { generateBreadcrumbSchema } from "#/lib/structured-data";

const fumadocsTableOfContentsSchema = z.array(
  z.object({
    depth: z.number(),
    title: z.string(),
    url: z.string(),
  }),
);
// Enforces that the zod type matches `TableOfContents` from fumadocs or we get
// a TypeScript compiler error. https://stackoverflow.com/a/72945564
type IsExtends<TNarrow, TGeneral> = [TNarrow] extends [TGeneral] ? true : false;
type StaticAssert<T extends true> = T;
export type _TestTocSchemaCompatibility = StaticAssert<
  IsExtends<z.infer<typeof fumadocsTableOfContentsSchema>, TableOfContents>
>;

const fumadocsFrontmatterSchema = z.object({
  _openapi: z.object({
    toc: fumadocsTableOfContentsSchema,
  }),
});

export default async function Page(props0: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props0.params;
  const page = getPage(params.slug);
  if (!page) {
    notFound();
  }

  // fumadocs-openapi 5.0 and later renders the API documentation on the server
  // side. `page.data.toc` will be an empty list for those MDX files. This hack
  // allows us to access the precomputed TOC in the MDX frontmatter.
  const openApiToc = fumadocsFrontmatterSchema.safeParse(page.data);

  const toc = openApiToc.success ? openApiToc.data._openapi.toc : page.data.toc;
  const tocOptions = {
    enabled: page.file.name !== "index" && toc.length > 0,
  };

  // Generate breadcrumbs for structured data
  const breadcrumbs = [
    { name: "Home", url: "https://www.braintrust.dev" },
    { name: "Docs", url: "https://www.braintrust.dev/docs" },
    ...(params.slug
      ? params.slug.map((segment, index) => ({
          name:
            segment.charAt(0).toUpperCase() +
            segment.slice(1).replace(/-/g, " "),
          url: `https://www.braintrust.dev/docs/${params.slug ? params.slug.slice(0, index + 1).join("/") : ""}`,
        }))
      : []),
  ];

  return (
    <div className={cn("docs-content flex flex-1 min-w-0", page.file.path)}>
      <StructuredData data={generateBreadcrumbSchema(breadcrumbs)} />
      <DocsPage
        toc={toc}
        full={page.data.full}
        tableOfContent={tocOptions}
        tableOfContentPopover={tocOptions}
        breadcrumb={{
          enabled: true,
          component: (
            <div className="flex items-baseline justify-between gap-4">
              <Breadcrumb full={page.data.full} />
              <LLMCopyButton path={page.file.path} />
            </div>
          ),
        }}
        footer={{
          enabled: true,
          component: (
            <Footer
              inDocs
              className="border-t border-primary-100 pb-8 pt-6 text-sm text-primary-600"
            />
          ),
        }}
      >
        <DocsBody
          className={cn(
            proseMDXBaseClassName,
            "leading-[1.8] prose-headings:font-bold prose-headings:leading-tight prose-headings:font-display prose-h1:text-5xl prose-h2:text-4xl prose-h3:text-3xl prose-h4:text-2xl prose-h5:text-xl prose-h6:text-lg font-inter prose-lg prose-h2:mb-6 prose-h3:mb-4 prose-h4:mb-2 prose-h5:mb-2 prose-h6:mb-2",
          )}
        >
          <page.data.body
            components={{
              ...defaultMdxComponents,
              APIPage: openapi.APIPage,
            }}
          />
        </DocsBody>
      </DocsPage>
    </div>
  );
}

export async function generateStaticParams() {
  return getPages().map((page) => ({
    slug: page.slugs,
  }));
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const page = getPage(params.slug);
  const slug = params.slug;

  if (!page) notFound();

  // metaTitle can be used to set a different meta title than what shows
  // up in docs sidebar/breadcrumbs
  const title = page.data.metaTitle ?? page.data.title;

  // Generate relevant tags based on the page path and title
  const tags = [
    "documentation",
    "AI documentation",
    "LLM documentation",
    "developer docs",
  ];

  const sections = ["Docs"];

  if (slug && slug.length > 0) {
    if (slug.includes("start")) {
      tags.push("getting started", "tutorial");
      sections.push("Start");
    }
    if (slug.includes("guides")) {
      tags.push("guides", "how-to");
      sections.push("Guides");
    }
    if (slug.includes("reference")) {
      tags.push("API reference", "reference");
      sections.push("Reference");
    }
    if (slug.includes("evaluation") || slug.includes("eval"))
      tags.push("AI evaluation", "LLM evaluation");
    if (slug.includes("monitor"))
      tags.push("monitoring", "observability", "AI observability", "llm ops");
    if (slug.includes("logs")) {
      tags.push(
        "logging",
        "debugging",
        "llm logs",
        "observability",
        "AI observability",
      );
    }
    if (slug.includes("functions")) {
      tags.push("functions", "prompts", "tools");
      sections.push("Functions");
    }
    if (slug.includes("datasets")) {
      tags.push("datasets", "data management");
      sections.push("Datasets");
    }
    if (slug.includes("experiments")) {
      tags.push("experiments", "A/B testing");
      sections.push("Experiments");
    }
  }

  return buildMetadata({
    title,
    sections,
    description: slug
      ? page.data.description
      : "Comprehensive documentation for Braintrust - the end-to-end platform for building, evaluating, and monitoring AI applications. Learn how to implement AI evaluation, observability, and debugging.",
    relativeUrl: `/docs/${params.slug?.join("/") ?? ""}`,
    tags,
    ogTemplate: "docs",
  });
}
