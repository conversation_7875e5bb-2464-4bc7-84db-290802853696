"use client";
import { PageTracker } from "#/ui/use-analytics";
import { CustomerLogos } from "./customer-logos";

import Link from "next/link";

import { CtaLink } from "./cta-button";
import Header from "#/ui/landing/header";
import { type BtSession } from "#/utils/auth/server-session";
import { ArrowRight, ArrowUpRight, Blend } from "lucide-react";
import { ParticleFlux } from "./particle-flux";
import { cn } from "#/utils/classnames";
import { buttonVariants } from "#/ui/button";
import { LandingFeature } from "./landing-feature";
import {
  Airtable,
  AmbienceHealth,
  Coda,
  Notion,
  Vercel,
  Zapier,
} from "./logos";
import { LottieAnimation } from "./lottie-animation";
import { useRef } from "react";

import iterateLottieData from "./landing-iterate.json";
import evalLottieData from "./landing-eval.json";
import shipLottieData from "./landing-ship.json";
import whyEvalLottieData from "./landing-why-eval.json";
import { StickyLottieAnimation } from "./sticky-lottie-animation";

import { LandingBrainstoreCharts } from "./landing-brainstore-charts";
import { LandingBlogLink } from "./landing-blog-link";
import { Fade } from "react-awesome-reveal";
import { LandingFooterFlux } from "./landing-footer";
import {
  Bubble,
  Collab,
  Data,
  Graph,
  Head,
  Score,
  Lock,
  Shield,
  Hybrid,
} from "./shapes";
import Image from "next/image";
import { LandingCarousel } from "./landing-carousel";

export const Landing = ({ session }: { session: BtSession }) => {
  const evalSectionRef = useRef<HTMLDivElement>(null);
  const shipSectionRef = useRef<HTMLDivElement>(null);

  return (
    <PageTracker category="landing">
      <div className="w-full overflow-x-hidden">
        <div className="px-4 md:px-8">
          <div className="mx-auto w-full max-w-landing text-black">
            <div className="relative z-10">
              <ParticleFlux
                className="-mx-16 opacity-0 md:opacity-100"
                gridScaleMultiplier={0.8}
                waveAmplitude={50}
              />
              <Header session={session} />
              <div className="z-10 w-full py-36 lg:py-44 min-[1200px]:w-1/2 min-[1700px]:w-1/2">
                <div className="mb-2 font-suisse text-xs uppercase tracking-wider">
                  End-to-end AI development
                </div>
                <h1 className="tracking-snug mb-3 text-5xl font-semibold sm:text-6xl">
                  Iterate, eval, ship
                </h1>
                <h2 className="mb-6 text-balance text-2xl leading-tight">
                  Braintrust is the evals and observability platform for
                  building reliable AI agents
                </h2>
                <div className="flex w-4/5 gap-4">
                  <CtaLink
                    className="h-9 bg-brandBlue px-3 font-display text-lg font-semibold hover:!bg-black"
                    cta={
                      <>
                        Start for free <ArrowRight className="size-4" />
                      </>
                    }
                  />
                  <Link
                    href="/contact"
                    className={cn(
                      buttonVariants({
                        size: "lg",
                      }),
                      "h-9 px-3 font-display text-lg font-semibold bg-gray-100 hover:!bg-gray-300 border-0",
                    )}
                  >
                    Chat with us
                  </Link>
                </div>
              </div>
              <div className="mb-6 font-suisse text-xs uppercase tracking-wider">
                Trusted by the best
              </div>
              <div className="relative z-10 -mx-16 overflow-hidden pb-8">
                <div
                  className="relative h-12"
                  style={{
                    maskImage:
                      "linear-gradient(to right, transparent 0%, black 32px, black calc(100% - 32px), transparent 100%)",
                    WebkitMaskImage:
                      "linear-gradient(to right, transparent 0%, black 32px, black calc(100% - 32px), transparent 100%)",
                  }}
                >
                  <CustomerLogos />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-brandBlue px-4 pb-16 pt-6 text-white md:px-8 md:pt-16 lg:pb-32">
          <div className="mx-auto max-w-landing">
            <LandingCarousel />

            <div className="mb-5 font-suisse text-xs uppercase tracking-wider">
              Why run evals?
            </div>
            <h2 className="mb-24 text-balance text-5xl md:text-6xl">
              Agents fail in unpredictable ways
            </h2>
            <div className="flex flex-col gap-24 lg:flex-row lg:gap-16">
              <div className="flex-1">
                <div className="max-w-xl">
                  <LottieAnimation animationData={whyEvalLottieData} />
                  <Link
                    href="/docs/start/eval-ui"
                    className={cn(
                      buttonVariants({ variant: "border" }),
                      "rounded h-8 mt-12 px-3 border-brandBlue2 text-white hover:bg-transparent hover:border-white hover:text-white",
                    )}
                  >
                    Get started with evals
                    <ArrowUpRight className="size-3" />
                  </Link>
                </div>
              </div>
              <div className="flex flex-1 flex-col justify-end gap-12">
                <Fade>
                  <LandingFeature
                    className="lg:pr-20"
                    title="How do you know your AI feature works?"
                    description="Evals test your AI with real data and score the results. You can determine whether changes improve or hurt performance."
                    descriptionClassName="text-brandBlue3"
                  />
                </Fade>
                <Fade>
                  <LandingFeature
                    className="lg:pr-20"
                    title="Are bad responses reaching users?"
                    description="Production monitoring tracks live model responses and alerts you when quality drops or incorrect outputs increase."
                    descriptionClassName="text-brandBlue3"
                  />
                </Fade>
                <Fade>
                  <LandingFeature
                    className="lg:pr-20"
                    title="Can your team improve quality without guesswork?"
                    description="Side-by-side diffs allow you to compare the scores of different prompts and models, and see exactly why one version performs better than another."
                    descriptionClassName="text-brandBlue3"
                  />
                </Fade>
              </div>
            </div>
          </div>
        </div>
        <div className="border-black px-4 pt-16 text-black md:px-8 lg:pt-32">
          <div className="mx-auto flex max-w-landing flex-col gap-8 lg:flex-row">
            <div className="border-t border-black lg:border-l" />
            <LandingFeature
              iconSlot={<Head className="size-24 text-black " />}
              className="flex-1"
              title="Intuitive mental model"
              titleClassName="pt-12 lg:pt-24"
              description="All evals are composed of a dataset, task, and scorers. This framework gives teams a shared understanding for testing and improving AI applications systematically."
            />
            <div className="border-t border-black lg:border-l" />
            <LandingFeature
              iconSlot={<Collab className="size-24 text-black" />}
              className="flex-1"
              title="Cross-functional collaboration"
              titleClassName="pt-12 lg:pt-24"
              description="Engineers write code-based tests. Product managers prototype in the UI. Everyone can review results and debug issues together in real time."
            />
            <div className="border-t border-black lg:border-l" />
            <LandingFeature
              iconSlot={<Graph className="size-24 text-black" />}
              className="flex-1"
              title="Built for scale"
              titleClassName="pt-12 lg:pt-24"
              description="Reliable, fast infrastructure handles high-volume production traffic and complex testing workflows."
            />
            <div className="border-t border-black lg:border-l" />
          </div>
          <Fade>
            <div className="flex flex-col gap-5 py-40 md:text-center lg:py-56">
              <blockquote className="max-w-[960px] text-pretty -indent-3 text-4xl md:mx-auto md:indent-0 lg:text-5xl">
                &ldquo;I&apos;ve never seen a workflow transformation like the
                one that incorporates evals into &lsquo;mainstream
                engineering&rsquo; processes before. It&apos;s
                astonishing.&rdquo;
              </blockquote>
              <div className="font-suisse text-xs uppercase tracking-wider">
                Malte Ubl, CTO at Vercel
              </div>
              <Vercel className="md:mx-auto" />
            </div>
          </Fade>
        </div>
      </div>
      <LandingFooterFlux
        className="h-24 border-b border-black"
        // color="rgb(230 230 230)"
        color="#e5e5e5"
        // rows={2}
      />
      <div className="relative z-10">
        <StickyLottieAnimation
          evalSectionRef={evalSectionRef}
          shipSectionRef={shipSectionRef}
        />
        <div className="top-0 z-10 border-black bg-white px-4 py-8 pt-0 text-black md:px-8">
          <div className="mx-auto max-w-landing">
            <div className="flex w-full flex-col gap-12 pb-12 pt-20 lg:w-1/2 lg:pr-16">
              <div className="flex flex-col gap-4">
                <div className="font-suisse text-xs uppercase tracking-wider">
                  Iterate
                </div>
                <div className="text-balance text-4xl">
                  Refine prompt and eval ideas fast with playgrounds
                </div>
                <div>
                  <Link
                    href="/playground"
                    className={cn(
                      buttonVariants({ variant: "border" }),
                      "rounded h-8 px-3 border-black text-black hover:bg-black hover:border-black hover:text-white inline-flex",
                    )}
                  >
                    Try playground
                    <ArrowUpRight className="size-3" />
                  </Link>
                </div>
              </div>
              <div className="-mx-4 bg-brandForest md:mx-0 md:rounded-md lg:hidden">
                <LottieAnimation animationData={iterateLottieData} />
              </div>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Fast prompt engineering"
                  description="Tune prompts, swap models, edit scorers, and run evaluations directly in the browser. Compare traces side-by-side to see exactly what changed."
                />
              </Fade>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Batch testing"
                  description="Run your prompts against hundreds or thousands of real or synthetic examples to understand performance across scenarios."
                />
              </Fade>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="AI-assisted workflows"
                  description="Automate writing and optimizing prompts, scorers, and datasets with Loop, our built-in agent."
                />
              </Fade>
            </div>
          </div>
        </div>
        <LandingFooterFlux
          className="h-16 border-b border-black"
          // bgColor="rgb(9,65,53)"
          color="#e5e5e5"
        />
        <div
          ref={evalSectionRef}
          className="border-black bg-white px-4 py-8 pt-0 text-black md:px-8"
        >
          <div className="mx-auto max-w-landing">
            <div className="flex w-full flex-col gap-12 pb-12 pt-20 lg:w-1/2 lg:pr-16">
              <div className="flex flex-col gap-4">
                <div className="font-suisse text-xs uppercase tracking-wider">
                  Eval
                </div>
                <div className="text-balance text-4xl">
                  Run comprehensive tests on every prompt change to measure
                  accuracy, consistency, and safety
                </div>
              </div>
              <div className="-mx-4 bg-brandForest md:mx-0 md:rounded-md lg:hidden">
                <LottieAnimation animationData={evalLottieData} />
              </div>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Quantifiable progress"
                  description="Measure changes against your own benchmarks to make data-driven decisions."
                />
              </Fade>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Quality and safety gates"
                  description="Prevent quality regressions and unsafe outputs from reaching users."
                />
              </Fade>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Automated and human scoring"
                  description="Run automated tests on every change, then layer human feedback to capture the nuance machines miss."
                />
              </Fade>
            </div>
          </div>
        </div>
        <LandingFooterFlux
          className="h-6 border-b border-black"
          // color="rgb(9,65,53)"
          color="#e5e5e5"
        />
        <div
          ref={shipSectionRef}
          className="border-black bg-white px-4 py-8 pt-0 text-black md:px-8"
        >
          <div className="mx-auto max-w-landing">
            <div className="flex w-full flex-col gap-12 pb-12 pt-20 lg:w-1/2 lg:pr-16">
              <div className="flex flex-col gap-4">
                <div className="font-suisse text-xs uppercase tracking-wider">
                  Ship
                </div>
                <div className="text-balance text-4xl">
                  Track production AI applications with real-time monitoring and
                  online scoring
                </div>
              </div>
              <div className="-mx-4 bg-brandForest md:mx-0 md:rounded-md lg:hidden">
                <LottieAnimation animationData={shipLottieData} />
              </div>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Live performance monitoring"
                  description="Track latency, cost, and custom quality metrics as real traffic flows through your application."
                />
              </Fade>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Automations and alerts"
                  description="Configure alerts that trigger when quality thresholds are crossed or safety rails trip."
                />
              </Fade>
              <Fade>
                <LandingFeature
                  descriptionClassName="text-pretty md:w-2/3"
                  title="Scalable log ingestion"
                  description="Ingest and store all application logs with Brainstore, purpose-built for searching and analyzing AI interactions at enterprise scale."
                />
              </Fade>
            </div>
          </div>
        </div>
      </div>
      <LandingFooterFlux
        className="h-2 border-b border-black"
        // color="rgb(9,65,53)"
        color="#e5e5e5"
      />
      <div className="px-4 py-16 md:px-8 md:py-24">
        <div className="mx-auto box-content w-full max-w-landing text-black">
          <div className="mb-5 font-suisse text-xs uppercase tracking-wider">
            AI-powered workflows
          </div>
          <h3 className="tracking-snug leading-tighter mb-6 text-balance text-6xl">
            <Blend className="inline-block size-12" /> Loop
          </h3>
          <div className="mb-32 text-balance text-xl leading-normal text-black lg:w-1/2">
            Loop is an agent that builds your evals and automates the most
            time-intensive parts of AI development so you can focus on building
            compelling AI applications.
          </div>
          <div className="flex flex-col gap-8 lg:flex-row">
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <LandingFeature
              iconSlot={<Bubble className="size-24 text-black" />}
              className="flex-1"
              titleClassName="pt-12"
              title="Prompt optimization"
              description="Loop analyzes your prompts and generates better-performing versions so you can hit your quality targets faster."
              descriptionClassName="pb-6"
            >
              <Image
                src="/img/loop-prompt.png"
                alt="Loop prompt optimization"
                height={373}
                width={390}
                quality={100}
              />
            </LandingFeature>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <LandingFeature
              iconSlot={<Data className="size-24 text-black" />}
              titleClassName="pt-12"
              title="Synthetic data generation"
              description="Loop creates evaluation datasets tailored to your use case with the volume and variety needed for thorough testing."
              className="flex-1"
              descriptionClassName="pb-6"
            >
              <Image
                src="/img/loop-data.png"
                alt="Loop synthetic data generation"
                height={373}
                width={390}
                quality={100}
              />
            </LandingFeature>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <LandingFeature
              iconSlot={<Score className="size-24 text-black" />}
              titleClassName="pt-12"
              className="flex-1"
              title="Scorer building"
              description="Loop builds and refines scorers to measure the specific quality metrics that matter for your application."
              descriptionClassName="pb-6"
            >
              <Image
                src="/img/loop-scorer.png"
                alt="Loop scorer building"
                height={373}
                width={390}
                quality={100}
              />
            </LandingFeature>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
          </div>
        </div>
      </div>
      <div className="relative z-10 overflow-hidden bg-brandBlood px-4 py-16 text-brandOrange md:px-8 md:py-32">
        <img
          src="/img/brainstore-flux.svg"
          alt="Brainstore flux"
          className="absolute inset-y-0 left-0 z-[-1] hidden rotate-180 opacity-10 lg:block"
        />
        <div className="mx-auto w-full max-w-landing">
          <div className="mb-5 font-suisse text-xs uppercase tracking-wider">
            Unprecedented scale
          </div>
          <h3 className="mb-32 text-6xl">Brainstore is built for AI data</h3>
          <div className="flex flex-col gap-16 md:flex-row">
            <div className="flex flex-1 flex-col justify-between gap-16">
              <div>
                <div className="text-balance text-xl leading-normal text-brandOrange">
                  Traditional databases can&apos;t handle the complexity of
                  modern AI workflows. Brainstore is designed specifically for
                  AI application logs and traces. Query, filter, analyze, and
                  review logs 80x faster with Brainstore.
                </div>
                <Link
                  href="/blog/brainstore#benchmarks"
                  className={cn(
                    buttonVariants({ variant: "border" }),
                    "rounded h-8 mt-4 px-3 border-brandOrange text-brandOrange hover:bg-transparent hover:border-brandTerracotta hover:text-brandTerracotta",
                  )}
                >
                  View benchmarks
                  <ArrowUpRight className="size-3" />
                </Link>
              </div>
              <div className="text-brandTerracotta">
                <div className="mb-4 text-balance -indent-3 text-3xl leading-tight">
                  “Brainstore has completely changed how our team interacts with
                  logs. We&apos;ve been able to discover insights by running
                  searches in seconds that would previously take hours.”
                </div>
                <div className="mb-4 font-suisse text-xs uppercase tracking-wider">
                  Sarah Sachs, Engineering Lead at Notion
                </div>
                <Notion />
              </div>
            </div>
            <div className="flex flex-1 flex-col gap-6">
              <hr className="border-brandTerracotta/50" />
              <div className="text-3xl">
                <span className="text-5xl">86.6x</span> faster full text search
              </div>
              <LandingBrainstoreCharts
                brainstoreValue={240}
                competitorValue={20789}
              />
              <hr className="border-brandTerracotta/50" />
              <div className="text-3xl">
                <span className="text-5xl">2.4x</span> faster write latency
              </div>
              <LandingBrainstoreCharts
                brainstoreValue={1780}
                competitorValue={4176}
              />
              <hr className="border-brandTerracotta/50" />
              <div className="text-3xl">
                <span className="text-5xl">2.1x</span> faster span load time
              </div>
              <LandingBrainstoreCharts
                brainstoreValue={549}
                competitorValue={1160}
              />
              <hr className="border-brandTerracotta/50" />
            </div>
          </div>
        </div>
      </div>
      <div className="relative z-10 overflow-hidden bg-brandBlue3/70 px-4 py-16 text-brandBlue md:px-8 md:py-32">
        <img
          src="/img/enterprise-flux.svg"
          alt="Enterprise flux"
          className="absolute inset-y-0 right-0 z-[-1] hidden opacity-30 lg:block"
        />
        <div className="mx-auto w-full max-w-landing overflow-hidden">
          <div className="mb-5 font-suisse text-xs uppercase tracking-wider">
            Built for enterprise
          </div>
          <h3 className="mb-6 text-6xl">Security and compliance at scale</h3>
          <div className="mb-32 text-balance text-xl leading-normal text-brandBlue lg:w-1/2">
            Braintrust meets the demanding security, performance, and
            collaboration requirements of large organizations building AI
            applications at scale
          </div>
          <div className="flex flex-col gap-8 lg:w-1/2">
            <div className="flex flex-col gap-8 border-t border-brandBlue pb-8 pt-4 lg:flex-row">
              <div className="w-16">
                <Lock className="size-8" />
              </div>
              <LandingFeature
                className="flex-1"
                title="Granular permissions"
                description="Role-based access control with org-level permissions and project isolation to meet your security and compliance requirements."
              />
            </div>
            <div className="flex flex-col gap-8 border-t border-brandBlue pb-8 pt-4 lg:flex-row">
              <div className="w-16">
                <Shield className="size-8" />
              </div>
              <LandingFeature
                title="SOC 2 Type II"
                description="Third-party security certification with comprehensive security controls."
                className="flex-1"
              >
                <div className="mt-2">
                  <Link
                    href="https://trust.braintrust.dev"
                    target="_blank"
                    className={cn(
                      buttonVariants(),
                      "rounded h-8 px-3 border-brandBlue text-brandBlue hover:bg-transparent hover:border-brandBlue2 hover:text-brandBlue2 inline-flex",
                    )}
                  >
                    Trust center <ArrowUpRight className="size-3" />
                  </Link>
                </div>
              </LandingFeature>
            </div>
            <div className="flex flex-col gap-8 border-t border-brandBlue pb-8 pt-4 lg:flex-row">
              <div className="w-16">
                <Hybrid className="size-8" />
              </div>
              <LandingFeature
                title="Hybrid deployment"
                className="flex-1"
                description="Self-hosting options to maintain full control over your AI data and meet strict compliance requirements."
              />
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white px-4 pb-48 pt-32 text-black md:px-8">
        <div className="mx-auto max-w-landing">
          <div className="mb-5 font-suisse text-xs uppercase tracking-wider">
            Driving results
          </div>
          <h3 className="mb-32 text-pretty text-6xl lg:max-w-[50%]">
            Outsized impact for the biggest brands in AI
          </h3>
          <div className="flex flex-col gap-8 lg:flex-row">
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <Fade className="flex-1">
              <div className="flex h-80 flex-1 flex-col justify-between gap-8">
                <Zapier />
                <div className="flex flex-col gap-2">
                  <div className="text-8xl xl:text-9xl">5x</div>
                  <div className="h-14 text-balance text-xl leading-tight">
                    More AI features in production
                  </div>
                </div>
              </div>
            </Fade>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <Fade className="flex-1" delay={100}>
              <div className="flex h-80 flex-1 flex-col justify-between gap-8">
                <Coda />
                <div className="flex flex-col gap-2">
                  <div className="text-8xl xl:text-9xl">20x</div>
                  <div className="h-14 text-balance text-xl leading-tight">
                    Increase in team productivity
                  </div>
                </div>
              </div>
            </Fade>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <Fade className="flex-1" delay={200}>
              <div className="flex h-80 flex-1 flex-col justify-between gap-8">
                <Notion />
                <div className="flex flex-col gap-2">
                  <div className="text-8xl xl:text-9xl">24h</div>
                  <div className="h-14 text-balance text-xl leading-tight">
                    Max time to switch models
                  </div>
                </div>
              </div>
            </Fade>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
            <Fade className="flex-1" delay={300}>
              <div className="flex h-80 flex-1 flex-col justify-between gap-8">
                <AmbienceHealth className="w-40" />
                <div className="flex flex-col gap-2">
                  <div className="text-8xl xl:text-9xl">3x</div>
                  <div className="h-14 text-balance text-xl leading-tight">
                    Faster eval time
                  </div>
                </div>
              </div>
            </Fade>
            <div className="flex-none border-t border-black lg:border-l lg:border-t-0" />
          </div>
          <div className="flex flex-col gap-5 py-40 text-black md:text-center lg:py-56">
            <blockquote className="max-w-[960px] text-pretty -indent-3 text-4xl md:mx-auto md:indent-0 lg:text-5xl">
              &ldquo;Every new AI project starts with evals in
              Braintrust—it&apos;s a game changer.&rdquo;
            </blockquote>
            <div className="font-suisse text-xs uppercase tracking-wider">
              Lee Weisberger, Engineering Manager at Airtable
            </div>
            <Airtable isMono className="md:mx-auto" />
          </div>
          <div className="flex flex-col gap-8 border-black lg:flex-row lg:border-t">
            <div className="flex-none pt-8 font-suisse text-xs uppercase tracking-wider lg:w-1/4">
              From the blog
            </div>
            <div className="flex flex-1 flex-col">
              <hr className="border-black lg:hidden" />
              <LandingBlogLink
                href="/blog/coursera"
                title="How Coursera builds next-generation learning tools"
              />
              <hr className="border-black" />
              <LandingBlogLink
                href="/blog/best-practices"
                title="Webinar: Eval best practices"
              />
              <hr className="border-black" />
              <LandingBlogLink
                href="/blog/loom"
                title="How Loom auto-generates video titles"
              />
              <hr className="border-black" />
              <LandingBlogLink
                href="/blog/brainstore"
                title="Brainstore: the database designed for AI engineering"
              />
              <hr className="border-black" />
            </div>
          </div>
        </div>
      </div>
      <LandingFooterFlux />
      <div className="relative z-10 flex h-96 w-screen bg-black px-4 py-12 text-white md:px-8">
        <div className="mx-auto w-full max-w-landing">
          <h3 className="mb-8 max-w-md text-5xl">
            Bring structure to your AI agent development
          </h3>
          <div className="flex gap-3">
            <CtaLink
              className="h-9 bg-brandBlue px-3 font-display text-lg font-semibold hover:!bg-black"
              cta={<>Sign up for free</>}
            />
            <Link
              href="/contact"
              className={cn(
                buttonVariants({
                  size: "lg",
                }),
                "h-9 px-3 font-display text-lg font-semibold bg-gray-100 hover:!bg-gray-300 border-0",
              )}
            >
              Chat with us
            </Link>
          </div>
        </div>
      </div>
    </PageTracker>
  );
};
