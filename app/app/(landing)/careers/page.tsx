import { proseMDXBase<PERSON>lassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { ArrowLeft, ArrowUpRight } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

import "./careers-style.css";
import { Apply<PERSON>utton } from "#/ui/careers/apply-button";
import { buildMetadata } from "#/app/metadata";
import Header from "#/ui/landing/header";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { LandingFooterFlux } from "../landing-footer";

const headers = {
  accept: "application/json; version=1",
  "Content-Type": "application/json",
  Authorization: `Basic ${btoa(`${process.env.NEXT_PUBLIC_ASHBY_API_KEY ?? ""}:`)}`,
};

type Job = {
  id: string;
  title: string;
  descriptionHtml: string;
  applyLink: string;
};

const getJob = async ({ ashby_jid }: { ashby_jid: string }): Promise<Job> => {
  const res = await fetch("https://api.ashbyhq.com/jobPosting.info", {
    method: "POST",
    headers,
    body: JSON.stringify({ jobPostingId: ashby_jid }),
  });
  const json = await res.json();
  return json.results;
};

export default async function CareersPage({
  searchParams,
}: {
  searchParams: Promise<{ ashby_jid?: string }>;
}) {
  const session = await getServerAuthSession();
  const { ashby_jid } = await searchParams;

  if (!ashby_jid) {
    let jobs: { id: string; title: string }[] = [];
    try {
      const res = await fetch("https://api.ashbyhq.com/jobPosting.list", {
        method: "POST",
        headers,
        body: JSON.stringify({ listedOnly: true }),
      });
      const json = await res.json();
      jobs = json.results;
    } catch (err) {
      console.error(err);
      // redirect to ashby hosted page in case something goes wrong
      return redirect("https://jobs.ashbyhq.com/Braintrust");
    }

    return (
      <div className="px-4 sm:px-8">
        <div className="mx-auto max-w-landing font-display text-black">
          <Header session={session} />
          <section className="-mx-4 mb-48 border-t border-black px-4 py-16 sm:-mx-8 sm:px-8">
            <div className="mb-3 font-suisse text-xs uppercase tracking-wider">
              Careers
            </div>
            <h1 className="mb-6 text-balance text-6xl font-medium tracking-tight">
              Build with us
            </h1>
            <p className="mb-16 max-w-screen-md text-2xl text-primary-900">
              Braintrust is a small team of builders passionate about empowering
              developers working with AI. We’re looking for highly independent,
              self-motivated, and creative people to join us.
            </p>
            <div className="flex flex-col">
              {jobs.map((child, idx) => {
                return (
                  <Link
                    key={idx}
                    href={`/careers?ashby_jid=${child.id}`}
                    className="group flex max-w-screen-md items-center justify-between border-t border-black bg-transparent py-4 text-2xl transition-all hover:bg-gray-100 hover:px-4"
                  >
                    {child.title}
                    <ArrowUpRight className="size-4 opacity-0 transition-opacity group-hover:opacity-100" />
                  </Link>
                );
              })}
            </div>
          </section>
        </div>
      </div>
    );
  }

  let job: Job | null = null;
  try {
    job = await getJob({ ashby_jid });
  } catch (err) {
    console.error(err);
    // redirect to ashby hosted page in case something goes wrong
    return redirect("https://jobs.ashbyhq.com/Braintrust");
  }

  if (!job) return notFound();

  return (
    <>
      <div className="px-4 sm:px-8">
        <div className="mx-auto max-w-landing font-display text-black">
          <Header session={session} />
          <section className="-mx-4 mb-48 border-t border-black px-4 py-16 sm:-mx-8 sm:px-8">
            <Link
              href="/careers"
              className="mb-3 flex items-center gap-1 font-suisse text-xs uppercase tracking-wider underline-offset-4 hover:underline"
            >
              <ArrowLeft className="size-3" /> Careers
            </Link>
            <div className="careers-page">
              <article
                className={cn(
                  proseMDXBaseClassName,
                  "mb-44 prose-h1:font-normal max-w-screen-md prose-xl prose-headings:font-medium prose-headings:font-display prose-p:text-black prose-p:text-lg",
                )}
              >
                <h1 className="mb-32 text-6xl">{job.title}</h1>
                <div
                  dangerouslySetInnerHTML={{ __html: job.descriptionHtml }}
                />
                <ApplyButton url={job.applyLink} />
              </article>
            </div>
          </section>
        </div>
      </div>
      <LandingFooterFlux />
    </>
  );
}

export async function generateMetadata({
  searchParams,
}: {
  searchParams: Promise<{ ashby_jid?: string }>;
}) {
  const { ashby_jid } = await searchParams;

  let job: Job | null = null;
  if (ashby_jid) {
    try {
      job = await getJob({ ashby_jid });
    } catch (err) {
      // swallow
    }
  }

  if (!job) {
    return buildMetadata({
      title: "Careers",
      description:
        "Builders passionate about empowering developers working with AI",
      relativeUrl: "/careers",
    });
  }

  return buildMetadata({
    title: job.title,
    sections: ["Careers"],
    relativeUrl: `/careers?ashby_jid=${ashby_jid}`,
  });
}
