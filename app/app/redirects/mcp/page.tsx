import {
  type AuthCodeCachedData,
  makeMcp<PERSON>ode<PERSON><PERSON><PERSON><PERSON>,
} from "#/app/api/mcp/exchange-auth-code/route";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { KV } from "#/utils/cache";
import { sha256 } from "#/utils/hash";
import { encryptMessage } from "@braintrust/proxy/utils";
import { auth } from "@clerk/nextjs/server";
import Link from "next/link";
import { redirect } from "next/navigation";
import { z } from "zod";
import { Button } from "#/ui/button";

const EXPIRATION_TIME_SECONDS = 3600; // 1 hour
const AUTH_CODE_EXPIRATION_TIME_SECONDS = 600; // 10 minutes

// The control plane asks as a trusted relay, of sorts, to facilitate authentication for the MCP service. The
// user's MCP client starts by authenticating to the data plane, and that generates a URL that leads them here.
//
// 1. We first check to see if they're logged into Braintrust. If not, we redirect them to the appropriate SSO page,
//    and then they come back here.
// 2. Once they are logged in, we generate a temporary code that is encrypted with a key derived from the user-suppled
//    PKCE code challenge. That way, you _have_ to know that code to look at the token (or even find it in the KV cache).
// 3. This token gets cached for a short period of time, and it can be retrieved via /api/mcp/exchange-auth-code. This
//    exchange is the final step of the OAuth flow.
export default async function MCPRedirectPage(props: {
  searchParams: Promise<Record<string, string | string[]>>;
}) {
  const searchParams = await props.searchParams;
  const session = await getServerAuthSession();

  const parsedParams = z
    .object({
      client_id: z.string().min(1),
      redirect_uri: z.string().url(),
      state: z.string().optional(),
      // PKCE parameters
      code_challenge: z.string().optional(),
      code_challenge_method: z.enum(["S256", "plain"]).optional(),
    })
    .safeParse(searchParams);

  if (!parsedParams.success) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-6xl text-red-500">✗</div>
          <h2 className="mb-2 text-xl font-semibold">Invalid Request</h2>
          <p className="mb-4 text-red-600">
            Missing or invalid OAuth parameters
          </p>
          <Button asChild>
            <Link href="/">Go to Home</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Extract MCP OAuth parameters
  const clientId = parsedParams.data.client_id;
  const redirectUri = parsedParams.data.redirect_uri;
  const state = parsedParams.data.state;
  const codeChallenge = parsedParams.data.code_challenge;
  const codeChallengeMethod = parsedParams.data.code_challenge_method;

  // Check if user is logged into Braintrust
  if (!session.authId) {
    // User not logged in - redirect to Braintrust login
    // Store OAuth params to return to after login
    const oauthParams = new URLSearchParams();
    for (const [key, value] of Object.entries(searchParams)) {
      if (typeof value === "string") {
        oauthParams.set(key, value);
      } else if (Array.isArray(value)) {
        oauthParams.set(key, value[0]); // Use first value if array
      }
    }

    // It has to be double encoded to make its way all the way back here.
    const loginUrl = `/signin?redirect_url=${encodeURIComponent(`/redirects/mcp?${encodeURIComponent(oauthParams.toString())}`)}`;
    return redirect(loginUrl);
  }

  // User is logged in - get their Braintrust token
  const braintrustToken =
    session.legacy_sessionToken ??
    (await (
      await auth()
    ).getToken({
      template: "data_plane",
      expiresInSeconds: EXPIRATION_TIME_SECONDS,
    }));

  if (!braintrustToken) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-6xl text-red-500">✗</div>
          <h2 className="mb-2 text-xl font-semibold">Authentication Error</h2>
          <p className="mb-4 text-red-600">
            Unable to get authentication token
          </p>
          <Button variant="primary" asChild>
            <Link href="/signin">Sign In Again</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (!redirectUri) {
    // No redirect URI specified, show success page
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-6xl text-green-500">✓</div>
          <h2 className="mb-2 text-xl font-semibold">
            MCP Authorization Success!
          </h2>
          <p className="mb-4 text-gray-600">
            You have successfully authorized MCP access to your Braintrust
            account. However, without a valid redirect URI, we cannot exchange a
            token with your MCP client. Please try logging in again.
          </p>
        </div>
      </div>
    );
  }

  const randomBytes = new Uint8Array(16);
  crypto.getRandomValues(randomBytes);
  const authCode = `auth_${Date.now()}_${Array.from(randomBytes, (b) => b.toString(16).padStart(2, "0")).join("")}`;
  const authCodeEncryptionKey = sha256(authCode, "base64");

  const encryptedSecret = await encryptMessage(
    authCodeEncryptionKey,
    braintrustToken,
  );

  if (!encryptedSecret) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-6xl text-red-500">✗</div>
          <h2 className="mb-2 text-xl font-semibold">Encryption Error</h2>
          <p className="mb-4 text-red-600">
            Unable to encrypt authentication token
          </p>
        </div>
      </div>
    );
  }

  const body: AuthCodeCachedData = {
    client_id: clientId,
    code_challenge: codeChallenge,
    code_challenge_method: codeChallengeMethod || "plain",
    expires_at: Date.now() + EXPIRATION_TIME_SECONDS * 1000,
    used: false,

    encrypted_token: encryptedSecret.data,
    iv: encryptedSecret.iv,
  };

  const kv = new KV();
  const cacheKey = makeMcpCodeCacheKey({
    code: authCode,
    codeChallenge,
  });

  await kv.set(cacheKey, body, {
    ex: AUTH_CODE_EXPIRATION_TIME_SECONDS,
  });

  // Show consent page instead of auto-redirecting
  const callbackUrl = `${redirectUri}?code=${authCode}&state=${state || ""}`;

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full">
            <svg
              className="size-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
          </div>
          <h2 className="mb-2 text-2xl font-bold">MCP Authorization Request</h2>
          <p className="mb-4">
            An MCP client is requesting access to your Braintrust account
          </p>
        </div>

        <div className="mb-6 rounded-lg border p-4">
          <p className="mb-2 text-sm font-medium">Redirect URL:</p>
          <p className="break-all rounded border p-2 font-mono text-sm">
            {redirectUri}
          </p>
        </div>

        <div className="mb-6">
          <p className="mb-4 text-sm">
            Are you sure you trust this redirect URL? If so, click the button
            below to proceed with authorization.
          </p>
          <p className="text-xs text-amber-600">
            ⚠️ Only proceed if you recognize and trust this URL
          </p>
        </div>

        <div className="flex flex-col gap-3">
          <Button variant="primary" className="w-full" asChild>
            <a href={callbackUrl}>Yes, I trust this URL - Proceed</a>
          </Button>

          <Button variant="default" className="w-full" asChild>
            <Link href="/">Cancel</Link>
          </Button>
        </div>

        <div className="mt-6 border-t pt-4">
          <p className="text-xs">
            This authorization allows the MCP client to access your Braintrust
            account with the permissions you&apos;ve granted.
          </p>
        </div>
      </div>
    </div>
  );
}
