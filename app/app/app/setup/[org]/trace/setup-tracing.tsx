"use client";

import { useState, useEffect, useRef } from "react";
import { isObject } from "braintrust/util";
import { useIndexedDBObject } from "#/utils/use-indexeddb-object";
import { useCreateNewPlayground } from "#/ui/prompts/function-editor/create-playground";
import { type LoggedOutPlaygroundData } from "#/app/playground/logged-out-playground-table";
import { newId } from "braintrust";
import Link from "next/link";
import { getProjectLink } from "../../../[org]/p/[project]/getProjectLink";
import { buttonVariants } from "#/ui/button";

import { createApiKey } from "#/app/app/[org]/settings/api-keys/api-key";
import {
  type fetchApiKeys,
  type deleteApiKey,
} from "#/app/app/[org]/settings/api-keys/actions";

import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { useAnalytics } from "#/ui/use-analytics";
import { starterProjectName } from "./trace-config";
import { Button } from "#/ui/button";
import {
  getOnboardingFlowId,
  clearOnboardingFlowId,
} from "#/lib/onboardingFlow";
import { cn } from "#/utils/classnames";
import { type getProjectSummary } from "../../../[org]/org-actions";
import { useUser } from "#/utils/user";
import { motion } from "motion/react";
import { WaitingIndicator } from "#/ui/waiting-indicator";
import { TraceSetupInstructions } from "./trace-setup-instructions";
import { useWaitingForLogs } from "./use-waiting-for-logs";
import { useTraceSetupState } from "./use-trace-setup";

export const SetupTracing = ({ orgName }: { orgName: string }) => {
  const { orgs } = useUser();
  const org = orgs[orgName];

  const {
    selectedLanguage,
    selectedProvider,
    setSelectedLanguage,
    setSelectedProvider,
  } = useTraceSetupState();

  const { analytics } = useAnalytics();

  // Track page view when component mounts
  useEffect(() => {
    analytics?.track("onboardingPageViewed", {
      flowId: getOnboardingFlowId(),
      orgName,
      orgId: orgs?.[orgName]?.id,
      pageName: "trace_setup",
      pageIndex: 3,
      isNewUser:
        Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0,
      source: "web",
    });
  }, [analytics, orgName, orgs]);

  const [apiKey, setApiKey] = useState<string | undefined>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [starterProjectId, setStarterProjectId] = useState<
    string | undefined
  >();

  const { getToken } = useAuth();
  //TODO: verify we need this, avoid calling endpoints twice
  const hasCreatedApiKey = useRef(false);

  const [loggedOutPlaygroundData, _, clearLoggedOutPlaygroundData] =
    useIndexedDBObject<LoggedOutPlaygroundData>({
      store: "loggedOutData",
      key: "playground",
    });

  const { createNewPlayground } = useCreateNewPlayground({
    orgName,
    projectName: starterProjectName,
    onFinish: () => {
      clearLoggedOutPlaygroundData();
    },
    routerMethod: "replace",
    orgId: org.id,
  });

  useWaitingForLogs({ orgName, selectedLanguage, selectedProvider });

  const handlePlaygroundCreate = async () => {
    if (!org.id) {
      toast.error("Failed to create playground", {
        action: (
          <Link
            href={getProjectLink({
              orgName,
              projectName: starterProjectName,
            })}
            className={buttonVariants({ size: "xs" })}
          >
            Go to project
          </Link>
        ),
      });
      return;
    }
    if (!starterProjectId) {
      // If no starter project ID, the project creation should be handled elsewhere
      // or the UI should not show the create button
      return;
    }
    try {
      setIsSubmitting(true);
      {
        try {
          await createNewPlayground({
            projectId: starterProjectId,
            flowId: newId(),
            sessionName: "Playground 1",
            datasetName: "Dataset 1",
            initialRecords: loggedOutPlaygroundData?.playgroundBlocks.map(
              ({ prompt_data, function_data }) => ({
                prompt_data,
                function_data,
              }),
            ),
            datasetRows: loggedOutPlaygroundData?.datasetRows.map(
              ({ input, expected, metadata }) => ({
                input,
                expected,
                metadata: isObject(metadata) ? metadata : undefined,
              }),
            ),
            scorerFunctions: loggedOutPlaygroundData?.scorerFunctions,
            promptSessionData: {
              scorers: loggedOutPlaygroundData?.savedScorers ?? [],
              settings: loggedOutPlaygroundData?.playgroundSettings,
            },
          });

          // Clear onboarding flow after completion in trace path
          clearOnboardingFlowId();
        } catch (error) {
          console.error(error);
          toast.error("Failed to create playground", {
            action: (
              <Link
                href={getProjectLink({
                  orgName,
                  projectName: starterProjectName,
                })}
                className={buttonVariants({ size: "xs" })}
              >
                Go to project
              </Link>
            ),
          });
        }
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to create project. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fetch starter project ID for providers that need it
  useEffect(() => {
    if (!orgName) return;

    const fetchStarterProjectId = async () => {
      try {
        const projects = await invokeServerAction<typeof getProjectSummary>({
          fName: "getProjectSummary",
          args: { org_name: orgName },
          getToken,
        });

        const starterProject = projects?.find(
          (p) => p.project_name === starterProjectName,
        );
        if (starterProject) {
          setStarterProjectId(starterProject.project_id);
        }
      } catch (error) {
        console.warn("Failed to fetch starter project ID:", error);
        // Could add more sophisticated error handling here if needed
        // For now, just log the warning and let the UI handle the undefined state
      }
    };
    fetchStarterProjectId();
  }, [orgName, getToken]);

  useEffect(() => {
    if (!org.id || hasCreatedApiKey.current) return;

    const createAndReplaceTutorialApiKey = async () => {
      hasCreatedApiKey.current = true;
      const existingApiKeys =
        (await invokeServerAction<typeof fetchApiKeys>({
          fName: "fetchApiKeys",
          args: {},
          getToken,
        })) ?? [];

      const tutorialKeys = existingApiKeys.filter(
        (key) => key.name === "tutorial" && key.org_id === org.id,
      );

      try {
        const newKey = await createApiKey({
          name: "tutorial",
          orgId: org.id ?? "",
        });
        setApiKey(newKey);
        analytics?.track("onboarding_api_key_created", { orgName });
      } catch (error) {
        hasCreatedApiKey.current = false;
        toast.error("Failed to create API key", {
          description: String(error),
        });
      }

      try {
        await Promise.all(
          tutorialKeys.map((tutorialKey) =>
            invokeServerAction<typeof deleteApiKey>({
              fName: "deleteApiKey",
              args: { api_key_id: tutorialKey.id },
              getToken,
            }),
          ),
        );
      } catch (error) {
        toast.error("Failed to delete old tutorial keys", {
          description: String(error),
        });
      }
    };

    createAndReplaceTutorialApiKey();
  }, [org.id, orgName, analytics, getToken, orgs]);

  return (
    <div className="mx-auto flex size-full flex-col gap-8">
      <motion.div
        key="waiting-indicator"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.5,
          delay: 0.3,
          ease: "easeOut",
        }}
        className="pointer-events-none fixed inset-x-0 top-0 z-50 flex justify-center py-9"
      >
        <WaitingIndicator task="logs" className="shadow-lg" />
      </motion.div>
      <div className="flex flex-col">
        <div>
          <h1 className="mb-2 font-display text-3xl font-semibold">
            Trace LLM calls to set up observability
          </h1>
          <p className="text-base text-primary-600">
            Start tracing your existing app in minutes. You will be redirected
            to the dashboard once we receive your first log.
          </p>
        </div>
      </div>
      <TraceSetupInstructions
        orgName={orgName}
        apiKey={apiKey}
        projectId={starterProjectId}
        projectName={starterProjectName}
        showKeyGeneration={false}
        selectedLanguage={selectedLanguage}
        onLanguageChange={setSelectedLanguage}
        selectedProvider={selectedProvider}
        onProviderChange={setSelectedProvider}
      />
      <div className="z-10 flex justify-between gap-2 pb-8">
        <Link
          prefetch
          href={`/app/setup/${orgName}`}
          className={cn(
            buttonVariants({ variant: "ghost", size: "sm" }),
            "transition-all text-primary-500",
          )}
        >
          Back
        </Link>
        <Button
          variant="ghost"
          size="sm"
          className="text-primary-500 transition-all"
          disabled={isSubmitting}
          isLoading={isSubmitting}
          onClick={() => {
            // Track onboarding completion for trace path (skip)
            analytics?.track("onboardingComplete", {
              flowId: getOnboardingFlowId(),
              flowName:
                Object.keys(orgs || {}).filter((n) => n !== orgName).length ===
                0
                  ? "new_user_signup"
                  : "existing_user_org_creation",
              completionDetails: {
                path: "trace",
                stepName: "trace_setup",
                stepIndex: 3,
                providerKeyProvided: false,
                providerChoice: selectedProvider,
                skipped: true,
                sdkLanguage: selectedLanguage,
              },
              isNewUser:
                Object.keys(orgs || {}).filter((n) => n !== orgName).length ===
                0,
              org_id: orgs?.[orgName]?.id,
              org_name: orgName,
              source: "web",
            });

            // Clear onboarding flow after completion (skip path)
            clearOnboardingFlowId();

            handlePlaygroundCreate();
          }}
        >
          Skip for now
        </Button>
      </div>
    </div>
  );
};
