"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { useOrg } from "#/utils/user";
import {
  type RefObject,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BodyWrapper, HEIGHT_WITH_DOUBLE_TOP_OFFSET } from "../../body-wrapper";
import { CheckOrg } from "../clientlayout";
import { type ProjectSummary } from "../org-actions";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { QueryResults } from "./results";
import { BtqlEditor } from "./btql-editor";
import { type TextEditorHandle } from "#/ui/text-editor";
import { ErrorBanner } from "#/ui/error-banner";
import { parseBtqlSchema } from "#/utils/btql/btql";
import { BtqlRunButton, useBtqlQuery } from "./btql-run-button";
import { CopyBtqlCodeSnippet } from "./btql-code-snippet";
import { cn } from "#/utils/classnames";
import {
  OptimizationProvider,
  useOptimizationContext,
} from "#/utils/optimization/provider";
import { GlobalChatProvider } from "#/ui/optimization/global-chat-provider";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";
import {
  type BTQLContextObject,
  useGlobalChat,
} from "#/ui/optimization/use-global-chat-context";
import { DockedChatSpacer } from "../p/[project]/playgrounds/[playground]/docked-chat-spacer";
import { ProjectContext } from "../p/[project]/projectContext";
import { useSearchParams } from "next/navigation";
import { Tabs, TabsContent } from "@radix-ui/react-tabs";
import { Button } from "#/ui/button";
import { X } from "lucide-react";
import useEvent from "react-use-event-hook";
import { newId } from "#/utils/btapi/btapi";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { RowDetailSheet } from "./row-detail-sheet";
import { ErrorBoundary } from "#/utils/error-boundary";
import { usePanelSize } from "#/ui/use-panel-size";
import {
  classifyQuery,
  BTQLChartRenderer,
} from "#/ui/optimization/btql-chart-display";
import { parseQuery } from "@braintrust/btql/parser";
import { BarChart3 } from "lucide-react";
import { BasicTooltip } from "#/ui/tooltip";
import { useBtqlAutocompleteDataSources } from "./use-btql-autocomplete-data-sources";
import {
  BTQLEditorTabs,
  type BtqlTabResult,
  type BtqlTab,
} from "./btql-editor-tabs";
import { produce } from "immer";
import { FixLoopButton } from "./fix-loop-button";
import { type SearchableItemInfo } from "#/utils/codemirror/btql-lang";

const defaultTabId = newId();

// TODO:
// - Use inference queries to autocomplete columns.
// - Fill in code snippets.
// - Include and link to in docs
// - Support scrolling through with cursors.
// - Show the query plan
//
// Crazier ideas:
// - Go from a slow query in the UI into the editor easily.
// - "Paste" a BTQL curl command and view it in the editor.
// - Would be kind of magical to make real-time work

export interface Params {
  org: string;
}

export function makeBtqlTabContextObject(
  tab: BtqlTab,
  result: BtqlTabResult,
): BTQLContextObject {
  return {
    id: `btql-${tab.id}`,
    resource: "btql",
    name: tab.name,
    query: tab.query,
    hasResults: Boolean(result.result?.data),
    resultCount: result.result?.data?.length ?? 0,
    error: result.error,
    lastQueryMs: result.lastQueryMs,
  };
}

function ChatContextSetter({
  activeTab,
  activeTabResult,
}: {
  activeTab: BtqlTab;
  activeTabResult: BtqlTabResult;
}) {
  const {
    setCurrentSessionContextObjects,
    setDataSourceSelectionConfirmationData,
    currentSessionHasInteractedWithContextObjects,
  } = useGlobalChat();
  const {
    registerDataSourceSelectionConfirmationHandler,
    unregisterDataSourceSelectionConfirmationHandler,
  } = useOptimizationContext();

  // Populate the chat session with the active tab's context object when changing tabs,
  // as long as the user has not interacted with the context objects yet
  useEffect(() => {
    if (!activeTab || currentSessionHasInteractedWithContextObjects) return;
    setCurrentSessionContextObjects({
      [`btql-${activeTab.id}`]: makeBtqlTabContextObject(
        activeTab,
        activeTabResult,
      ),
    });
  }, [
    activeTab,
    activeTabResult,
    currentSessionHasInteractedWithContextObjects,
    setCurrentSessionContextObjects,
  ]);

  useEffect(() => {
    registerDataSourceSelectionConfirmationHandler?.(
      setDataSourceSelectionConfirmationData,
    );

    return () => {
      unregisterDataSourceSelectionConfirmationHandler?.();
    };
  }, [
    registerDataSourceSelectionConfirmationHandler,
    unregisterDataSourceSelectionConfirmationHandler,
    setDataSourceSelectionConfirmationData,
  ]);

  return null;
}

export default function ClientPage({
  projectSummary: projectSummaryServer,
}: {
  params: Params;
  projectSummary: ProjectSummary[];
}) {
  const { name: orgName } = useOrg();
  const searchParams = useSearchParams();

  useAnalytics({
    page: {
      category: "btql",
    },
  });

  const { dataSources, projectsPending } = useBtqlAutocompleteDataSources({
    projectSummaryServer,
  });

  const isClient = useIsClient();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollMarginRef = useRef<HTMLDivElement>(null);

  // TO-DO right now, if we bring the query in via search param, it will just override
  // every tab that has btql working. This is probably not what the user would want.
  // Figure this out -- this could mean we save query to storage on run rather than on every value change.
  // or some smart mechanism to not override all tab if coming from search param
  // or create tabs .. etc
  const queryFromUrl = searchParams?.get("q") || null;

  const [selectedRow, setSelectedRow] = useState<{
    row: Record<string, unknown> | null;
    index?: number;
  } | null>(null);
  const [activeRow, setActiveRow] = useState<string | null>(null);

  const [showCharts, setShowCharts] = useEntityStorage({
    entityType: "btql",
    entityIdentifier: orgName,
    key: "showCharts",
    defaultValue: true,
  });

  // Split tabs and tab results for better render performance
  const [tabs, setTabs] = useEntityStorage({
    entityType: "btql",
    entityIdentifier: orgName,
    key: "tabs",
    defaultValue: [
      {
        id: defaultTabId,
        name: "Query 1",
        query: queryFromUrl || "",
      },
    ],
  });
  const [tabResults, setTabResults] = useEntityStorage({
    entityType: "btql",
    entityIdentifier: orgName,
    key: "tabResults",
    defaultValue: [{ id: defaultTabId }],
  });

  const [activeTabId, setActiveTabId] = useEntityStorage({
    entityType: "btql",
    entityIdentifier: orgName,
    key: "activeTabId",
    defaultValue: defaultTabId,
  });

  const activeTabIndex = tabs.findIndex((tab) => tab.id === activeTabId);
  const activeTab = tabs[activeTabIndex] || tabs[0];
  const activeTabResult = tabResults[activeTabIndex] || tabResults[0];

  const updateTabQuery = useCallback(
    (tabId: string, query: string) => {
      setTabs((prevTabs) =>
        produce(prevTabs, (draft) => {
          const tabIndex = draft.findIndex((tab) => tab.id === tabId);
          if (tabIndex !== -1) {
            draft[tabIndex].query = query;
          }
        }),
      );
    },
    [setTabs],
  );

  useEffect(() => {
    if (queryFromUrl && activeTab.id) {
      updateTabQuery(activeTab.id, queryFromUrl);
    }
  }, [queryFromUrl, activeTab.id, updateTabQuery]);

  const updateTabResult = useCallback(
    (tabId: string, updates: Omit<BtqlTabResult, "id">) => {
      setTabResults((prevTabResults) =>
        produce(prevTabResults, (draft) => {
          const tabIndex = draft.findIndex((tab) => tab.id === tabId);
          if (tabIndex !== -1) {
            Object.assign(draft[tabIndex], updates);
          }
        }),
      );
    },
    [setTabResults],
  );

  const textEditorRef = useRef<TextEditorHandle | null>(null);
  const getCurrentQuery = useCallback(() => {
    const val = textEditorRef.current?.getValue() ?? "";
    return val;
  }, [textEditorRef]);
  const detailsMinWidth = usePanelSize(400);

  const lintErrors = useMemo(() => {
    if (
      !activeTab?.query ||
      !activeTabResult?.result ||
      activeTabResult.result.data.length === 0
    )
      return [];
    // eslint-disable-next-line react-compiler/react-compiler
    return textEditorRef?.current?.getLintErrors() ?? [];
  }, [activeTab?.query, activeTabResult.result]);

  const isLoopEnabled = useIsLoopEnabled();

  const { projectId } = useContext(ProjectContext);

  const { runQuery, abortQuery, running } = useBtqlQuery({
    getCurrentQuery,
    onQueryComplete: useEvent((result, error, lastQueryMs) => {
      updateTabResult(activeTabId, { result, error, lastQueryMs });
    }),
    setSelectedRow,
  });

  const activeTabHasNotRun =
    activeTab.query.trim().length > 0 && activeTabResult.lastQueryMs == null;
  const runSandboxQuery = useCallback(
    async ({
      query,
      title,
      openNewTab,
    }: {
      query: string;
      title?: string;
      openNewTab?: boolean;
    }) => {
      let tabId = activeTabId;
      // Open a new tab if it has not run yet and the user has entered a query or if explicitly requested
      if (activeTabHasNotRun || openNewTab) {
        tabId = newId();
        const newTab: BtqlTab = {
          id: tabId,
          name: title ?? `Query ${tabs.length + 1}`,
          query: "",
        };
        setTabs((prevTabs) =>
          produce(prevTabs, (draft) => {
            draft.push(newTab);
          }),
        );
        setTabResults((prevTabResults) =>
          produce(prevTabResults, (draft) => {
            draft.push({ id: tabId });
          }),
        );
        setActiveTabId(tabId);
        // Retitle the tab if a title is provided by the LLM
      } else if (title) {
        setTabs((prevTabs) =>
          produce(prevTabs, (draft) => {
            const tabIndex = draft.findIndex((tab) => tab.id === tabId);
            if (tabIndex !== -1) {
              draft[tabIndex].name = title;
            }
          }),
        );
      }
      updateTabQuery(tabId, query);
      return runQuery(query);
    },
    [
      activeTabId,
      activeTabHasNotRun,
      updateTabQuery,
      runQuery,
      tabs.length,
      setTabs,
      setTabResults,
      setActiveTabId,
    ],
  );
  const onMetaEnter = useCallback(
    () => (running ? abortQuery() : runQuery()),
    [running, abortQuery, runQuery],
  );

  const tableSchema = useMemo(() => {
    if (!activeTabResult?.result?.schema) {
      return undefined;
    }
    return parseBtqlSchema(null /*ignored*/, activeTabResult.result.schema)
      .fields;
  }, [activeTabResult?.result?.schema]);

  const chartData = useMemo(() => {
    if (
      !activeTab?.query ||
      !activeTabResult?.result?.data ||
      activeTabResult.result.data.length === 0
    ) {
      return null;
    }

    try {
      const ast = parseQuery(activeTab.query);
      const chartKind = classifyQuery(ast);

      if (chartKind === "table" || chartKind === "single-value") {
        return null;
      }

      return {
        ast,
        chartKind,
        data: activeTabResult.result.data,
      };
    } catch (error) {
      console.warn("Failed to parse query for charts:", error);
      return null;
    }
  }, [activeTab.query, activeTabResult.result?.data]);

  const contextSelectorData = useMemo(
    () => ({
      dataSources,
      btqlTabs: tabs.map((tab, index) =>
        makeBtqlTabContextObject(tab, tabResults[index]),
      ),
    }),
    [dataSources, tabs, tabResults],
  );

  // Populate the chat session with the active tab's context object when a new session is created
  const getNewSessionOverrides = useEvent(() => {
    return {
      contextObjects: {
        [`btql-${activeTab.id}`]: makeBtqlTabContextObject(
          activeTab,
          activeTabResult,
        ),
      },
    };
  });

  const hasChartData = !!chartData;
  const chartButton = useMemo(
    () =>
      hasChartData && (
        <BasicTooltip tooltipContent="Show chart">
          <Button
            size="xs"
            variant="ghost"
            className={cn(
              "-mr-3 size-7 text-primary-500",
              showCharts && "bg-primary-100",
            )}
            onClick={() => setShowCharts(!showCharts)}
            Icon={BarChart3}
            title="Show chart"
          />
        </BasicTooltip>
      ),
    [hasChartData, showCharts, setShowCharts],
  );

  if (!dataSources.projects && projectsPending) {
    return <TableSkeleton />;
  }

  if (!dataSources.projects) {
    return <CheckOrg params={{ org: orgName }}>{null}</CheckOrg>;
  }

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <CheckOrg params={{ org: orgName }}>
      <OptimizationProvider
        runSandboxQuery={runSandboxQuery}
        queryProjectId={projectId ?? ""}
      >
        <GlobalChatProvider getNewSessionOverrides={getNewSessionOverrides}>
          <ChatContextSetter
            activeTab={activeTab}
            activeTabResult={activeTabResult}
          />
          <Tabs value={activeTabId} onValueChange={setActiveTabId}>
            <div className="sticky top-0 z-10 flex h-10 items-stretch">
              <BTQLEditorTabs
                activeTabId={activeTabId}
                onChangeActiveTab={(tabId) => {
                  setActiveTabId(tabId);
                  setSelectedRow(null);
                  setActiveRow(null);
                }}
                tabs={tabs}
                setTabs={setTabs}
                setTabResults={setTabResults}
              />
              <div className="flex items-center justify-end gap-2 bg-primary-50 pl-1 pr-3">
                <CopyBtqlCodeSnippet value={activeTab?.query || ""} />
                <BtqlRunButton
                  runQuery={runQuery}
                  abortQuery={abortQuery}
                  running={running}
                  value={activeTab?.query ?? ""}
                />
                {isLoopEnabled && (
                  <OptimizationChat
                    contextSelectorData={contextSelectorData}
                    onRunInSandbox={runSandboxQuery}
                  />
                )}
              </div>
              <DockedChatSpacer />
            </div>
            <BodyWrapper
              innerClassName={cn(tabs[0].id === activeTabId && "rounded-none")}
              outerClassName={cn(HEIGHT_WITH_DOUBLE_TOP_OFFSET)}
            >
              <ResizablePanelGroup
                autoSaveId="btqlPanelLayout"
                direction="horizontal"
                className="flex w-full"
              >
                <ResizablePanel
                  className="relative flex min-w-48 flex-1"
                  id="main"
                  order={0}
                >
                  <div className="flex max-w-full flex-1">
                    <div
                      ref={scrollContainerRef}
                      className={cn(
                        "flex-1 flex-col max-w-full overflow-auto px-3",
                        {
                          "pb-6":
                            activeTabResult.result &&
                            activeTabResult.result.data.length > 0,
                        },
                      )}
                    >
                      <div
                        className={cn(
                          "sticky -left-3 -mx-3 -mt-3 flex h-[50%] flex-col border-b pt-3 transition-all bg-primary-50",
                          {
                            "h-[calc(100vh-185px)]":
                              !activeTabResult.result ||
                              activeTabResult.result.data.length === 0,
                          },
                        )}
                        ref={scrollMarginRef}
                      >
                        {tabs.map((tab) => (
                          <TabsContent
                            key={tab.id}
                            value={tab.id}
                            className="m-0 h-full bg-background"
                          >
                            <SandboxBtqlEditor
                              tab={tab}
                              updateTabQuery={updateTabQuery}
                              onMetaEnter={onMetaEnter}
                              dataSources={dataSources}
                              textEditorRef={textEditorRef}
                              activeTabId={activeTabId}
                            />
                          </TabsContent>
                        ))}
                      </div>
                      {lintErrors.length > 0 && (
                        <ErrorBanner
                          skipErrorReporting
                          className="sticky left-0 flex"
                        >
                          <div className="flex w-full items-center gap-2">
                            {lintErrors.map((error) => (
                              <div key={error.message}>{error.message}</div>
                            ))}
                            <FixLoopButton
                              query={activeTab?.query || ""}
                              error={
                                lintErrors.reduce(
                                  (acc, error) => acc + error.message,
                                  `\n\n`,
                                ) || ""
                              }
                              className="ml-auto"
                            />
                          </div>
                        </ErrorBanner>
                      )}
                      <div className="flex flex-1 flex-col items-stretch gap-4 @container/query-results">
                        {chartData && showCharts && (
                          <div className="relative mt-2 flex flex-1 justify-center rounded-lg border-primary-100 bg-primary-50 px-4 py-3">
                            <Button
                              size="xs"
                              variant="ghost"
                              onClick={() => setShowCharts(false)}
                              Icon={X}
                              className="absolute right-1 top-1 text-primary-500 hover:text-primary-700"
                            />
                            <ErrorBoundary
                              fallback={
                                <div className="flex h-32 items-center justify-center text-sm text-primary-500">
                                  Error rendering chart
                                </div>
                              }
                            >
                              <div className="max-w-[1200px] flex-1">
                                <BTQLChartRenderer
                                  ast={chartData.ast}
                                  chartKind={chartData.chartKind}
                                  query={activeTab?.query || ""}
                                  data={chartData.data}
                                  maxCategories={15}
                                />
                              </div>
                            </ErrorBoundary>
                          </div>
                        )}
                        <div className="flex min-w-fit flex-col">
                          <QueryResults
                            scrollContainerRef={scrollContainerRef}
                            scrollMarginRef={scrollMarginRef}
                            rawResult={activeTabResult.result ?? null}
                            error={activeTabResult.error ?? null}
                            lastQueryMs={activeTabResult.lastQueryMs ?? null}
                            activeRow={activeRow}
                            setActiveRow={setActiveRow}
                            setSelectedRow={setSelectedRow}
                            chartButton={chartButton}
                            fixLoopButton={
                              <FixLoopButton
                                query={activeTab?.query || ""}
                                error={activeTabResult.error ?? ""}
                                className="ml-auto"
                              />
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </ResizablePanel>
                {selectedRow && (
                  <>
                    <ResizableHandle />
                    <ResizablePanel
                      id="trace"
                      order={1}
                      className="flex flex-none"
                      minSize={detailsMinWidth}
                    >
                      <div className="flex flex-1 flex-col overflow-hidden">
                        <ErrorBoundary
                          fallback={
                            <ErrorBanner skipErrorReporting>
                              There was a problem rendering the row details
                            </ErrorBanner>
                          }
                        >
                          <RowDetailSheet
                            rowData={selectedRow.row}
                            fields={tableSchema}
                            onClose={() => setSelectedRow(null)}
                            rowIdx={selectedRow.index}
                          />
                        </ErrorBoundary>
                      </div>
                    </ResizablePanel>
                  </>
                )}
                <DockedChatSpacer />
              </ResizablePanelGroup>
            </BodyWrapper>
          </Tabs>
        </GlobalChatProvider>
      </OptimizationProvider>
    </CheckOrg>
  );
}

/** Wrapper component to enable access to useGlobalChat */
const SandboxBtqlEditor = ({
  tab,
  updateTabQuery,
  onMetaEnter,
  dataSources,
  textEditorRef,
  activeTabId,
}: {
  tab: BtqlTab;
  updateTabQuery: (tabId: string, query: string) => void;
  onMetaEnter: () => void;
  dataSources: {
    projects: SearchableItemInfo[];
    datasets: SearchableItemInfo[];
    experiments: SearchableItemInfo[];
    promptSessions: SearchableItemInfo[];
    orgs: SearchableItemInfo[];
  };
  textEditorRef: RefObject<TextEditorHandle<string> | null>;
  activeTabId: string;
}) => {
  const { handleSendMessage, setIsChatOpen } = useGlobalChat();
  const onFixWithLoop = useCallback(
    (message: string) => {
      handleSendMessage(
        {
          id: crypto.randomUUID(),
          type: "user_message",
          message: message,
        },
        {
          clearContextObjects: false,
          clearUserMessage: false,
        },
      );
      setTimeout(() => {
        setIsChatOpen(true);
      }, 250);
    },
    [handleSendMessage, setIsChatOpen],
  );

  return (
    <BtqlEditor
      mode="query"
      className="h-full overflow-auto p-3"
      value={tab.query}
      onDebouncedSave={(value) => updateTabQuery(tab.id, value)}
      onMetaEnter={onMetaEnter}
      dataSources={dataSources}
      textEditorRef={tab.id === activeTabId ? textEditorRef : undefined}
      onFixWithLoop={onFixWithLoop}
    />
  );
};
