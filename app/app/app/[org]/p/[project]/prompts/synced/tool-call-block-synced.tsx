import {
  DataTextEditor,
  EDITABLE_DATA_MODES,
  type TextEditorValue,
} from "#/ui/data-text-editor";
import {
  chatCompletionMessageToolCallSchema,
  type ToolCall,
} from "@braintrust/typespecs";
import { useCallback, useState } from "react";
import { cn } from "#/utils/classnames";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { AlertTriangle } from "lucide-react";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { type TransactionId } from "braintrust/util";
import { useSyncedPrompts } from "./use-synced-prompts";
import { z } from "zod";
import { zodErrorToString } from "#/utils/validation";

const REDLISTED_TOOL_CALL_MODES = ["text", "schema-builder"];
const RENDER_OPTIONS = EDITABLE_DATA_MODES.filter(
  (m) => !REDLISTED_TOOL_CALL_MODES.includes(m),
);

interface Props {
  value: ToolCall[];
  triggerSave: () => Promise<TransactionId | null>;
  runPrompts: () => void;
  autoFocus?: boolean;
  isReadOnly?: boolean;
  promptId: string;
  messageIdx: number;
}

const ToolCallBlockSynced = ({
  value,
  triggerSave,
  runPrompts,
  autoFocus,
  isReadOnly,
  promptId,
  messageIdx,
}: Props) => {
  const { updateMessageToolCalls_NO_SAVE } = useSyncedPrompts();
  const [error, setError] = useState<string | null>(null);

  const onMetaEnter = useCallback(
    (type: "cmd" | "shift") => {
      if (type === "cmd") {
        triggerSave();
        runPrompts();
      }
    },
    [triggerSave, runPrompts],
  );

  // TODO: is validating onChange too much? should we do it in some callback passed to onSave?
  const onChange = useCallback(
    (value: TextEditorValue) => {
      const editorValue = z
        .array(chatCompletionMessageToolCallSchema)
        .safeParse(value);
      if (editorValue.success) {
        updateMessageToolCalls_NO_SAVE({
          id: promptId,
          index: messageIdx,
          toolCalls: editorValue.data,
        });
        setError(null);
      } else {
        // Set error but don't save if it's invalid
        setError(zodErrorToString(editorValue.error, 0, true));
      }
    },
    [updateMessageToolCalls_NO_SAVE, promptId, messageIdx],
  );

  return (
    <div
      className={cn(
        "mr-3 mt-3 flex grow flex-col rounded-md border bg-primary-100",
        {
          "border-bad-600": !!error,
        },
      )}
    >
      <div className="flex justify-between p-2 pb-1.5 text-xs font-medium">
        Tool calls
        <Tooltip>
          <TooltipTrigger>
            <AlertTriangle
              className={cn("size-3 text-bad-600", {
                hidden: !error,
              })}
            />
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent className="text-xs">{error}</TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </div>
      <DataTextEditor
        className="flex grow border-0 bg-transparent text-sm"
        onMetaEnter={onMetaEnter}
        readOnly={isReadOnly}
        autoFocus={autoFocus}
        value={value}
        placeholder="An array of JSON tool function calls"
        onSave={triggerSave}
        onChange={onChange}
        tabAutocomplete={true}
        formatOnBlur
        allowedRenderOptions={RENDER_OPTIONS}
      />
    </div>
  );
};

export default ToolCallBlockSynced;
