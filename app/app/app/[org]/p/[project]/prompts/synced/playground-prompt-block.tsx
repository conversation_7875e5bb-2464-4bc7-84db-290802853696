import { cn } from "#/utils/classnames";
import { type ModelSpec } from "@braintrust/proxy/schema";
import { MessageCircleOff } from "lucide-react";
import { useCallback, type RefObject } from "react";
import { type PromptData, type ModelParams } from "@braintrust/typespecs";

import ModelParameters from "#/app/app/[org]/prompt/[prompt]/model-parameters";
import { ModelDropdown } from "#/app/app/[org]/prompt/[prompt]/ModelDropdown";
import { type ModelDetails } from "#/ui/prompts/models";
import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import { SavedTaskBarSynced } from "./saved-task-bar-synced";
import PromptBlockSynced from "./prompt-block-synced";
import { DiffedPromptDataSynced } from "./diffed-prompt-data-synced";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { type SavedPromptMeta } from "../../playgrounds/[playground]/use-saved-prompt-meta";
import { type Extension } from "@codemirror/state";
import { useSyncedPrompts } from "./use-synced-prompts";
import { NoAISecretsLink } from "#/ui/prompts/empty";
import { buttonVariants } from "#/ui/button";
import { type TaskEditConfirmationData } from "#/utils/optimization/provider";
import LoopTaskEditBlock from "#/ui/optimization/loop-task-edit-block";

interface PlaygroundPromptBlockProps {
  isReadOnly?: boolean;
  index: number;
  orgName: string;
  projectId?: string;
  playgroundId?: string; // The actual playground ID (promptSessionId)
  promptId: string;
  promptData?: PromptData;
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  allAvailableModels: Record<string, ModelSpec>;
  onRunPrompts: VoidFunction;
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
  copilotContext?: PlaygroundCopilotContext;
  containerRef: RefObject<HTMLDivElement | null>;
  diffId?: string;
  datasetId?: string;
  extensions?: Extension[];
  rowData?: Record<string, unknown>;
  extraMessagesPath?: string | null;
  showNoConfiguredSecretsMessage: boolean;
  loopEditConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (data: TaskEditConfirmationData | null) => void;
}

export const PlaygroundPromptBlock = ({
  isReadOnly,
  index,
  orgName,
  projectId,
  playgroundId,
  promptId,
  promptData,
  modelOptionsByProvider,
  allAvailableModels,
  onRunPrompts,
  savedPromptMeta,
  copilotContext,
  containerRef,
  diffId,
  datasetId,
  extensions,
  rowData,
  extraMessagesPath,
  showNoConfiguredSecretsMessage,
  loopEditConfirmationData,
  setEditTaskConfirmationData,
}: PlaygroundPromptBlockProps) => {
  const { updateModelParams, updateModel, saveSyncedPrompt } =
    useSyncedPrompts();
  const onSavePrompt = useCallback(
    () => saveSyncedPrompt(promptId),
    [promptId, saveSyncedPrompt],
  );
  const onChangeModel = useCallback(
    (model: string) => updateModel({ id: promptId, model }),
    [promptId, updateModel],
  );
  const onSaveModelParams = useCallback(
    async (params: ModelParams) => updateModelParams({ id: promptId, params }),
    [promptId, updateModelParams],
  );

  const modelParams = promptData?.options?.params;
  const currentModel = promptData?.options?.model;
  const currentAvailableModel = currentModel
    ? allAvailableModels[currentModel]
    : undefined;

  const hasEditConfirmationData =
    loopEditConfirmationData?.type === "edit_task" &&
    loopEditConfirmationData.index === index;

  return !hasEditConfirmationData ? (
    <>
      <div
        className={cn("flex flex-none items-center border-b p-2", {
          "border-none": !currentModel,
        })}
      >
        {showNoConfiguredSecretsMessage ? (
          <NoAISecretsLink
            orgName={orgName}
            className={cn(
              buttonVariants({ variant: "ghost", size: "xs" }),
              "flex justify-start items-center gap-2 text-primary-600",
            )}
            suffix=" to select a model"
          />
        ) : (
          <>
            <ModelDropdown
              isReadOnly={isReadOnly}
              orgName={orgName}
              currentModel={currentModel}
              onChange={onChangeModel}
              isLoading={!!promptData?.origin?.prompt_id && !currentModel}
              modelOptionsByProvider={modelOptionsByProvider}
              currentModelNotFound={!!currentModel && !currentAvailableModel}
            />
            {currentModel && currentAvailableModel && !isReadOnly && (
              <ModelParameters
                modelSpec={currentAvailableModel}
                params={modelParams}
                saveParams={onSaveModelParams}
              />
            )}
          </>
        )}
      </div>
      <div className="flex-auto overflow-auto p-2">
        {diffId ? (
          <DiffedPromptDataSynced id={promptId} diffId={diffId} />
        ) : currentModel &&
          // For an instant, allAvailableModels may not include the current model. If not, hold off on rendering the prompt block.
          (isReadOnly || currentAvailableModel) ? (
          <PromptBlockSynced
            isReadOnly={isReadOnly}
            idx={index}
            isInPlayground
            model={currentModel}
            allAvailableModels={allAvailableModels}
            triggerSave={onSavePrompt}
            runPrompts={onRunPrompts}
            data={promptData}
            extensions={extensions}
            autoFocus
            copilotContext={copilotContext}
            promptId={promptId}
            playgroundId={playgroundId}
            datasetId={datasetId}
            rowData={rowData}
            extraMessagesPath={extraMessagesPath}
          />
        ) : !currentModel ? (
          <TableEmptyState
            className="border-none"
            Icon={MessageCircleOff}
            labelClassName="text-sm"
            label="There is no model selected for this prompt"
          >
            <span className="text-xs text-primary-500">
              Select a model above to get started
            </span>
          </TableEmptyState>
        ) : null}
      </div>
      {!isReadOnly && (
        <SavedTaskBarSynced
          containerRef={containerRef}
          isReadOnly={isReadOnly}
          orgName={orgName}
          promptData={promptData}
          origin={promptData?.origin}
          promptId={promptId}
          projectId={projectId}
          savedPromptMetaName={savedPromptMeta[promptId]?.name}
          type="prompt"
        />
      )}
    </>
  ) : (
    <LoopTaskEditBlock
      loopEditConfirmationData={loopEditConfirmationData}
      setEditTaskConfirmationData={setEditTaskConfirmationData}
      promptId={promptId}
    />
  );
};
