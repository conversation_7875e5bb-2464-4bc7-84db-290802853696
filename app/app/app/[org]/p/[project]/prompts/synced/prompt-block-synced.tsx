import { But<PERSON> } from "#/ui/button";
import PlainDropdown from "#/ui/plain-dropdown";
import { type TextEditorProps } from "#/ui/text-editor";
import {
  ChatPlaceholders,
  CompletionPlaceholders,
} from "#/utils/ai/placeholders";
import {
  type OpenAIModelParams,
  type MessageRole,
  type SavedFunctionId,
  type FunctionObjectType,
} from "@braintrust/typespecs";
import {
  type ModelSpec,
  modelProviderHasTools,
} from "@braintrust/proxy/schema";
import { memo, useCallback, useContext, useMemo, useState } from "react";
import { cn } from "#/utils/classnames";
import {
  BetweenHorizontalStart,
  Clipboard,
  File,
  MessageSquare,
  Minus,
  Plus,
  PocketKnife,
  XIcon,
} from "lucide-react";
import { type TransactionId } from "#/utils/duckdb";
import { type PromptData } from "#/ui/prompts/schema";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { MessageActionButton } from "../message-action-button";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { Tools } from "../tools";
import { ProjectContext } from "../../projectContext";
import React from "react";
import { OutputFormat } from "../output-format";
import { type structuredOutputSchema } from "../structured-output";
import { toast } from "sonner";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { deserializePlainStringAsJSON } from "braintrust";
import { InfoBanner } from "#/ui/info-banner";
import MessageBlockSynced from "./message-block-synced";
import { MESSAGE_ROLES, useSyncedPrompts } from "./use-synced-prompts";
import { type z } from "zod";
import ToolCallBlockSynced from "./tool-call-block-synced";
import { FocusScope } from "@react-aria/focus";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import Link from "next/link";
import { useHotkeys } from "react-hotkeys-hook";
import { parse } from "#/utils/string";
import { BasicTooltip } from "#/ui/tooltip";
import { Spinner } from "#/ui/icons/spinner";
import {
  type ChatCompletionMessage,
  useRunPrompt,
  type StreamChunkData,
} from "#/ui/prompts/chat/use-run-prompt";

export type OnSaveToolFunctionsFn = (
  tools: SavedFunctionId[],
) => Promise<TransactionId | null>;

export type OnSaveResponseTypeFn = (
  type: "json_schema" | "json_object" | "text",
) => Promise<void>;

export type AgentPosition = "first" | "later";

export interface PromptBlockProps {
  isReadOnly?: boolean;
  agentPosition?: AgentPosition;
  data?: PromptData;
  model: string;
  allAvailableModels: { [name: string]: ModelSpec };
  triggerSave: () => Promise<TransactionId | null>;
  runPrompts: () => void;
  idx: number;
  extensions: TextEditorProps["extensions"];
  autoFocus?: boolean;
  disableTools?: boolean;
  copilotContext?: CopilotContextBuilder;
  isInPlayground?: boolean;
  datasetId?: string;
  promptId: string;
  playgroundId?: string; // The actual playground ID (promptSessionId)
  type?: FunctionObjectType;
  rowData?: Record<string, unknown>;
  enableHotkeys?: boolean;
  extraMessagesPath?: string | null;
}

const PromptBlockSynced = memo(
  ({
    isReadOnly,
    agentPosition,
    data,
    model,
    allAvailableModels,
    triggerSave,
    runPrompts,
    extensions,
    disableTools,
    copilotContext,
    isInPlayground,
    datasetId,
    promptId,
    playgroundId,
    type,
    rowData,
    enableHotkeys,
    extraMessagesPath,
  }: PromptBlockProps) => {
    const { orgName } = useContext(ProjectContext);

    const {
      updateStructuredOutput,
      updateResponseType,
      updateTools,
      addMessage,
      removeMessage,
      updateMessageRole,
      addMessagePart,
      updateMessageToolId_NO_SAVE,
      toggleMessageToolCalls,
      focusedEditor,
    } = useSyncedPrompts();

    const onSaveStructuredOutput = useCallback(
      async (data: z.infer<typeof structuredOutputSchema>) => {
        await updateStructuredOutput({ id: promptId, data });
      },
      [promptId, updateStructuredOutput],
    );

    const onSaveResponseType = useCallback(
      async (type: "json_schema" | "json_object" | "text") => {
        await updateResponseType({ id: promptId, type });
      },
      [promptId, updateResponseType],
    );

    const {
      format,
      flavor,
      multimodal: isModelMultimodal,
    } = allAvailableModels[model] || {};

    const isMultimodalActive = useCallback(
      (role?: MessageRole) => !!isModelMultimodal && role === "user",
      [isModelMultimodal],
    );

    const canCallTools = useCallback(
      (role?: MessageRole) =>
        modelProviderHasTools[format] && role === "assistant",
      [format],
    );

    const validRoles = MESSAGE_ROLES[format];
    const messages =
      data?.prompt?.type === "chat"
        ? data.prompt.messages
        : [{ ...data?.prompt, role: undefined }];

    const debouncedSave = useDebouncedCallback(triggerSave, 500);

    const isChat = flavor === "chat";
    const supportsTools =
      isChat && modelProviderHasTools[format] && !data?.parser && !disableTools;
    const supportsOutputFormat = isChat && !data?.parser;
    const supportsStructuredOutput = supportsTools;

    // TODO: bring back variable detection
    const hasVariable = false;

    const [promptVariableTipDismissed, setPromptVariableTipDismissed] =
      useEntityStorage({
        entityType: "dismissableMessages",
        key: "promptVariableTipDismissed",
        entityIdentifier: orgName,
      });

    const rawToolsString =
      data?.prompt?.type === "chat" ? data?.prompt?.tools : null;
    const rawTools: Record<string, unknown>[] | null = useMemo(() => {
      if (!rawToolsString) return null;
      return deserializePlainStringAsJSON(rawToolsString).value ?? [];
    }, [rawToolsString]);

    const hasTools =
      (data?.tool_functions?.length ?? 0) > 0 || (rawTools?.length ?? 0) > 0;

    const variableTipText = (
      <>
        Try inserting dataset variables from{" "}
        <code className="font-semibold text-comparison-700">{`{{input}}`}</code>
        ,{" "}
        <code className="font-semibold text-comparison-700">{`{{expected}}`}</code>
        , or{" "}
        <code className="font-semibold text-comparison-700">{`{{metadata}}`}</code>
      </>
    );

    const runData = useMemo(() => {
      if (!rowData) return null;

      return {
        input: parse(rowData?.input),
        expected: parse(rowData?.expected),
        metadata: parse(rowData?.metadata),
      };
    }, [rowData]);

    const [previewText, setPreviewText] = useState("");

    const { runPrompt: generateMessage, isGenerating } = useRunPrompt({
      promptData: data,
      runData,
      onStreamChunk: useCallback(
        (data: StreamChunkData) => setPreviewText(data.previewText),
        [],
      ),
      onStreamDone: useCallback(
        (messages: ChatCompletionMessage[]) => {
          setPreviewText("");
          addMessage({ id: promptId, message: messages[0] });
        },
        [addMessage, promptId],
      ),
    });

    useHotkeys(
      "M",
      () => {
        addMessage({ id: promptId });
      },
      {
        enabled: enableHotkeys,
        preventDefault: true,
      },
    );

    return (
      <FocusScope>
        <div className="flex flex-auto grow flex-col">
          {messages.map((message, index) => (
            <MessageContainer
              key={`${index}${focusedEditor.current?.messageIndex === index && focusedEditor.current?.focusKey ? `-${focusedEditor.current?.focusKey}` : ""}`}
              isReadOnly={isReadOnly}
              flavor={flavor}
              isChat={isChat}
              validRoles={validRoles}
              role={message?.role}
              actionsSlot={
                !isReadOnly && (
                  <div className="-mr-1 flex gap-1 opacity-0 transition-opacity group-hover/prompt:opacity-100">
                    <MessageActionButton
                      onClick={() => {
                        if (!message?.content) return;
                        if (typeof message.content === "string") {
                          navigator.clipboard.writeText(message.content);
                          toast.success("Message copied to clipboard");
                          return;
                        }
                        if (Array.isArray(message.content)) {
                          const textPart = message.content.find(
                            (part) => part.type === "text",
                          );
                          if (textPart && "text" in textPart) {
                            navigator.clipboard.writeText(textPart.text);
                            toast.success("Message copied to clipboard");
                          }
                        }
                      }}
                      tooltip="Copy message to clipboard"
                      Icon={Clipboard}
                    />
                    {canCallTools(message.role) && (
                      <MessageActionButton
                        onClick={() =>
                          toggleMessageToolCalls({
                            id: promptId,
                            index,
                          })
                        }
                        tooltip="Toggle tool calls"
                        Icon={PocketKnife}
                      />
                    )}

                    <MessageActionButton
                      onClick={() => {
                        addMessage({ id: promptId, afterIndex: index });
                      }}
                      tooltip="Add message"
                      Icon={Plus}
                    />
                    {messages.length > 1 && (
                      <MessageActionButton
                        onClick={() => {
                          removeMessage({ id: promptId, index });
                        }}
                        tooltip="Remove message"
                        Icon={Minus}
                      />
                    )}
                  </div>
                )
              }
              onRoleChange={(role) =>
                updateMessageRole({
                  id: promptId,
                  index,
                  newRole: role,
                  newIsMultimodal: isMultimodalActive(role),
                })
              }
            >
              {message?.role === "tool" && (
                <div className="mb-2">
                  <PlainInput
                    value={message.tool_call_id}
                    onChange={(e) => {
                      updateMessageToolId_NO_SAVE({
                        id: promptId,
                        index,
                        toolId: e.target.value,
                      });
                      debouncedSave();
                    }}
                    disabled={isReadOnly}
                    required
                    placeholder="Enter tool call ID"
                    className="-ml-2 block h-8 w-full max-w-xs px-2 font-mono text-xs placeholder:font-inter"
                  />
                </div>
              )}
              <MessageBlockSynced
                isReadOnly={isReadOnly}
                data={message?.content}
                isMultimodal={isMultimodalActive(message.role)}
                triggerSave={triggerSave}
                runPrompts={runPrompts}
                extensions={extensions}
                textPlaceholder={
                  flavor === "completion"
                    ? CompletionPlaceholders[format]
                    : message?.role &&
                      ChatPlaceholders[format] &&
                      ChatPlaceholders[format][message.role]
                }
                copilotContext={copilotContext}
                promptId={promptId}
                playgroundId={playgroundId}
                messageIdx={index}
                messageRole={message?.role}
              />
              {!isReadOnly && isChat && isMultimodalActive(message.role) && (
                <div className="flex gap-1 pt-3">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        className="text-primary-700"
                        Icon={Plus}
                        size="xs"
                        isDropdown
                      >
                        Message part
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem
                        onSelect={() =>
                          // This is a hack to ensure the new message part is added and auto-focused _after_ radix refocuses the dropdown trigger
                          setTimeout(
                            () =>
                              addMessagePart({
                                id: promptId,
                                index,
                                type: "text",
                              }),
                            50,
                          )
                        }
                      >
                        <MessageSquare className="size-3" /> Text
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() =>
                          setTimeout(
                            () =>
                              addMessagePart({
                                id: promptId,
                                index,
                                type: "image_url",
                              }),
                            50,
                          )
                        }
                      >
                        <File className="size-3" /> File
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
              {message?.role === "assistant" && message?.tool_calls && (
                <ToolCallBlockSynced
                  value={message.tool_calls}
                  triggerSave={triggerSave}
                  runPrompts={runPrompts}
                  promptId={promptId}
                  messageIdx={index}
                  // TODO: bring back focus management
                  // autoFocus={autoFocus || focusedEditor === i}
                />
              )}
            </MessageContainer>
          ))}
          {previewText && (
            <MessageContainer
              isReadOnly
              flavor={flavor}
              isChat={isChat}
              validRoles={validRoles}
              role="assistant"
            >
              <div className="text-sm text-primary-500">{previewText}</div>
            </MessageContainer>
          )}
          {isChat && (
            <>
              {!promptVariableTipDismissed && isInPlayground && !isReadOnly && (
                <div className="mb-2 flex items-center gap-2 rounded-md border border-primary-200/50 bg-primary-100/80 pl-3 pr-0 text-xs text-primary-500">
                  <div className="flex-1 py-2 leading-normal">
                    {variableTipText}
                  </div>
                  <Button
                    size="icon"
                    transparent
                    className="size-8 flex-none text-primary-400"
                    Icon={XIcon}
                    onClick={() => {
                      setPromptVariableTipDismissed(true);
                    }}
                  />
                </div>
              )}
              {agentPosition === "first" && !isReadOnly && (
                <InfoBanner>{variableTipText}</InfoBanner>
              )}
              {agentPosition === "later" && !isReadOnly && (
                <InfoBanner>
                  Learn about{" "}
                  <Link
                    href="/docs/guides/functions/agents#variables"
                    target="_blank"
                    className="font-medium hover:underline"
                  >
                    variable interpolation
                  </Link>{" "}
                  in agents.
                </InfoBanner>
              )}
              {hasVariable &&
                !datasetId &&
                agentPosition !== "later" &&
                !isReadOnly && (
                  <div className="mb-2 rounded-md border border-primary-200/50 bg-primary-100/80 p-3 text-xs text-primary-500">
                    This prompt includes variables, but there is no dataset
                    selected.
                  </div>
                )}
              {extraMessagesPath && (
                <div className="mb-2 flex flex-none items-center gap-2 rounded-md border border-dashed border-primary-200 p-3 text-xs text-primary-500">
                  <BetweenHorizontalStart className="size-3 flex-none" />
                  <span>
                    Messages from the dataset path{" "}
                    <code className="font-semibold text-comparison-700">
                      {extraMessagesPath}
                    </code>{" "}
                    will be appended to this prompt
                  </span>
                </div>
              )}
              <div className="mb-2 flex w-full flex-wrap gap-2">
                {!isReadOnly && (
                  <Button
                    size="xs"
                    onClick={(e) => {
                      e.preventDefault();
                      addMessage({ id: promptId });
                    }}
                    className="flex-none"
                    Icon={Plus}
                  >
                    Message
                  </Button>
                )}
                {supportsTools && (
                  <Tools
                    selectedTools={data?.tool_functions}
                    onSave={async (tools, rawTools, toolChoice) =>
                      await updateTools({
                        id: promptId,
                        toolFunctions: tools ?? [],
                        rawTools,
                        toolChoice,
                      })
                    }
                    copilotContext={copilotContext}
                    rawToolsValue={rawTools}
                    toolChoice={
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                      data?.options?.params
                        ?.tool_choice as OpenAIModelParams["tool_choice"]
                    }
                    isReadOnly={isReadOnly}
                    type={type}
                  />
                )}
                {supportsOutputFormat && (
                  <OutputFormat
                    onSaveStructuredOutput={onSaveStructuredOutput}
                    currentInitData={data}
                    supportsStructuredOutput={supportsStructuredOutput}
                    onSaveResponseType={onSaveResponseType}
                    isReadOnly={isReadOnly}
                    // Reset state when the prompt version changes
                    key={data?.origin?.prompt_version ?? ""}
                  />
                )}
                {!isReadOnly &&
                  !isGenerating &&
                  messages[messages.length - 1]?.role === "user" && (
                    <BasicTooltip
                      tooltipContent={
                        hasTools
                          ? "Currently, generate message is not available for prompts that include tools"
                          : null
                      }
                    >
                      <Button
                        disabled={messages.length === 0 || hasTools}
                        size="xs"
                        Icon={Plus}
                        onClick={() => generateMessage()}
                        variant="ghost"
                        className="flex-none text-primary-600"
                      >
                        Generate message
                      </Button>
                    </BasicTooltip>
                  )}
                {isGenerating && (
                  <div className="flex w-fit items-center">
                    <Spinner className="my-auto size-3 text-primary-500" />
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </FocusScope>
    );
  },
);

const MessageContainer = ({
  isReadOnly,
  flavor,
  isChat,
  children,
  actionsSlot,
  onRoleChange,
  validRoles,
  role,
}: {
  isReadOnly?: boolean;
  flavor: "chat" | "completion";
  isChat: boolean;
  children: React.ReactNode;
  actionsSlot?: React.ReactNode;
  onRoleChange?: (role: MessageRole) => void;
  validRoles: MessageRole[];
  role?: MessageRole;
}) => {
  return (
    <div
      className={cn(
        `group/prompt flex flex-col mb-2 pb-3 pt-1 bg-transparent border border-primary-200/50 focus-within:border-primary-200 hover:bg-primary-200/20 focus-within:hover:bg-primary-200/60 focus-within:bg-primary-200/60 transition-colors rounded-md px-3 min-h-[2.5lh] cursor-text`,
        {
          "bg-transparent": isReadOnly,
          grow: flavor === "completion",
        },
      )}
    >
      {isChat && (
        <div className="mb-1 flex h-7 items-center">
          <div className="flex-auto text-sm font-medium capitalize">
            <PlainDropdown
              selectedOption={role}
              setSelectedOption={(role) => onRoleChange?.(role)}
              className="-ml-1.5 rounded-md px-1.5 py-1 text-xs text-primary-700 transition-all hover:bg-primary-200"
              disabled={isReadOnly}
              options={validRoles}
              itemClassName="capitalize"
            />
          </div>
          {actionsSlot}
        </div>
      )}

      {children}
    </div>
  );
};

PromptBlockSynced.displayName = "PromptBlockSynced";

export default PromptBlockSynced;
