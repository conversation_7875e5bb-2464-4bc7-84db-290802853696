"use client";

import {
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { AccessFailed } from "#/ui/access-failed";
import {
  type ClauseSpec,
  type ClauseType,
  makeTagsFilterTree,
  useScoreMetricsTopLevelFields,
} from "#/utils/search/search";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { type SavingState } from "#/ui/saving";
import { type ExpandedRowParams, type TraceViewParams } from "#/ui/trace/trace";
import { cn } from "#/utils/classnames";
import { doubleQuote } from "#/utils/sql-utils";
import { type UpdateRowFn, useMutableObject } from "#/utils/mutable-object";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Beaker } from "lucide-react";
import Link from "next/link";
import { ExperimentHeader, type ExperimentItem } from "./ExperimentHeader";
import { runAISearch } from "#/utils/ai-search/actions/events";
import { type ClientOptions } from "openai";
import {
  type BTQLTableDefinition,
  makeBtqlTagsFilter,
  useClauseChecker,
} from "#/utils/search-btql";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { usePanelSize } from "#/ui/use-panel-size";
import { TracePanel } from "#/ui/trace/trace-panel";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { extractLogicalSchemaItemsObject } from "@braintrust/local/query";
import {
  useActiveRowAndSpan,
  useDiffModeState,
  useHumanReviewState,
} from "#/ui/query-parameters";
import { type ExtendedExperiment } from "./experiment-actions";
import {
  BackendSummariesLoader,
  BackendSummariesProvider,
  type LoadExperimentScansParams,
  useLoadExperiment,
} from "./(queries)/loaders";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import { useTraceFullscreen } from "#/ui/trace/use-trace-fullscreen";
import { decodeURIComponentPatched } from "#/utils/url";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { DiffModeSwitch, useDiffModeOptions } from "#/ui/diff-mode-switch";
import {
  fetchBtql,
  useBtql,
  useIsRootAvailable,
  useIsRootBtqlSnippet,
} from "#/utils/btql/btql";
import { useObjectIdResolver } from "#/ui/use-object-id-resolver";
import {
  BUILT_IN_CUSTOM_COLUMNS,
  useCustomColumns,
} from "#/utils/custom-columns/use-custom-columns";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { GROUP_BY_INPUT_VALUE } from "#/ui/table/grouping/controls";
import { ExperimentContext } from "./experimentContext";
import { ExperimentSidebar } from "./experiment-sidebar";
import { type Permission } from "@braintrust/typespecs";
import { DUCKDB_PHYSICAL_SCHEMA } from "@braintrust/local/api-schema";
import { BRAINTRUST_AUDIT_LOG_LOGICAL_SCHEMA } from "@braintrust/local/api-schema";
import { useModelScan } from "./(queries)/models";
import {
  useExperimentScan,
  useComparisonExperimentScans,
} from "./(queries)/useExperiment";
import { ExperimentShareDialog } from "./experiment-share-dialog";
import { useComparisonQueries } from "./(queries)/(comparisons)/comparison-queries";
import {
  ExperimentTable,
  INITIALLY_VISIBLE_COLUMNS,
  NEVER_VISIBLE_COLUMNS,
  useExperimentTableProps,
} from "./experiment-table";
import { useOrg } from "#/utils/user";
import { Button } from "#/ui/button";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/color";
import { Bubble } from "#/ui/table/bubble";
import { type BatchUpdateRowFn } from "#/ui/arrow-table";
import useEvent from "react-use-event-hook";
import { type RowId } from "#/utils/diffs/diff-objects";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { useRecentExperiments } from "../clientpage";
import * as Query from "#/utils/btql/query-builder";
import { type ErrorSummary } from "./(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { arrowTypeToLogicalSchema } from "#/utils/arrow";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
  HEIGHT_WITH_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { useQueryClient } from "@tanstack/react-query";
import { useSessionToken } from "#/utils/auth/session-token";
import { BT_GROUP_BY_METADATA } from "#/ui/table/grouping/queries";
import { type AliasExpr, type Expr } from "@braintrust/btql/parser";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { OptimizationProvider } from "#/utils/optimization/provider";
import { GlobalChatProvider } from "#/ui/optimization/global-chat-provider";
import { DockedChatSpacer } from "../../playgrounds/[playground]/docked-chat-spacer";
import { useInferObjectSchemaPaths } from "#/ui/use-infer-object";
import { BT_ASSIGNMENTS, BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";
import { RealtimeStateBanner } from "#/ui/banners/realtime-state-banner";

export interface Params {
  org: string;
  project: string;
  experiment: string;
}

export type PanelLayout = {
  trace: number;
  sidebar: number;
  main: number;
};

const emptyArray: string[] = [];

export default function ClientPage({
  params,
  experiment,
  defaultPanelLayout,
  permissions,
}: {
  params: Params;
  experiment: ExtendedExperiment | null;
  defaultPanelLayout: PanelLayout;
  permissions: Permission[];
}) {
  // For public experiments accessed anonymously, the projectId will not be
  // available from the ProjectContext, but we still want to make it available
  // to the experiment in general, so we re-wrap the ProjectContext with an
  // updated projectId when available.
  const projectContext = useContext(ProjectContext);

  const memoizedProjectContext = useMemo(
    () => ({
      ...projectContext,
      projectId: projectContext.projectId || experiment?.project_id || "",
    }),
    [experiment?.project_id, projectContext],
  );
  const experimentName = decodeURIComponentPatched(params.experiment);

  const memoizedExperimentContext = useMemo(
    () => ({ experimentName, experimentId: experiment?.id || "" }),
    [experimentName, experiment?.id],
  );

  return (
    <ProjectContext.Provider value={memoizedProjectContext}>
      <ExperimentContext.Provider value={memoizedExperimentContext}>
        <BackendSummariesProvider>
          <ClientPageInner
            params={params}
            experiment={experiment}
            defaultPanelLayout={defaultPanelLayout}
            permissions={permissions}
          />
        </BackendSummariesProvider>
      </ExperimentContext.Provider>
    </ProjectContext.Provider>
  );
}

function ClientPageInner({
  params,
  experiment,
  defaultPanelLayout,
  permissions,
}: {
  params: Params;
  experiment: ExtendedExperiment | null;
  defaultPanelLayout: PanelLayout;
  permissions: Permission[];
}) {
  const experimentName = decodeURIComponentPatched(params.experiment);
  const pageIdentifier = "project-experiment-" + experiment?.project_id;

  const projectContext = useContext(ProjectContext);
  const { config, projectSettings, projectName, projectId } = projectContext;
  const { name: orgName, api_url: apiUrl } = useOrg();
  const btqlFlags = useBtqlFlags();
  const fastExperimentSummary = useIsFeatureEnabled("fastExperimentSummary");

  // TODO: Propagate user-defined metrics this way too
  const metricDefinitions = config.metricDefinitions;

  const { getOrRefreshToken } = useSessionToken();

  const [humanReviewState] = useHumanReviewState();

  // If this boolean is true, we freeze the `ready` signals for the table scans, which means we'll
  // stop updating the table and insight views. This is useful for human review, where we want to
  // rapidly make changes without paying the performance and rendering cost of the whole page updating.
  const tableScansPaused = humanReviewState === "1";
  const [clauseCheckerTable, setClauseCheckerTable] = useState<
    keyof (typeof DUCKDB_PHYSICAL_SCHEMA)["tables"] | BTQLTableDefinition | null
  >(fastExperimentSummary ? null : "experiment");

  const {
    clauseChecker: experimentClauseChecker,
    setTopLevelFields,
    setCustomColumnsSchema,
  } = useClauseChecker(clauseCheckerTable, !fastExperimentSummary);

  const auditLogTableSchema = useMemo(
    () => ({
      logical: extractLogicalSchemaItemsObject(
        "experiment",
        BRAINTRUST_AUDIT_LOG_LOGICAL_SCHEMA,
      ),
      physical: DUCKDB_PHYSICAL_SCHEMA.tables.audit_log,
    }),
    [],
  );
  const { clauseChecker: auditLogClauseChecker } =
    useClauseChecker(auditLogTableSchema);
  const clauseChecker = useMemo(() => {
    if (!experimentClauseChecker || !auditLogClauseChecker) {
      return null;
    }

    return async (clause: ClauseSpec<ClauseType>) => {
      const results = await Promise.all([
        experimentClauseChecker(clause),
        auditLogClauseChecker(clause),
      ]);
      const result = results.find((r) => r.type === "checked");
      return result ?? results[0];
    };
  }, [experimentClauseChecker, auditLogClauseChecker]);

  const hasExperiment = !!experiment;
  const viewParams: ViewParams | undefined = useMemo(
    () =>
      hasExperiment && projectId
        ? {
            objectType: "project",
            objectId: projectId,
            viewType: "experiment",
          }
        : undefined,
    [hasExperiment, projectId],
  );
  const viewProps = useViewStates({
    viewParams,
    clauseChecker,
    pageIdentifier,
  });
  const { search, setSearch } = viewProps;

  const { grouping, setGrouping } = viewProps;

  const projectedColumns: AliasExpr[] = useMemo(() => {
    const assignmentColumn: AliasExpr = {
      alias: BT_ASSIGNMENTS,
      expr: { btql: `metadata.${doubleQuote(BT_ASSIGNMENTS_META_FIELD)}` },
    };
    try {
      const parsed = JSON.parse(grouping);
      if (Array.isArray(parsed) && parsed[0] === "metadata") {
        return [
          assignmentColumn,
          {
            alias: BT_GROUP_BY_METADATA,
            expr: { btql: parsed.join(".") },
          },
        ];
      }
    } catch (e) {}
    return [
      assignmentColumn,
      {
        alias: BT_GROUP_BY_METADATA,
        expr: { op: "literal", value: null },
      },
    ];
  }, [grouping]);

  const filters = useMemo(
    () =>
      fastExperimentSummary
        ? {
            sql: (search.filter?.flatMap((f) => f.tree ?? []) || []).concat(
              ...makeTagsFilterTree(search),
            ),
            btql: (search.filter ?? [])
              .reduce<Expr[]>((acc, f) => {
                if (f.btql?.parsed) {
                  acc.push(f.btql.parsed);
                }
                return acc;
              }, [])
              .concat(makeBtqlTagsFilter(search)),
          }
        : undefined,
    [fastExperimentSummary, search],
  );

  const customColumnState = useCustomColumns({
    scope: projectId
      ? {
          object_type: "project",
          object_id: projectId,
          subtype: "experiment",
          variant: "experiment",
        }
      : undefined,
    projectedColumns: fastExperimentSummary
      ? undefined
      : BUILT_IN_CUSTOM_COLUMNS["experiment"],
  });

  const loadedExperiment = useLoadExperiment({
    btObject: {
      id: experiment?.id || "",
      name: experimentName,
      type: "experiment",
    },
    tableScansPaused,
    projectedColumns,
    filters,
    customColumns: customColumnState?.customColumnDefinitions,
  });
  const {
    refreshed: experimentReadyRaw,
    schema: experimentSchema,
    scan: experimentScanRaw,
    summary,
  } = loadedExperiment;
  const { refreshed: experimentAuditLogReady, scan: experimentAuditLogScan } =
    loadedExperiment.auditLog ?? {
      refreshed: 0,
      scan: null,
    };

  useEffect(() => {
    if (!summary?.schema) {
      return;
    }
    const logicalSchema = extractLogicalSchemaItemsObject("experiment");
    const physicalSchema = DUCKDB_PHYSICAL_SCHEMA.tables.experiment;

    for (const field of summary?.schema.fields) {
      if (logicalSchema.properties && !logicalSchema.properties[field.name]) {
        logicalSchema.properties[field.name] = arrowTypeToLogicalSchema(
          field.type,
        );
      }
      if (physicalSchema.columns && !physicalSchema.columns[field.name]) {
        physicalSchema.columns[field.name] = {
          type: { type: "varchar" },
          path: [field.name],
        };
      }
      physicalSchema.columns["scores"] = {
        path: ["scores"],
        type: { type: "struct_map", value: { type: "double" } },
      };
    }
    setClauseCheckerTable({
      logical: logicalSchema,
      physical: physicalSchema,
    });
  }, [summary?.schema]);

  const [savingState, setSavingState] = useState<SavingState>("none");

  const hasErrorField = useMemo(() => {
    return !!experimentSchema?.fields.find((f) => f.name === "error");
  }, [experimentSchema]);

  const comparisonKey = useMemo(
    () => (relation: string) => `${relation}."comparison_key"`,
    [],
  );

  const { modelSpecScan, allAvailableModelCosts } = useModelScan({
    id: experiment?.id ?? "",
  });

  const isRootAvailable = useIsRootAvailable();

  const loadExperimentScanParams: LoadExperimentScansParams = useMemo(() => {
    return {
      modelSpecScan,
      auditLogScan: experimentAuditLogScan,
      scoreConfig: config.scores,
      metricDefinitions,
      comparisonKey: projectSettings?.comparison_key ?? null,
      hasErrorField,
      isRootAvailable,
      customColumns: customColumnState.customColumnDefinitions,
    };
  }, [
    modelSpecScan,
    experimentAuditLogScan,
    config.scores,
    projectSettings?.comparison_key,
    hasErrorField,
    isRootAvailable,
    customColumnState.customColumnDefinitions,
    metricDefinitions,
  ]);

  const {
    data: experimentScan,
    projectedPaths,
    isExperimentEmpty,
    isPending: isExperimentPending,
    ready: primaryExperimentReady,
    error: experimentError,
  } = useExperimentScan({
    experiment: loadedExperiment,
    loadExperimentScanParams,
    search,
  });
  const { scoreFields = emptyArray } = experimentScan ?? {};

  useEffect(() => {
    if (isExperimentPending) {
      return;
    }
    if (customColumnState.isError || experimentError) {
      setCustomColumnsSchema?.(null);
      return;
    }
    setCustomColumnsSchema?.(experimentScan?.customColumnsSchema ?? null);
  }, [
    isExperimentPending,
    setCustomColumnsSchema,
    experimentScan?.customColumnsSchema,
    customColumnState.isError,
    experimentError,
  ]);

  useScoreMetricsTopLevelFields({
    scoreFields,
    setTopLevelFields,
    include: true,
  });

  const [selectedComparisonExperiments, _setSelectedComparisonExperiments] =
    useState<ExperimentItem[]>([]);

  const [diffMode, setDiffMode] = useDiffModeState();
  const {
    setSelectedComparisonExperiments,
    comparisonsInitialized,
    regressionFilters,
    setRegressionFilters,
    addRegressionFilter,
    clearRegressionFilters,
    comparisonExperimentData,
    isPending: isComparisonExperimentPending,
    errors: comparisonExperimentErrors,
  } = useComparisonExperimentScans({
    experiment: loadedExperiment,
    loadExperimentScanParams,
    selectedComparisonExperiments,
    onSetSelectedComparisonExperiments: _setSelectedComparisonExperiments,
    tableScansPaused,
    diffMode,
    setDiffMode,
    search,
    setSearch,
    enableSummary: true,
  });
  const queryError = useMemo(() => {
    return (
      loadedExperiment.error ??
      experimentError ??
      comparisonExperimentErrors?.find(Boolean) ??
      null
    );
  }, [loadedExperiment.error, experimentError, comparisonExperimentErrors]);

  const { diffModeOptions } = useDiffModeOptions({
    diffModeState: diffMode,
    setDiffMode,
    diffModeOptionParams: {
      comparisonExperimentsCount: selectedComparisonExperiments.length,
      experimentSchema,
      loading: !comparisonsInitialized || !experimentSchema,
    },
  });

  const [{ r: activeRowId }] = useActiveRowAndSpan();

  const [isSidebarCollapsed, setSidebarCollapsed] = useEntityStorage({
    entityType: "collapsibleSection",
    entityIdentifier: "experimentSidebar",
    key: "isCollapsed",
    defaultValue: false,
    onMount: true,
  });

  const setTableGrouping = useCallback(
    (valueOrUpdater: SetStateAction<string>) => {
      const next =
        typeof valueOrUpdater === "function"
          ? valueOrUpdater(grouping)
          : valueOrUpdater;
      setGrouping(next);
      if (next === GROUP_BY_NONE_VALUE) {
        setSearch((prev) => ({
          ...prev,
          sort: prev.sort?.filter((s) => s.comparison?.type !== "regression"),
        }));
      }
    },
    [grouping, setGrouping, setSearch],
  );

  const metadataGroupingOptions = useInferObjectSchemaPaths(
    useMemo(
      () => ({
        objectType: "experiment",
        objectId: experiment?.id ?? null,
        paths: ["metadata"],
      }),
      [experiment],
    ),
  );

  const tableGroupingOptions = useMemo(() => {
    if (!fastExperimentSummary) {
      return (experimentScan?.metadataRootFields ?? []).map((f) => ({
        label: f.join("."),
        value: JSON.stringify(f),
      }));
    } else {
      return (metadataGroupingOptions ?? []).map((opt) => {
        return {
          label: opt.slice(1).join("."),
          value: JSON.stringify(opt),
        };
      });
    }
  }, [
    experimentScan?.metadataRootFields,
    metadataGroupingOptions,
    fastExperimentSummary,
  ]);

  const tableGrouping = useMemo(
    () =>
      [
        GROUP_BY_INPUT_VALUE,
        ...tableGroupingOptions.map(({ value }) => value),
        ...(
          (fastExperimentSummary
            ? customColumnState.customColumnDefinitions
            : experimentScan?.customColumns) ?? []
        ).map(({ name }) => name),
      ].find((v) => v === grouping) ?? GROUP_BY_NONE_VALUE,
    [
      tableGroupingOptions,
      experimentScan?.customColumns,
      grouping,
      customColumnState.customColumnDefinitions,
      fastExperimentSummary,
    ],
  );

  const { experimentIds } = useRecentExperiments();
  const isRootBtqlSnippet = useIsRootBtqlSnippet();
  const errorQuery = useBtql({
    name: "Error query",
    query: useMemo(
      () =>
        experimentIds
          ? {
              from: Query.from("experiment", experimentIds),
              filter: {
                btql: isRootBtqlSnippet,
              },
              measures: [
                {
                  alias: "row_count",
                  expr: { btql: `count(${isRootBtqlSnippet})` },
                },
                {
                  alias: "error_count",
                  expr: {
                    btql: `COUNT(${isRootBtqlSnippet} ? error : NULL)`,
                  },
                },
                {
                  alias: "error_rate",
                  expr: {
                    btql: `error_count / row_count`,
                  },
                },
              ],
              dimensions: [
                { alias: "experiment_id", expr: { btql: "experiment_id" } },
              ],
            }
          : null,
      [experimentIds, isRootBtqlSnippet],
    ),
    disableLimit: true,
    expensive: true,
    brainstoreRealtime: true,
  });
  const errorSummaryData = useMemo(() => {
    const result: Record<string, ErrorSummary> = {};

    for (const row of errorQuery.data ?? []) {
      result[row.experiment_id] = {
        rowCount: Number(row.row_count),
        errorCount: Number(row.error_count),
        errorRate: Number(row.error_rate),
      };
    }
    return result;
  }, [errorQuery.data]);

  const { summaryBreakdownData, comparisonKeyFilterClause } =
    useComparisonQueries({
      experiment: experimentScan,
      columnVisibility: viewProps.columnVisibility,
      comparisonExperimentData,
      regressionFilters,
      search,
      tableGrouping,
      comparisonKey,
      scoreConfig: config.scores,
      hasErrorField,
      customColumns: experimentScan?.customColumns,
      fastExperimentSummary,
    });

  const experimentTableProps = useExperimentTableProps(
    useMemo(
      () => ({
        experiment: experimentScan,
        comparisonExperimentData,
        search,
        projectedPaths,
        diffMode,
        tableGrouping,
        setTableGrouping,
        comparisonKey,
        comparisonKeyFilterClause,
        addRegressionFilter,
        clearRegressionFilters,
        summaryBreakdownData,
        clauseChecker,
        setSearch,
        experimentScanRaw: loadedExperiment.scan,
        primaryExperimentReady: [primaryExperimentReady],
        enableStarColumn: true,
        viewProps,
        refetchDataParams:
          loadedExperiment.summary !== undefined && experiment?.id
            ? {
                objectType: "experiment",
                objectId: experiment.id,
              }
            : undefined,
        customColumns:
          loadedExperiment.summary !== undefined
            ? customColumnState.customColumnDefinitions
            : undefined,
        metricDefinitions,
      }),
      [
        experimentScan,
        comparisonExperimentData,
        search,
        projectedPaths,
        diffMode,
        tableGrouping,
        setTableGrouping,
        comparisonKey,
        comparisonKeyFilterClause,
        addRegressionFilter,
        clearRegressionFilters,
        summaryBreakdownData,
        clauseChecker,
        setSearch,
        loadedExperiment.scan,
        primaryExperimentReady,
        viewProps,
        experiment?.id,
        loadedExperiment.summary,
        customColumnState.customColumnDefinitions,
        metricDefinitions,
      ],
    ),
  );

  const histogramRef = useRef<{
    [scoreName: string]: { removeBrush: () => void };
  }>({});
  const {
    chartBrushFilters,
    setChartBrushFilter,
    removeChartBrushFilter,
    baseComparisonQuery,
  } = experimentTableProps;

  const columnVisibility = useMemo(() => {
    return {
      ...INITIALLY_VISIBLE_COLUMNS,
      ...viewProps.columnVisibility,
      ...Object.fromEntries(
        Array.from(NEVER_VISIBLE_COLUMNS).map((k) => [k, false]),
      ),
    };
  }, [viewProps.columnVisibility]);

  const clearChartFilter = useEvent((scoreName: string) => {
    removeChartBrushFilter(scoreName);
    histogramRef.current?.[scoreName]?.removeBrush();
  });

  const clearChartBrushFilters = useEvent(() => {
    Object.values(histogramRef.current ?? {}).forEach((ref) =>
      ref?.removeBrush(),
    );
    experimentTableProps.clearChartBrushFilters();
  });

  const distributionScoreBubbles = useMemo(() => {
    if (!chartBrushFilters || chartBrushFilters.length === 0) return [];

    return chartBrushFilters
      .filter((f) => !!f.scoreBounds)
      .map(
        ({ scoreName, scoreBounds }) =>
          // eslint-disable-next-line react-compiler/react-compiler -- is there an alternative to using an imperative handle?
          new Bubble({
            type: "filter",
            label: `${doubleQuote(scoreName)} between ${scoreBounds!.map((v) => `${(v * 100).toFixed()}%`).join(" and ")}`,
            isReadonly: true,
            clear: () => clearChartFilter(scoreName),
          }),
      );
  }, [chartBrushFilters, clearChartFilter]);

  const hiddenScoreColumns = useMemo(() => {
    // Don't hide any score columns when we have multiple filters
    if (
      !chartBrushFilters ||
      chartBrushFilters.length === 0 ||
      chartBrushFilters.length > 1
    ) {
      return undefined;
    }

    // When we have exactly one filter, maintain the original behavior
    const chartBrushFilter = chartBrushFilters[0];
    return Object.fromEntries(
      experimentScan?.scoreFields
        ?.filter((s) => s !== chartBrushFilter.scoreName)
        .map((s) => [`scores.${s}`, true]) ?? [],
    );
  }, [experimentScan?.scoreFields, chartBrushFilters]);

  const [shareModalOpen, setShareModalOpen] = useState(false);

  const dml = useMutableObject({
    scan: experimentScanRaw,
    objectType: "experiment",
    objectId: experiment?.id, // This lets us avoid reading the full row to perform an update
    channel: loadedExperiment.channel,
    auditLogChannel: loadedExperiment.auditLog?.channel,
    setSavingState,
  });

  const updateRow = useEvent<UpdateRowFn>(async (row, path, newValue) => {
    const r = row;
    r.experiment_id = experiment?.id;
    return await dml.update([r], [{ path, newValue }]);
  });

  const batchUpdateRow = useCallback<BatchUpdateRowFn>(
    async (row, updates) => await dml.update([row], updates),
    [dml],
  );

  const expandedRowParams: ExpandedRowParams = useMemo(
    () => ({
      primaryScan: null /*experimentScanRaw*/,
      primaryScanReady: [] /*[experimentReadyRaw]*/,
      primaryDynamicObjectId: experiment?.id ?? null,
      modelSpecScan,
      auditLogScan: experimentAuditLogScan,
      auditLogScanReady: [experimentAuditLogReady],
      multiExperimentData: {
        comparisonExperimentData,
      },
      hasTrials: experimentScan?.hasTrials,
      customColumnsParams: {
        scan: experimentScan?.tableScan,
        scanQueryKeys: experimentScan?.queryKeys,
        customColumns:
          loadedExperiment.summary !== undefined
            ? customColumnState.customColumnDefinitions
            : experimentScan?.customColumns,
      },
      isFastSummaryEnabled: loadedExperiment.summary !== undefined,
      allAvailableModelCosts,
    }),
    [
      experiment?.id,
      modelSpecScan,
      experimentAuditLogScan,
      experimentAuditLogReady,
      comparisonExperimentData,
      experimentScan?.hasTrials,
      experimentScan?.tableScan,
      experimentScan?.queryKeys,
      experimentScan?.customColumns,
      customColumnState.customColumnDefinitions,
      loadedExperiment.summary,
      allAvailableModelCosts,
    ],
  );

  const isReadOnly =
    !permissions.includes("update") || !permissions.includes("delete");

  const traceViewParams = useMemo(() => {
    const params: TraceViewParams = {
      title: "eval",
      objectType: "experiment",
      objectName: experimentName,
      objectId: experiment?.id,
      roster: loadedExperiment.roster,
      expandedRowParams,
      selectableExperimentsList: comparisonExperimentData,
      editableFields: ["expected"],
      updateRow,
      batchUpdateRow,
      commentFn: dml.commentOn,
      deleteCommentFn: dml.deleteComment,
      savingState,
    };
    return params;
  }, [
    loadedExperiment,
    expandedRowParams,
    experimentName,
    experiment?.id,
    comparisonExperimentData,
    updateRow,
    batchUpdateRow,
    dml.commentOn,
    dml.deleteComment,
    savingState,
  ]);

  const runExperimentAISearch = useEvent(
    (openAIOpts: ClientOptions, query: string) =>
      runAISearch({
        openAIOpts,
        apiUrl,
        query,
        orgName,
        searchType: "experiment",
        aiSchemaColumns: projectedPaths,
        scoreFields,
      }),
  );

  const minTracePanelWidth = usePanelSize(500);
  const minSidebarPanelWidth = usePanelSize(220);
  const maxSidebarPanelWidth = usePanelSize(400);
  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: PanelLayout = {
        ...defaultPanelLayout,
        sidebar: sizes[0],
        main: sizes[1],
        trace: sizes[2],
      };
      document.cookie = `react-resizable-panels:experiment-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  useObjectIdResolver(experimentScanRaw, [experimentReadyRaw]);

  const { applySearch } = useFilterSortBarSearch({
    runAISearch: runExperimentAISearch,
    clauseChecker,
    setSearch,
  });

  const { isTraceFullscreen } = useTraceFullscreen();

  const [rowIds, setRowIds] = useState<RowId[]>([]);

  const extraLeftControls = useMemo(
    () =>
      isSidebarCollapsed ? (
        <Button
          size="xs"
          onClick={() => setSidebarCollapsed(false)}
          Icon={ArrowRightToLine}
        >
          <span className="flex gap-[-8px]">
            {comparisonExperimentData.map((e, idx) => (
              <span
                key={e.id}
                className={cn(
                  "rounded-full size-[10px]",
                  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[idx],
                  {
                    "-ml-0.5": idx > 0,
                  },
                  "border border-background",
                )}
              />
            ))}
          </span>
          Details
        </Button>
      ) : undefined,
    [comparisonExperimentData, isSidebarCollapsed, setSidebarCollapsed],
  );

  const builder = useBtqlQueryBuilder({});
  const queryClient = useQueryClient();
  const refetchTooltipContentData = useCallback(
    async (fieldName: string, rowId: string, previewLength?: number) => {
      if (!experiment?.id) return undefined;

      const queryKey = ["tooltip-content", experiment.id, rowId, previewLength];
      const rowData = await queryClient.fetchQuery({
        queryKey,
        queryFn: async () => {
          const response = await fetchBtql({
            args: {
              query: {
                filter: builder.and({
                  btql: `id = '${rowId}'`,
                }),
                from: builder.from("experiment", [experiment.id], "summary"),
                select: [{ op: "star" }],
                preview_length: previewLength ?? 1500,
              },
              brainstoreRealtime: true,
              disableLimit: false,
            },
            btqlFlags,
            apiUrl,
            getOrRefreshToken,
          });
          return Array.isArray(response.data) ? response.data[0] : {};
        },
        staleTime: 60 * 1000,
        gcTime: 5 * 60 * 1000,
      });

      return rowData?.[fieldName];
    },
    [
      apiUrl,
      builder,
      experiment?.id,
      btqlFlags,
      getOrRefreshToken,
      queryClient,
    ],
  );

  const vizQueryProps = useMemo(() => {
    return {
      extraLeftControls,
      hiddenColumns: hiddenScoreColumns,
      disabledFilterColumns: fastExperimentSummary ? ["comments"] : undefined,
      refetchTooltipContentData: fastExperimentSummary
        ? refetchTooltipContentData
        : undefined,
      className: "pt-3",
      enableScrollToColumn: (viewProps.layout ?? "list") === "list",
    };
  }, [
    extraLeftControls,
    hiddenScoreColumns,
    fastExperimentSummary,
    refetchTooltipContentData,
    viewProps.layout,
  ]);

  const tableSelectionProps = useTableSelection(
    fastExperimentSummary ? experimentTableProps.refetchDataQueryFn : undefined,
  );

  const comparisonSummariesObjects = useMemo(
    () =>
      selectedComparisonExperiments.map((e) => ({
        id: e.id,
        type: "experiment" as const,
      })),
    [selectedComparisonExperiments],
  );

  if (!experiment) {
    return <AccessFailed objectType="Experiment" objectName={experimentName} />;
  }

  return (
    <OptimizationProvider
      summaryBreakdownData={experimentTableProps.summaryBreakdownData}
      baseQuery={experimentTableProps.baseQuery}
      comparisonQueries={experimentTableProps.comparisonExperimentData}
      experimentId={experiment.id}
      setFilters={setSearch}
      clauseChecker={clauseChecker ?? undefined}
      queryProjectId={projectId ?? undefined}
    >
      <GlobalChatProvider>
        <div className="flex">
          <MainContentWrapper
            hideFooter
            className={cn(
              "flex flex-col overflow-hidden p-0 flex-1",
              HEIGHT_WITH_TOP_OFFSET,
            )}
          >
            <BackendSummariesLoader
              btObjects={comparisonSummariesObjects}
              projectedColumns={projectedColumns}
              customColumns={customColumnState.customColumnDefinitions}
            />
            <ExperimentHeader
              isLoading={false}
              orgName={orgName}
              projectName={projectName}
              experiment={experiment}
              savingState={savingState}
              setShareModalOpen={setShareModalOpen}
              selectedComparisonExperiments={selectedComparisonExperiments}
              getRowsForExport={experimentTableProps.getRowsForExport}
              getRawData={experimentTableProps.getRawData}
              isReadOnly={isReadOnly}
              errorSummaryData={errorSummaryData}
              refetchDataQueryFn={experimentTableProps.refetchDataQueryFn}
              search={search}
              columnVisibility={columnVisibility}
              projectId={projectId ?? ""}
            >
              <DiffModeSwitch
                diffModeState={diffMode}
                diffModeOptions={diffModeOptions}
                setDiffMode={setDiffMode}
              />
            </ExperimentHeader>

            <BodyWrapper
              outerClassName={cn(
                HEIGHT_WITH_DOUBLE_TOP_OFFSET,
                "overflow-hidden",
              )}
              innerClassName="flex px-3"
            >
              {isExperimentEmpty &&
              (!filters ||
                (filters.btql.length === 0 && filters.sql.length === 0)) &&
              regressionFilters.length === 0 ? (
                <div className="w-full pt-4">
                  <TableEmptyState
                    Icon={Beaker}
                    label={
                      <>
                        <p className="mb-4">
                          This experiment is currently empty. This may be
                          because your experiment has not logged data yet.{" "}
                          <Link
                            href="/docs/guides/evals"
                            className="text-accent-500"
                            target="_blank"
                          >
                            Check out the docs
                          </Link>{" "}
                          for more on how to log data to experiments.
                        </p>
                        <p className="text-sm">
                          If you think this is an error, then please reach out
                          to{" "}
                          <a
                            href="mailto:<EMAIL>"
                            className="text-accent-500"
                          >
                            <EMAIL>
                          </a>
                          .
                        </p>
                      </>
                    }
                  />
                </div>
              ) : (
                <div className="-mx-3 flex flex-auto overflow-hidden">
                  <ResizablePanelGroup
                    autoSaveId="experimentPanelLayout"
                    direction="horizontal"
                    className="flex w-full flex-auto"
                    onLayout={onPanelLayout}
                  >
                    <ResizablePanel
                      order={0}
                      id="sidebar"
                      defaultSize={Math.max(
                        defaultPanelLayout.sidebar,
                        minSidebarPanelWidth,
                      )}
                      minSize={minSidebarPanelWidth}
                      maxSize={maxSidebarPanelWidth}
                      className={cn("flex flex-none", {
                        hidden: isSidebarCollapsed || isTraceFullscreen,
                      })}
                    >
                      <ExperimentSidebar
                        viewLayout={viewProps.layout ?? "list"}
                        isExperimentPending={isExperimentPending !== false}
                        scoreFields={scoreFields}
                        experiment={experiment}
                        setSavingState={setSavingState}
                        selectedComparisonExperiments={
                          selectedComparisonExperiments
                        }
                        setComparisonExperiments={
                          setSelectedComparisonExperiments
                        }
                        isReadOnly={isReadOnly}
                        baseComparisonQuery={
                          experimentScan?.scoreFields
                            ? baseComparisonQuery
                            : null
                        }
                        onSetChartBrushFilter={setChartBrushFilter}
                        distributionChartSignals={[primaryExperimentReady]}
                        histogramRef={histogramRef}
                        primaryExperiment={experimentScan}
                        comparisonExperimentData={comparisonExperimentData}
                        errorSummaryData={errorSummaryData}
                      />
                    </ResizablePanel>
                    <ResizableHandle
                      className={cn(
                        (isTraceFullscreen || isSidebarCollapsed) &&
                          "hidden pointer-events-none",
                      )}
                    />
                    <ResizablePanel
                      className={cn("relative flex flex-1 min-w-48", {
                        // we want to use css to hide this panel rather than conditionally rendering it so that the useEffect
                        // hooks in the arrow-table are still triggered when the trace panel is fullscreen
                        // (e.g. selecting more comparison experiments)
                        hidden: isTraceFullscreen && !!activeRowId,
                      })}
                      id="main"
                      order={1}
                      defaultSize={defaultPanelLayout.main}
                    >
                      <ExperimentTable
                        {...experimentTableProps}
                        isLoading={
                          isExperimentPending || isComparisonExperimentPending
                        }
                        regressionFilters={regressionFilters}
                        setRegressionFilters={setRegressionFilters}
                        tableGroupingOptions={tableGroupingOptions}
                        customColumnState={customColumnState}
                        rowIds={rowIds}
                        setRowIds={setRowIds}
                        runExperimentAISearch={runExperimentAISearch}
                        vizQueryProps={vizQueryProps}
                        extraBubbles={distributionScoreBubbles}
                        dml={dml}
                        isReadOnly={isReadOnly}
                        pageIdentifier={pageIdentifier}
                        viewParams={viewParams}
                        viewProps={viewProps}
                        queryError={queryError?.message}
                        updateRow={updateRow}
                        tableSelectionProps={tableSelectionProps}
                        exportName={experimentName}
                        afterToolbarSlot={
                          <RealtimeStateBanner
                            realtimeState={
                              loadedExperiment.summary?.realtimeState
                            }
                            objectType="experiment"
                            objectId={loadedExperiment.id}
                          />
                        }
                        clearChartBrushFilters={clearChartBrushFilters}
                      />
                    </ResizablePanel>
                    {activeRowId && (
                      <>
                        <ResizableHandle
                          className={cn(
                            isTraceFullscreen && "hidden pointer-events-none",
                          )}
                        />
                        <ResizablePanel
                          id="trace"
                          order={2}
                          defaultSize={defaultPanelLayout.trace}
                          minSize={minTracePanelWidth}
                          className="flex flex-none"
                        >
                          <div className="flex flex-auto flex-col overflow-hidden p-0">
                            <TracePanel
                              rowIds={rowIds}
                              traceViewParams={traceViewParams}
                              isReadOnly={isReadOnly}
                              onApplySearch={applySearch}
                            />
                          </div>
                        </ResizablePanel>
                      </>
                    )}
                  </ResizablePanelGroup>
                </div>
              )}
            </BodyWrapper>
            <ExperimentShareDialog
              open={shareModalOpen}
              setOpen={setShareModalOpen}
              experiment={experiment}
              setSavingState={setSavingState}
              experimentReadyRaw={experimentReadyRaw}
              experimentScanRaw={experimentScanRaw}
            />
          </MainContentWrapper>

          <DockedChatSpacer />
        </div>
      </GlobalChatProvider>
    </OptimizationProvider>
  );
}
