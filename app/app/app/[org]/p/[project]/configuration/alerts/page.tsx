"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useContext, useState } from "react";
import { BellIcon, PencilLine, Plus, Trash2 } from "lucide-react";
import { Input, inputClassName } from "#/ui/input";
import {
  type UpsertResponse,
  type UpsertProjectAutomation,
  performDelete,
  performUpsert,
} from "../configuration-client-actions";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import ReactTextareaAutosize from "react-textarea-autosize";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { cn } from "#/utils/classnames";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import {
  performAutomationTest,
  type AutomationTestResponse,
} from "#/utils/btapi/scoring";
import {
  logAutomationConfigSchema,
  type LogAutomation,
} from "@braintrust/typespecs";
import { parseQuery } from "@braintrust/btql/parser";
import { formatDuration, intervalToDuration } from "date-fns";
import { DataTextEditor } from "#/ui/data-text-editor";
import { useAutomationIdState } from "#/ui/query-parameters";
import { ErrorBanner } from "#/ui/error-banner";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { useBtqlAutocompleteProjectDataSource } from "#/app/app/[org]/btql/use-btql-autocomplete-data-sources";

const Alerts = () => {
  // "new" means we're creating a new automation
  const [selectedAutomationId, setSelectedAutomationId] =
    useAutomationIdState();

  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
  } = useContext(ProjectContext);
  const automations = config.automations.reduce(
    (acc: LogAutomation[], automation) => {
      const { config, ...rest } = automation;
      if (config.event_type === "logs") {
        return [
          ...acc,
          {
            ...rest,
            config,
          },
        ];
      } else {
        return acc;
      }
    },
    [],
  );

  const [confirmDiscard, setConfirmDiscard] = useState(false);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);

  const selectedAutomation = automations.find(
    (a) => a.id === selectedAutomationId,
  );

  const reset = () => {
    setSelectedAutomationId(null);
    setConfirmDiscard(false);
  };

  if (!projectId) {
    throw new Error("Cannot instantiate Alerts outside project");
  }

  return (
    <ConfigurationSectionLayout
      title="Alerts"
      description="Configure alerts based on log events"
      action={
        automations.length > 0 ? (
          <Button
            size="sm"
            Icon={Plus}
            onClick={(_e) => {
              setSelectedAutomationId("new");
            }}
          >
            Alert
          </Button>
        ) : undefined
      }
    >
      {automations.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow>
              <TableHead className="w-36 truncate">Alert name</TableHead>
              <TableHead className="w-24 truncate">Event type</TableHead>
              <TableHead className="w-40 truncate">BTQL filter</TableHead>
              <TableHead className="w-56 truncate">Webhook URL</TableHead>
              <TableHead className="flex-1 truncate">Description</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {automations.map((automation, index) => (
              <TableRow key={index} className="py-2">
                <TableCell className="w-36 truncate">
                  {automation.name}
                </TableCell>
                <TableCell className="w-24 truncate">Log event</TableCell>
                <TableCell className="w-40 truncate">
                  {automation.config.btql_filter}
                </TableCell>
                <TableCell className="w-56 truncate">
                  {automation.config.action.url}
                </TableCell>
                <TableCell className="flex-1 truncate">
                  {automation.description ?? ""}
                </TableCell>
                <TableCell className="w-28 justify-end gap-1 pr-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={() => {
                      setSelectedAutomationId(automation.id);
                    }}
                    Icon={PencilLine}
                    iconClassName="size-4"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setConfirmDeleteId(automation.id);
                    }}
                    Icon={Trash2}
                    iconClassName="size-4"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <BellIcon className="text-primary-500" />
              <p className="text-base text-primary-500">
                No alerts defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setSelectedAutomationId("new");
                }}
                Icon={Plus}
              >
                Create alert
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}

      <Dialog
        open={selectedAutomationId === "new" || !!selectedAutomation}
        onOpenChange={(o) => {
          if (!o) {
            reset();
          }
        }}
      >
        <DialogContent className="sm:max-w-lg md:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Configure alert</DialogTitle>
            <DialogDescription>
              Configure an alert based on events
            </DialogDescription>
          </DialogHeader>
          <AlertForm
            apiUrl={apiUrl}
            row={selectedAutomation ?? null}
            onClose={() => {
              reset();
            }}
            test={async (row) => {
              if (!apiUrl) {
                return { kind: "error", message: "Not logged in (yet)" };
              }
              const sessionToken = await getOrRefreshToken();
              return await performAutomationTest({
                apiUrl,
                sessionToken,
                projectId,
                row,
                errorContext: "alert",
              });
            }}
            save={async (row) => {
              if (!apiUrl) {
                return { kind: "error", message: "Not logged in (yet)" };
              }
              const sessionToken = await getOrRefreshToken();
              return await performUpsert({
                apiUrl,
                sessionToken,
                objectType: "project_automation",
                row,
                projectId,
                mutate,
              });
            }}
          />
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        open={confirmDiscard}
        onOpenChange={setConfirmDiscard}
        title="Discard changes"
        description="Are you sure you want to discard changes?"
        confirmText="Discard"
        onConfirm={() => {
          reset();
        }}
      />

      <ConfirmationDialog
        open={!!confirmDeleteId}
        onOpenChange={(o) => {
          if (!o) {
            setConfirmDeleteId(null);
          }
        }}
        title="Delete alert"
        description="Are you sure you want to delete this alert? This action cannot be undone."
        confirmText="Delete"
        onConfirm={async () => {
          if (!apiUrl) {
            toast.error("Not logged in (yet)");
            return;
          }
          const sessionToken = await getOrRefreshToken();

          if (confirmDeleteId) {
            toast.promise(
              performDelete({
                apiUrl,
                sessionToken,
                objectType: "project_automation",
                rowId: confirmDeleteId,
                mutate,
              }),
              {
                loading: "Deleting alert...",
                success: "Alert deleted",
                error: "Failed to delete alert",
              },
            );
          }
        }}
      />
    </ConfigurationSectionLayout>
  );
};

const logAutomationFormSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  config: logAutomationConfigSchema,
});

type LogAutomationForm = z.infer<typeof logAutomationFormSchema>;

const getReset = (a?: LogAutomation): LogAutomationForm => {
  return {
    name: a?.name ?? "",
    description: a?.description ?? undefined,
    config: a?.config || {
      event_type: "logs",
      btql_filter: "",
      interval_seconds: 60 * 60, // 1 hour
      action: {
        type: "webhook",
        url: "",
      },
    },
  };
};

function AlertForm({
  row,
  test,
  save,
  onClose,
}: {
  apiUrl: string;
  row: LogAutomation | null;
  test: (row: UpsertProjectAutomation) => Promise<AutomationTestResponse>;
  save: (row: UpsertProjectAutomation) => Promise<UpsertResponse>;
  onClose: VoidFunction;
}) {
  const action = row ? "Update" : "Create";
  const dataSources = useBtqlAutocompleteProjectDataSource();

  const form = useForm<z.infer<typeof logAutomationFormSchema>>({
    resolver: async (values, context, options) => {
      const result = await zodResolver(logAutomationFormSchema)(
        values,
        context,
        options,
      );

      if (!values.name) {
        result.errors = {
          ...result.errors,
          name: { message: "Alert name is required" },
        };
      }

      if (values.config.btql_filter) {
        try {
          // TODO: should we swap with `useClauseChecker("project_logs", true)`?
          const query = parseQuery(
            `select: * | from: project_logs('fake_project') | filter: ${values.config.btql_filter}`,
          );
          if (!query.filter) {
            result.errors = {
              ...result.errors,
              "config.btql_filter": { message: "Invalid BTQL filter" },
            };
          }
        } catch {
          result.errors = {
            ...result.errors,
            "config.btql_filter": { message: "Invalid BTQL filter" },
          };
        }
      }

      if (!values.config.action.url) {
        result.errors = {
          ...result.errors,
          "config.action.url": { message: "Webhook URL is required" },
        };
      } else {
        try {
          new URL(values.config.action.url);
        } catch (e) {
          result.errors = {
            ...result.errors,
            "config.action.url": { message: "Invalid webhook URL" },
          };
        }
      }

      return result;
    },
    defaultValues: getReset(row ?? undefined),
  });

  const watchedInterval = form.watch("config.interval_seconds");
  const selectedInterval = intervalOptions.find(
    (o) => o.value === watchedInterval,
  );

  const [isRunningTest, setIsRunningTest] = useState(false);
  const [testResult, setTestResult] = useState<AutomationTestResponse | null>(
    null,
  );

  const handleSubmit = form.handleSubmit(async (data) => {
    const result = await save({
      id: row?.id,
      name: data.name,
      description: data.description,
      config: data.config,
    });

    if (result.kind === "duplicate") {
      form.setError(
        "name",
        {
          type: "value",
          message:
            "Name already exists. Please choose a unique automation name.",
        },
        {
          shouldFocus: true,
        },
      );
      return;
    } else if (result.kind === "error") {
      toast.error(`Failed to create or update alert`, {
        description: result.message,
      });
      return;
    }

    onClose();
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Alert name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter alert name"
                  {...field}
                  value={field.value ?? ""}
                  autoComplete="none"
                  data-1p-ignore="true"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <div>
              <CollapsibleSection
                title="Description (optional)"
                defaultCollapsed={!row?.description}
              >
                <FormItem>
                  <FormControl>
                    <ReactTextareaAutosize
                      {...field}
                      className={inputClassName}
                      rows={2}
                      placeholder="Enter description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </CollapsibleSection>
            </div>
          )}
        />
        <FormField
          control={form.control}
          name="config.event_type"
          render={() => (
            <FormItem>
              <FormLabel>Event type</FormLabel>
              <FormControl>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button isDropdown className="w-full">
                      <span className="flex-1 truncate text-left">
                        Log event
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-[462px]">
                    <DropdownMenuCheckboxItem key={"logs"} checked>
                      Log event
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="config.btql_filter"
          render={({ field }) => (
            <FormItem>
              <FormLabel>BTQL filter</FormLabel>
              <FormControl>
                <BtqlEditor
                  mode="expr"
                  value={field.value}
                  onValueChange={(v) =>
                    field.onChange(v, {
                      shouldDirty: true,
                      shouldTouch: true,
                    })
                  }
                  onMetaEnter={() => {
                    form.setValue("config.btql_filter", field.value, {
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                  dataSources={dataSources}
                  className={cn(
                    inputClassName,
                    "font-mono placeholder:font-inter h-auto",
                  )}
                  placeholder="Enter BTQL filter"
                />
              </FormControl>
              <FormDescription>
                Filter logs using BTQL&apos;s{" "}
                <a
                  href="https://www.braintrust.dev/docs/reference/btql#filter-clause"
                  target="_blank"
                  rel="noreferrer"
                  className="text-accent-600"
                >
                  filter clause
                </a>
                . If no filter is provided, the alert will apply to all logs.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="config.interval_seconds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Interval</FormLabel>
              <FormControl>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button isDropdown className="w-full">
                      <span className="flex-1 truncate text-left">
                        {selectedInterval?.label ?? "Select interval"}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-[462px]">
                    {intervalOptions.map((o) => (
                      <DropdownMenuCheckboxItem
                        key={o.value}
                        checked={field.value === o.value}
                        onSelect={() => field.onChange(o.value)}
                      >
                        {o.label}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </FormControl>
              <FormDescription>
                Trigger the webhook once in this time interval
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="config.action.url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Webhook URL</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter webhook URL"
                  {...field}
                  value={field.value ?? ""}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === "") {
                      field.onChange(null, {
                        shouldDirty: true,
                        shouldValidate: true,
                      });
                    } else {
                      field.onChange(value, {
                        shouldDirty: true,
                        shouldValidate: true,
                      });
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {testResult?.kind === "error" && (
          <ErrorBanner skipErrorReporting>
            <div className="mb-1 font-medium">Test failed</div>
            {testResult.message}
          </ErrorBanner>
        )}
        {testResult?.kind === "success" && (
          <div className="mt-4 text-xs font-medium">
            <div
              className={cn(
                "px-2 py-1 rounded mb-1 inline-block bg-good-100 text-good-700",
              )}
            >
              Test successful
            </div>
            <DataTextEditor
              className="mt-2"
              value={testResult.payload}
              allowedRenderOptions={["json"]}
              readOnly
            />
          </div>
        )}

        <DialogFooter className="mt-4">
          <Button
            variant="ghost"
            disabled={!form.formState.isValid}
            isLoading={isRunningTest}
            onClick={async (e) => {
              e.preventDefault();
              if (!form.formState.isValid) {
                return;
              }
              const data = form.getValues();

              setTestResult(null);
              setIsRunningTest(true);
              const result = await test({
                id: row?.id,
                name: data.name,
                description: data.description,
                config: data.config,
              });

              setTestResult(result);
              setIsRunningTest(false);
            }}
          >
            Test alert
          </Button>
          <Button
            variant="primary"
            type="submit"
            disabled={!form.formState.isDirty}
            isLoading={form.formState.isLoading || form.formState.isSubmitting}
          >
            {action} alert
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}

const formatInterval = (interval: number) => {
  return formatDuration(
    intervalToDuration({ start: 0, end: interval * 1000 }),
    { format: ["days", "hours", "minutes"] },
  );
};

const intervalOptions = [
  60 * 5,
  60 * 30,
  60 * 60,
  60 * 60 * 4,
  60 * 60 * 12,
  60 * 60 * 24,
].map((o) => ({
  label: formatInterval(o),
  value: o,
}));

export default Alerts;
