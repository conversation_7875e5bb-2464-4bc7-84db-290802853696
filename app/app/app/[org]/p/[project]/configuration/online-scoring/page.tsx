"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { Button } from "#/ui/button";
import { useContext, useMemo, useState } from "react";
import { PencilLine, Plus, Radio, Trash2 } from "lucide-react";
import Link from "next/link";

import { performDelete } from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";

import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

import { type ProjectScore } from "@braintrust/typespecs";
import { isOnlineScore } from "@braintrust/local/query";
import { ConfigureOnlineScoringRuleDialog } from "../../logs/configure-online-scoring-rule-dialog";
import { pluralizeWithCount } from "#/utils/plurals";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { TableSkeleton } from "#/ui/table/table-skeleton";

const OnlineScoreConfig = () => {
  // null means we're creating a new rule
  const [selectedRule, setSelectedRule] = useState<
    ProjectScore | null | undefined
  >(undefined);

  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
    isConfigLoading,
  } = useContext(ProjectContext);
  const { scores } = config;

  if (!projectId) {
    throw new Error("Cannot instantiate OnlineScoreConfig outside project");
  }

  const rules = useMemo(
    () =>
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      scores.filter((row) => isOnlineScore(row.score_type)) as ProjectScore[],
    [scores],
  );

  return (
    <ConfigurationSectionLayout
      title="Online scoring"
      description={
        <>
          Configure rules for continuous evaluation from real-time logs.
          <span className="text-primary-500">
            <Link
              href="https://www.braintrust.dev/docs/guides/logs/score#what-is-online-scoring"
              target="_blank"
              rel="noopener noreferrer"
              className="ml-1 text-primary-600 underline hover:text-primary-800"
            >
              Learn more
            </Link>{" "}
            about online scoring.
          </span>
        </>
      }
      action={
        rules.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setSelectedRule(null);
            }}
            Icon={Plus}
          >
            Rule
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : rules.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow className="hover:bg-background">
              <TableHead className="w-48">Rule name</TableHead>
              <TableHead className="w-36">Sampling rate</TableHead>
              <TableHead className="w-36">Scorers</TableHead>
              <TableHead className="flex-1">Description</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rules.map((rule) => (
              <TableRow key={rule.id} className="py-2">
                <TableCell className="w-48">
                  <span className="min-w-0 flex-1 truncate">{rule.name}</span>
                </TableCell>
                <TableCell className="w-36">
                  <span className="min-w-0 flex-1 truncate">
                    {((rate) => {
                      const percentage = (rate * 100).toFixed(2);
                      return percentage.endsWith(".00")
                        ? Math.round(rate * 100) + "%"
                        : percentage + "%";
                    })(rule.config?.online?.sampling_rate ?? 0)}
                  </span>
                </TableCell>
                <TableCell className="w-36">
                  <span className="min-w-0 flex-1 truncate">
                    {pluralizeWithCount(
                      rule.config?.online?.scorers.length ?? 0,
                      "scorer",
                      "scorers",
                    )}
                  </span>
                </TableCell>
                <TableCell className="flex-1">
                  <span className="min-w-0 flex-1 truncate">
                    {rule.description ?? ""}
                  </span>
                </TableCell>
                <TableCell className="w-28 justify-end gap-2 pr-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setSelectedRule(rule);
                    }}
                  >
                    <PencilLine className="size-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={async (_e) => {
                      if (!apiUrl) {
                        toast.error("Not logged in (yet)");
                        return;
                      }
                      return toast.promise(
                        performDelete({
                          apiUrl,
                          sessionToken: await getOrRefreshToken(),
                          objectType: "project_score",
                          rowId: rule.id,
                          mutate,
                        }),
                        {
                          loading: "Deleting online scoring rule...",
                          success: `Successfully deleted "${rule.name}"`,
                          error: "Failed to delete online scoring rule",
                        },
                      );
                    }}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <Radio className="text-primary-500" />
              <p className="text-base text-primary-500">
                No online scoring rules defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setSelectedRule(null);
                }}
                Icon={Plus}
              >
                Create rule
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <ConfigureOnlineScoringRuleDialog
        open={selectedRule !== undefined}
        onOpenChange={(o) => {
          if (!o) {
            setSelectedRule(undefined);
          }
        }}
        selectedRule={selectedRule ?? null}
        showDelete={true}
      />
    </ConfigurationSectionLayout>
  );
};

export default OnlineScoreConfig;
