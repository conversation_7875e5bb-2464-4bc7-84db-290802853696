"use client";

import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { Button } from "#/ui/button";
import { useCallback, useState } from "react";
import { GripVerticalIcon, PencilLine, Trash2 } from "lucide-react";
import { toast } from "sonner";
import {
  type DragEndEvent,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
  arrayMove,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import StyledDndContext from "#/ui/styled-dnd-context";

// Generic interfaces for draggable items
export interface DraggableItem {
  id: string;
  name: string;
  description?: string | null;
}

export interface ColumnDefinition<T extends DraggableItem> {
  key: string;
  header: string;
  width?: string;
  className?: string;
  render: (item: T) => React.ReactNode;
}

export interface DraggableTableProps<T extends DraggableItem> {
  items: T[];
  columns: ColumnDefinition<T>[];
  onReorder: (
    items: T[],
    draggedItem: T,
    originalIndex: number,
    newIndex: number,
  ) => Promise<void>;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  isLoading?: boolean;
  className?: string;
  showDragHandle?: boolean;
  getDragPreview?: (item: T) => string;
}

interface DraggableRowProps<T extends DraggableItem> {
  id: string;
  item: T;
  columns: ColumnDefinition<T>[];
  isLoading?: boolean;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  showDragHandle?: boolean;
}

function DraggableTableRow<T extends DraggableItem>({
  id,
  item,
  columns,
  isLoading,
  onEdit,
  onDelete,
  showDragHandle,
}: DraggableRowProps<T>) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id, data: { item } });

  const style = {
    transform: CSS.Transform.toString({
      x: 0,
      y: transform?.y ?? 0,
      scaleX: 1,
      scaleY: 1,
    }),
    transition,
    cursor: "default",
  };

  return (
    <TableRow className="py-2" ref={setNodeRef} {...attributes} style={style}>
      {columns.map((column) => (
        <TableCell
          key={column.key}
          className={column.className}
          style={column.width ? { width: column.width } : undefined}
        >
          {column.render(item)}
        </TableCell>
      ))}
      <TableCell className="flex w-28 justify-end gap-2 pr-0">
        {onEdit && (
          <Button
            variant="ghost"
            size="icon"
            className="size-8"
            onClick={() => onEdit(item)}
          >
            <PencilLine className="size-4" />
          </Button>
        )}
        {onDelete && (
          <Button
            variant="ghost"
            size="icon"
            className="size-8"
            onClick={() => onDelete(item)}
          >
            <Trash2 className="size-4" />
          </Button>
        )}
        {showDragHandle && (
          <Button
            variant="ghost"
            size="icon"
            isLoading={isLoading}
            className="size-8 cursor-grab text-primary-400"
            {...listeners}
          >
            <GripVerticalIcon className="size-4" />
          </Button>
        )}
      </TableCell>
    </TableRow>
  );
}

export function DraggableTable<T extends DraggableItem>({
  items,
  columns,
  onReorder,
  onEdit,
  onDelete,
  isLoading = false,
  className = "table-auto overflow-hidden text-left",
  showDragHandle = true,
  getDragPreview,
}: DraggableTableProps<T>) {
  const [tempOrder, setTempOrder] = useState<T[] | null>(null);
  const [draggingId, setDraggingId] = useState<string | number | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = useCallback(
    async (event: DragEndEvent) => {
      setDraggingId(null);
      const { active, over } = event;

      if (over && active.id !== over.id) {
        const prevIndex = items.findIndex((item) => item.id === active.id);
        const nextIndex = items.findIndex((item) => item.id === over.id);
        const newOrder = arrayMove(items, prevIndex, nextIndex);
        const draggedItem = items[prevIndex];

        setTempOrder(newOrder);

        try {
          await onReorder(newOrder, draggedItem, prevIndex, nextIndex);
        } catch (error) {
          console.error("Failed to reorder items", error);
          toast.error("Failed to update item order. Please try again.");
        } finally {
          setTempOrder(null);
        }
      }
    },
    [items, onReorder],
  );

  const displayItems = tempOrder === null ? items : tempOrder;
  const shouldShowDragHandle = showDragHandle && items.length > 1;

  return (
    <StyledDndContext
      sensors={sensors}
      onDragEnd={handleDragEnd}
      onDragStart={(e) => {
        const item = e.active.data.current?.item;
        const preview = getDragPreview
          ? getDragPreview(item)
          : (item?.id ?? null);
        setDraggingId(preview);
      }}
    >
      <SortableContext
        items={displayItems.map((item) => item.id)}
        strategy={verticalListSortingStrategy}
      >
        <Table className={className}>
          <TableHeader>
            <TableRow className="hover:bg-background">
              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={column.className}
                  style={column.width ? { width: column.width } : undefined}
                >
                  {column.header}
                </TableHead>
              ))}
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayItems.map((item) => (
              <DraggableTableRow
                key={item.id}
                id={item.id}
                item={item}
                columns={columns}
                isLoading={tempOrder !== null}
                onEdit={onEdit}
                onDelete={onDelete}
                showDragHandle={shouldShowDragHandle}
              />
            ))}
          </TableBody>
        </Table>
        <DragOverlay>
          {draggingId ? (
            <div className="cursor-grabbing truncate rounded-md border-2 border-accent-500 bg-background p-2 text-sm shadow-md">
              {draggingId}
            </div>
          ) : null}
        </DragOverlay>
      </SortableContext>
    </StyledDndContext>
  );
}
