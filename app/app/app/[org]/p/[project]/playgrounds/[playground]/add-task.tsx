import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { cn } from "#/utils/classnames";
import { MessageCircle, Percent, Plus, Route, Unplug } from "lucide-react";
import { PromptsDropdown } from "../prompts-dropdown";
import { RemoteEvalsDropdown } from "../remote-evals-dropdown";
import { AgentsDropdown } from "../agents-dropdown";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useMemo, useState, type PropsWithChildren } from "react";
import { usePlaygroundPromptSheetIndexState } from "#/ui/query-parameters";
import {
  type AppendEditorArgs,
  useSyncedPrompts,
} from "../../prompts/synced/use-synced-prompts";
import { ScorersDropdown } from "../scorers-dropdown";
import { atom, useAtomValue } from "jotai";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { excludeKeys } from "../../prompts/utils";
import {
  type PromptData,
  type SyncedPlaygroundBlock,
} from "#/ui/prompts/schema";
import isEqual from "lodash.isequal";

function isPromptEqual(
  prompt: SyncedPlaygroundBlock,
  defaultPromptData: PromptData,
) {
  return (
    prompt.function_data.type === "prompt" &&
    isEqual(
      {
        ...prompt.prompt_data,
        options: excludeKeys(prompt?.prompt_data?.options, ["position"]),
      },
      {
        ...defaultPromptData,
        options: excludeKeys(defaultPromptData.options, ["position"]),
      },
    )
  );
}

export const PlaygroundAddTask = ({
  isReadOnly,
  numTasks,
  projectName,
  children,
  disabledOriginIds = [],
  deletePrompt,
}: PropsWithChildren<{
  isReadOnly?: boolean;
  numTasks?: number;
  projectName: string;
  disabledOriginIds?: string[];
  deletePrompt: (id: string) => void;
}>) => {
  const { setPromptSheetIndex } = usePlaygroundPromptSheetIndexState();
  const { appendNewEditor_ROOT, sortedSyncedPromptsAtom_ROOT } =
    useSyncedPrompts();
  const defaultPromptData = useDefaultPromptData();
  const [deletionId, shouldReplace] = useAtomValue(
    useMemo(
      () =>
        atom((get) => {
          const sortedSyncedPrompts = get(sortedSyncedPromptsAtom_ROOT);
          const shouldReplace =
            sortedSyncedPrompts.length === 1 &&
            isPromptEqual(sortedSyncedPrompts[0], defaultPromptData);

          return shouldReplace
            ? [sortedSyncedPrompts[0].id, true]
            : [null, false];
        }),
      [defaultPromptData, sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const onAdd = (item: AppendEditorArgs) => {
    if (shouldReplace && deletionId) {
      deletePrompt?.(deletionId);
    }
    appendNewEditor_ROOT(item);
    setPromptSheetIndex(numTasks ?? 0);
  };

  return (
    <AddTask
      projectName={projectName}
      onAdd={onAdd}
      disabledOriginIds={disabledOriginIds}
    >
      {children ?? (
        <Button
          size="xs"
          Icon={Plus}
          className={cn("flex-none", { hidden: isReadOnly })}
        >
          Task
          {!!numTasks && numTasks > 0 && (
            <span className="text-[10px] font-normal text-primary-500">
              {numTasks}
            </span>
          )}
        </Button>
      )}
    </AddTask>
  );
};

export const AddTask = ({
  projectName,
  children,
  hideRemoteEvals,
  disallowBlankTasks,
  disabledOriginIds,
  align = "end",
  onAdd,
}: PropsWithChildren<{
  projectName: string;
  hideRemoteEvals?: boolean;
  align?: "start" | "end";
  disallowBlankTasks?: boolean;
  disabledOriginIds?: string[];
  onAdd: (task: AppendEditorArgs) => void;
}>) => {
  const [open, setOpen] = useState(false);
  const [openedChild, setOpenedChild] = useState<
    "prompt" | "agent" | "remoteEval" | "scorer" | undefined
  >();
  const {
    flags: { agents, remoteEvals, scorerTasks },
  } = useFeatureFlags();

  return (
    <DropdownMenu
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen) {
          setOpenedChild(undefined);
        }
        setOpen(newOpen);
      }}
    >
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        <DropdownMenuSub
          onOpenChange={(open) => {
            if (open) {
              setOpenedChild("prompt");
            } else {
              setOpenedChild(undefined);
            }
          }}
        >
          <DropdownMenuSubTrigger>
            <MessageCircle className="size-3" />
            Prompt
          </DropdownMenuSubTrigger>
          <PromptsDropdown
            onAddPrompt={onAdd}
            isInSubMenu
            open={openedChild === "prompt"}
            hideBlankOption={disallowBlankTasks}
            projectName={projectName}
            disabledOriginIds={disabledOriginIds}
            dropdownMenuContentProps={{
              align: "start",
            }}
          />
        </DropdownMenuSub>
        {agents && (
          <DropdownMenuSub
            onOpenChange={(open) => {
              if (open) {
                setOpenedChild("agent");
              } else {
                setOpenedChild(undefined);
              }
            }}
          >
            <DropdownMenuSubTrigger>
              <Route className="size-3" />
              Agent
            </DropdownMenuSubTrigger>
            <AgentsDropdown
              isInSubMenu
              open={openedChild === "agent"}
              projectName={projectName}
              selectedAgentIds={[]}
              disabledOriginIds={disabledOriginIds}
              dropdownMenuContentProps={{
                align: "start",
              }}
              hideBlankOption={disallowBlankTasks}
              onAddAgent={onAdd}
            />
          </DropdownMenuSub>
        )}
        {remoteEvals && !hideRemoteEvals && (
          <DropdownMenuSub
            onOpenChange={(open) => {
              if (open) {
                setOpenedChild("remoteEval");
              } else {
                setOpenedChild(undefined);
              }
            }}
          >
            <DropdownMenuSubTrigger>
              <Unplug className="size-3" />
              Remote eval
            </DropdownMenuSubTrigger>
            <RemoteEvalsDropdown
              isInSubMenu
              open={openedChild === "remoteEval"}
              onAddEval={onAdd}
            />
          </DropdownMenuSub>
        )}
        {scorerTasks && (
          <DropdownMenuSub
            onOpenChange={(open) => {
              if (open) {
                setOpenedChild("scorer");
              } else {
                setOpenedChild(undefined);
              }
            }}
          >
            <DropdownMenuSubTrigger>
              <Percent className="size-3" />
              Scorers
            </DropdownMenuSubTrigger>
            <ScorersDropdown
              onAddScorer={onAdd}
              isInSubMenu
              open={openedChild === "scorer"}
              hideBlankOption={disallowBlankTasks}
              projectName={projectName}
              disabledOriginIds={disabledOriginIds}
              dropdownMenuContentProps={{
                align: "start",
              }}
            />
          </DropdownMenuSub>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
