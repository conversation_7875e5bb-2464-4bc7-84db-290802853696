"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { type Annotations, type AnnotationData } from "#/ui/charts/annotations";
import { Chart, type ChartProps } from "#/ui/charts/Chart";
import { CHART_COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/color";
import {
  type NearestBarplotItems,
  useGroupedBarplot,
} from "#/ui/charts/grouped-barplot";
import {
  type HighlightState,
  type HighlightedGroup,
  type HighlightedGroupTypeEnum,
  isLineHighlighted,
  useHighlightState,
} from "#/ui/charts/highlight";
import {
  type NearestMultilineItems,
  useMultiLineChart,
} from "#/ui/charts/multi-line-chart";
import {
  type NearestScatterplotItems,
  useScatterPlot,
} from "#/ui/charts/scatterplot";
import {
  BASE_X_AXIS_OPTIONS,
  GROUP_BY_NONE,
  GROUP_BY_SCORE,
  type SelectionType,
  type SelectionTypesEnum,
  X_AXIS_COMPARISON,
  X_AXIS_EXPERIMENT,
  X_AXIS_TIME,
  compareSelectionTypes,
  formatSelectionTypeName,
  formatValueForSelectionType,
  isCategoricalXMetric,
  isEqualSelectionType,
  isGroupByYMetric,
  isTimeBasedXMetric,
  selectionTypeKey,
  selectionTypeSchema,
} from "#/ui/charts/selectionTypes";
import { ChartSymbol } from "#/ui/charts/symbols";
import { Combobox } from "#/ui/combobox/combobox";
import { smartTimeFormat } from "#/ui/date";
import { AutosizeInput } from "#/ui/input";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import {
  CurlyBraces,
  type LucideIcon,
  StretchHorizontal,
  XIcon,
} from "lucide-react";
import React, {
  type ChangeEventHandler,
  type PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  type PointMetadata,
  groupExperimentData,
  groupForBarChart,
  groupForLineChart,
} from "./grouping";
import { type ExperimentData } from "./useLoadExperimentChartData";
import { getObjectPathValue } from "./utils";
import {
  type ChartAggregationType,
  MIN_CHART_HEIGHT,
} from "#/lib/clientDataStorage";
import { produce } from "immer";

export const MAX_EXPERIMENTS = 500;

const MAX_CHART_HEIGHT = 600;

export const LOG_TIME_BUCKETS = ["hour", "day", "week", "month"] as const;
export type LogTimeBucket = (typeof LOG_TIME_BUCKETS)[number];

type NearestItems =
  | NearestMultilineItems
  | NearestBarplotItems
  | NearestScatterplotItems<PointMetadata>
  | null;

type ExperimentsChartProps = PropsWithChildren<{
  className?: string;
  experimentData: ExperimentData[] | null;
  loading?: boolean;
  scoreNames: string[];
  metricNames: string[];
  metadataFields: string[][] | undefined;
  customColumnOptions: string[][] | undefined;
  numericMetadataFields: string[][] | undefined;
  selectedExperiments: Record<string, boolean>;
  onBrush: (v: { min: number; max: number } | undefined) => void;
  onExperimentsClick: (experiments: PointMetadata[]) => void;
  controlsProps: {
    grouping: SelectionType;
    setGrouping: (v: React.SetStateAction<SelectionType>) => void;
    selectedMeasures: Record<string, Record<SelectionTypesEnum, number>>;
    setSelectedMeasures: (
      v: React.SetStateAction<
        Record<string, Record<SelectionTypesEnum, number>>
      >,
    ) => void;
    yMetric: SelectionType | null;
    setYMetric: (v: React.SetStateAction<SelectionType | null>) => void;
    xMetric: SelectionType;
    setXMetric: (v: React.SetStateAction<SelectionType | null>) => void;
    symbolGrouping: SelectionType;
    setSymbolGrouping: (v: React.SetStateAction<SelectionType | null>) => void;
    xAxisAggregation: ChartAggregationType;
    setXAxisAggregation: (
      v: React.SetStateAction<ChartAggregationType | null>,
    ) => void;
    chartAnnotations: Annotations;
    setChartAnnotations: (v: React.SetStateAction<Annotations>) => void;
  };
  chartHeight: number;
  setChartHeight: (v: number) => void;
}>;

export const ExperimentsChart = ({
  className,
  experimentData,
  loading,
  scoreNames,
  metricNames,
  metadataFields,
  customColumnOptions,
  numericMetadataFields,
  selectedExperiments,
  onBrush,
  onExperimentsClick,
  chartHeight,
  setChartHeight,
  controlsProps,
  children,
}: ExperimentsChartProps) => {
  const {
    state: highlightState,
    highlight,
    clearHighlight,
  } = useHighlightState();
  const {
    grouping: colorGrouping,
    setGrouping,
    selectedMeasures,
    setSelectedMeasures,
    yMetric: _yMetric,
    setYMetric,
    xMetric,
    setXMetric,
    symbolGrouping,
    setSymbolGrouping,
    xAxisAggregation: aggregationType,
    setXAxisAggregation: setAggregationType,
    chartAnnotations: annotationData,
    setChartAnnotations: setAnnotationData,
  } = controlsProps;

  const yMetric: SelectionType | null = useMemo(() => {
    if (scoreNames.length === 0) {
      return null;
    }
    return _yMetric ?? { type: "score", value: scoreNames[0] };
  }, [scoreNames, _yMetric]);

  const toggleSelectedMeasures = useCallback(
    (selections: SelectionType[]) => {
      setSelectedMeasures(
        produce((draft) => {
          selections.forEach((s) => {
            draft[s.value] = draft[s.value] ?? {};
            if (draft[s.value][s.type] == null) {
              // number doesn't technically matter here, since its value computed on read time
              draft[s.value][s.type] = 1;
            } else {
              delete draft[s.value][s.type];
            }
          });
        }),
      );
    },
    [setSelectedMeasures],
  );

  const visibleMeasures = useMemo(() => {
    return Object.entries(selectedMeasures).flatMap(
      ([measureName, measureInfo]) =>
        Object.entries(measureInfo).map(
          ([type, colorIndex]) =>
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            ({
              type,
              value: measureName,
              colorIndex,
            }) as SelectionType & {
              colorIndex: number;
            },
        ),
    );
  }, [selectedMeasures]);

  const setColorGrouping = useCallback(
    (v: SelectionType) => {
      setGrouping(v);
      if (isEqualSelectionType(v, symbolGrouping)) {
        setSymbolGrouping(GROUP_BY_NONE);
      }
    },
    [setGrouping, symbolGrouping, setSymbolGrouping],
  );

  const groupByYMetric = isGroupByYMetric(colorGrouping, xMetric);

  const groupedData = useMemo(() => {
    if (loading || !experimentData || !yMetric) {
      return null;
    }

    const groupedData = groupExperimentData(
      experimentData,
      colorGrouping,
      symbolGrouping,
      xMetric,
      yMetric,
      visibleMeasures,
      selectedExperiments,
    );
    return groupedData;
  }, [
    loading,
    experimentData,
    visibleMeasures,
    selectedExperiments,
    xMetric,
    yMetric,
    colorGrouping,
    symbolGrouping,
  ]);

  const chartType = useMemo(() => {
    if (isTimeBasedXMetric(xMetric)) {
      return "line" as const;
    }
    if (
      isEqualSelectionType(xMetric, X_AXIS_COMPARISON) ||
      (isCategoricalXMetric(xMetric) && aggregationType !== "all")
    ) {
      return "bar" as const;
    }

    return "scatter" as const;
  }, [xMetric, aggregationType]);

  const { scatterplotData } = useMemo(() => {
    if (chartType !== "scatter" || !groupedData) {
      return {};
    }
    return {
      scatterplotData: groupedData.pointData.filter(
        <T,>(
          d: T & { point: [number | string, number | null] },
        ): d is T & { point: [number | string, number] } => d.point[1] != null,
      ),
    };
  }, [chartType, groupedData]);

  const isYAxisAllScores = (
    groupByYMetric ? visibleMeasures : yMetric ? [yMetric] : []
  ).every(({ type }) => type === "score");

  const {
    chartProps: scatterChartProps,
    leftAxisProps: scatterChartLeftAxisProps,
    bottomAxisProps: scatterChartBottomAxisProps,
  } = useScatterPlot({
    height: chartHeight,
    data: scatterplotData ?? [],
    highlightState,
    yAxisBounds: isYAxisAllScores ? [0, 1] : undefined,
    onClick: onExperimentsClick,
    renderTooltip: ({
      point,
      metadata,
      className,
      symbolIndex,
      colorGroup,
    }) => {
      const groups = [
        ...(!isEqualSelectionType(colorGrouping, GROUP_BY_SCORE)
          ? [colorGrouping]
          : []),
        ...(!isEqualSelectionType(symbolGrouping, GROUP_BY_NONE)
          ? [symbolGrouping]
          : []),
      ];
      const xMetricGrouping = isCategoricalXMetric(xMetric) ? xMetric : null;
      const selectionTypeY = groupByYMetric ? colorGroup : yMetric!;
      return (
        <ChartTooltip
          title={metadata.name}
          subTitle={smartTimeFormat(metadata.last_updated)}
          groups={[
            ...groups,
            ...(xMetricGrouping &&
            !groups.find((g) => isEqualSelectionType(g, xMetricGrouping))
              ? [xMetricGrouping]
              : []),
          ].map((s) => ({
            label: formatSelectionTypeName(s),
            value: getObjectPathValue(metadata.metadata, s.value),
          }))}
          measures={[
            ...(!isCategoricalXMetric(xMetric) &&
            !isEqualSelectionType(xMetric, selectionTypeY)
              ? [
                  {
                    colorClassName: className,
                    symbolIndex,
                    selectionType: xMetric,
                    value: point[0],
                  },
                ]
              : []),
            {
              colorClassName: className,
              symbolIndex,
              selectionType: selectionTypeY,
              value: point[1],
            },
          ]}
        />
      );
    },
  });

  const onChartBrush = useMemo(
    () =>
      isTimeBasedXMetric(xMetric)
        ? (v: [number, number] | null) => {
            if (!v) {
              return onBrush(undefined);
            }
            if (
              isEqualSelectionType(xMetric, X_AXIS_EXPERIMENT) &&
              experimentData
            ) {
              // convert indexes to dateTimes
              const min = Math.ceil(v[0]);
              const max = Math.floor(v[1]);
              if (
                experimentData[min] == null ||
                experimentData[max] == null ||
                min > max
              ) {
                return;
              }
              onBrush({
                min: experimentData[min].last_updated,
                max: experimentData[max].last_updated,
              });
              return;
            }

            onBrush({ min: v[0], max: v[1] });
          }
        : undefined,
    [xMetric, experimentData, onBrush],
  );

  // scatterplot click could have multiple dots, so convert into an array
  const onLineChartClick = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    (e: any) => {
      if (!onExperimentsClick) {
        return;
      }
      onExperimentsClick([e]);
    },
    [onExperimentsClick],
  );

  const { lineChartData, lineChartColorMap, lineNames } = useMemo(() => {
    if (chartType !== "line" || !groupedData) {
      return {};
    }
    const { chartData, lineNames } = groupForLineChart(
      groupedData,
      symbolGrouping,
    );
    return {
      lineChartData: chartData,
      lineChartColorMap: groupByYMetric
        ? selectedMeasures
        : groupedData.colorMap,
      lineNames,
    };
  }, [
    chartType,
    groupedData,
    symbolGrouping,
    groupByYMetric,
    selectedMeasures,
  ]);

  const {
    chartProps: lineChartProps,
    leftAxisProps: lineChartLeftAxisProps,
    bottomAxisProps: lineChartBottomAxisProps,
  } = useMultiLineChart({
    height: chartHeight,
    data: lineChartData ?? [],
    yAxisBounds: isYAxisAllScores ? [0, 1] : undefined,
    seriesMetadata: groupByYMetric ? visibleMeasures : (lineNames ?? []),
    seriesColorMap: lineChartColorMap,
    highlightState,
    renderTooltip: (
      { y: values, metadata: { name, last_updated, metadata, colorGroup } },
      series,
    ) => {
      const groups = groupByYMetric
        ? []
        : [
            ...(!isEqualSelectionType(colorGrouping, GROUP_BY_SCORE)
              ? [colorGrouping]
              : []),
            ...(!isEqualSelectionType(symbolGrouping, GROUP_BY_NONE)
              ? [symbolGrouping]
              : []),
          ].map((s) => ({
            label: formatSelectionTypeName(s),
            value: getObjectPathValue(metadata, s.value),
          }));

      // should only be one valid value here if we're not grouping by metric
      const groupedValue = groupByYMetric
        ? null
        : values.find((v) => v?.value != null);

      const measures = groupedValue
        ? [
            {
              colorClassName:
                CHART_COLOR_CLASSNAMES[
                  lineChartColorMap?.[colorGroup.value][colorGroup.type] ?? 0
                ],
              symbolIndex: groupedValue?.symbolIndex,
              selectionType: yMetric ?? null,
              value: groupedValue?.value,
            },
          ]
        : values.map((v, i) => {
            const selectionType = series[i];
            return {
              colorClassName:
                lineChartColorMap == null
                  ? CHART_COLOR_CLASSNAMES[i]
                  : CHART_COLOR_CLASSNAMES[
                      lineChartColorMap[selectionType.value][selectionType.type]
                    ],
              symbolIndex: v?.symbolIndex,
              selectionType,
              value: v?.value,
            };
          });

      return (
        <ChartTooltip
          title={name}
          subTitle={smartTimeFormat(last_updated)}
          groups={groups}
          measures={measures}
          extraBottomText="(Ctrl+click to annotate)"
        />
      );
    },
    annotations: {
      data: annotationData,
      setData: setAnnotationData,
      idFn: (data) => {
        return data.metadata.id;
      },
      renderAnnotation: ({ y: values, metadata }, seriesNames, annotation) => {
        return (
          <TextAnnotation
            annotation={annotation}
            onChange={(e) =>
              setAnnotationData((prev) =>
                prev.map((a) =>
                  a.id === annotation.id ? { ...a, text: e.target.value } : a,
                ),
              )
            }
            onRemoveAnnotation={() =>
              setAnnotationData((prev) =>
                prev.filter((a) => a.id !== annotation.id),
              )
            }
          />
        );
      },
    },
    onBrush: onChartBrush,
    onClick: onLineChartClick,
    isOrdinalX: true,
    //onContextClick: onLineChartContextClick,
  });

  const { barChartData, barChartCategories, barChartSubGroups } =
    useMemo(() => {
      if (chartType !== "bar" || !groupedData || aggregationType === "all") {
        return {};
      }
      const { chartData, categories, subGroups } = groupForBarChart(
        groupedData,
        xMetric,
        // hack to pick the only value here
        isEqualSelectionType(xMetric, X_AXIS_COMPARISON) && groupByYMetric
          ? "max"
          : aggregationType,
      );
      return {
        barChartData: chartData,
        barChartCategories: categories,
        barChartSubGroups: subGroups,
      };
    }, [chartType, groupedData, xMetric, aggregationType, groupByYMetric]);

  const groupSelectionType = useCallback(
    (subGroup: SelectionType) => {
      return isEqualSelectionType(xMetric, X_AXIS_COMPARISON) && yMetric != null
        ? {
            value: subGroup.value,
            type: groupByYMetric ? ("score" as const) : yMetric.type,
          }
        : groupByYMetric
          ? subGroup
          : (yMetric ?? null);
    },
    [xMetric, groupByYMetric, yMetric],
  );

  const barChartValueRenderer = useCallback(
    (value: number, group: SelectionType, subGroup: SelectionType) => {
      const selectionType = groupSelectionType(subGroup);
      if (selectionType == null) {
        return value.toString();
      }
      return formatValueForSelectionType(value, selectionType);
    },
    [groupSelectionType],
  );

  const noSubGroups = isEqualSelectionType(xMetric, colorGrouping);
  const {
    chartProps: barChartProps,
    leftAxisProps: barChartLeftAxisProps,
    bottomAxisProps: barChartBottomAxisProps,
  } = useGroupedBarplot({
    height: chartHeight,
    data: barChartData ?? {},
    yAxisBounds: isYAxisAllScores ? [0, 1] : undefined,
    categories: barChartCategories ?? [],
    subGroups: barChartSubGroups ?? [],
    noSubGroups,
    highlightState,
    valueRenderer: barChartValueRenderer,
    renderTooltip: ({ group, subGroup, d }) => {
      const selectionType = groupSelectionType(subGroup);
      return (
        <ChartTooltip
          title={formatSelectionTypeName(group)}
          groups={
            noSubGroups || groupByYMetric
              ? []
              : [
                  {
                    label: formatSelectionTypeName(colorGrouping),
                    value: subGroup.value,
                  },
                ]
          }
          measures={[
            {
              colorClassName: d?.colorClassName,
              selectionType,
              value: d?.value,
              nullValue: "null",
            },
          ]}
        />
      );
    },
  });

  const {
    chartProps: _chartProps,
    leftAxisProps: _leftAxisProps,
    bottomAxisProps: _bottomAxisProps,
    colorMap,
    symbolMap,
  } = useMemo(() => {
    const createChartProps = <T extends NearestItems>(
      chartProps: ChartProps<T>,
    ): ChartProps<NearestItems> => ({
      ...chartProps,
      renderChartContent: (nearestItems, seriesEnabled) =>
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        chartProps.renderChartContent(nearestItems as T, seriesEnabled),
      renderTooltip: (nearestItems) =>
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        chartProps.renderTooltip?.(nearestItems as T),
    });
    switch (chartType) {
      case "line":
        return {
          chartProps: createChartProps(lineChartProps),
          leftAxisProps: lineChartLeftAxisProps,
          bottomAxisProps: lineChartBottomAxisProps,
          colorMap: lineChartColorMap ?? {},
          symbolMap: groupedData?.symbolMap ?? {},
        };
      case "bar":
        return {
          chartProps: createChartProps(barChartProps),
          leftAxisProps: barChartLeftAxisProps,
          bottomAxisProps: barChartBottomAxisProps,
          colorMap: groupedData?.colorMap ?? {},
          symbolMap: groupedData?.symbolMap ?? {},
        };
      case "scatter":
      default:
        return {
          chartProps: createChartProps(scatterChartProps),
          leftAxisProps: scatterChartLeftAxisProps,
          bottomAxisProps: scatterChartBottomAxisProps,
          colorMap: groupedData?.colorMap ?? {},
          symbolMap: groupedData?.symbolMap ?? {},
        };
    }
  }, [
    chartType,
    scatterChartProps,
    scatterChartLeftAxisProps,
    scatterChartBottomAxisProps,
    lineChartProps,
    lineChartLeftAxisProps,
    lineChartBottomAxisProps,
    barChartProps,
    barChartLeftAxisProps,
    barChartBottomAxisProps,
    groupedData,
    lineChartColorMap,
  ]);

  const onHighlight = useCallback(
    (v: HighlightedGroup | undefined) => {
      if (v) {
        highlight(v);
      } else {
        clearHighlight();
      }
    },
    [highlight, clearHighlight],
  );

  const handleResize = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();

      const startY = e.pageY;
      const startHeight = chartHeight;

      function onMouseMove(e: MouseEvent) {
        const delta = e.pageY - startY;
        setChartHeight(
          Math.min(
            MAX_CHART_HEIGHT,
            Math.max(MIN_CHART_HEIGHT, startHeight + delta),
          ),
        );
      }

      function onMouseUp() {
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
      }

      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
    },
    [chartHeight, setChartHeight],
  );

  const [isLegendExpanded, setIsLegendExpanded] = useState(false);

  const groupByOptions = useMemo(() => {
    const xMetricOption = isCategoricalXMetric(xMetric)
      ? [
          {
            label: formatSelectionTypeName(xMetric),
            value: selectionTypeKey(GROUP_BY_SCORE),
            Icon: CurlyBraces,
            selectionType: GROUP_BY_SCORE,
          },
        ]
      : [];

    return [
      ...xMetricOption,
      ...metadataFieldsToOptions(
        metadataFields,
        (st) => !isEqualSelectionType(xMetric, st),
      ),
      ...metadataFieldsToOptions(
        customColumnOptions,
        (st) => !isEqualSelectionType(xMetric, st),
      ),
    ];
  }, [metadataFields, customColumnOptions, xMetric]);

  const symbolGroupingOptions = useMemo(
    () => [
      ...metadataFieldsToOptions(
        metadataFields,
        (st) => !isEqualSelectionType(colorGrouping, st),
      ),
      ...metadataFieldsToOptions(
        customColumnOptions,
        (st) => !isEqualSelectionType(xMetric, st),
      ),
    ],
    [metadataFields, colorGrouping, customColumnOptions, xMetric],
  );

  const xAxisOptions = useMemo(
    () => [
      {
        label: "Ordered by",
        options: BASE_X_AXIS_OPTIONS,
      },
      {
        label: "Scores",
        options: valuesToOptions(scoreNames, "score"),
      },
      {
        label: "Metrics",
        options: valuesToOptions(metricNames, "metric"),
      },
      ...(metadataFields?.length
        ? [
            {
              label: "Metadata",
              options: metadataFieldsToOptions(metadataFields),
            },
          ]
        : []),
      ...(customColumnOptions?.length
        ? [
            {
              label: "Custom",
              options: metadataFieldsToOptions(customColumnOptions),
            },
          ]
        : []),
    ],
    [scoreNames, metricNames, metadataFields, customColumnOptions],
  );

  // Trying to clone the structure so the layout doesn't jump when loading state changes
  if (loading) {
    return (
      <>
        <div className="flex items-center gap-2 pt-3">
          <div className="flex-1 text-sm">Experiment analysis</div>
          <div className="flex">
            <div className="h-6 w-20 animate-pulse rounded bg-primary-200"></div>
          </div>
        </div>
        <div className={cn("pt-3 w-full", className)}>
          <div className="flex">
            <div className="relative flex grow pb-8">
              <div
                className="grow animate-pulse rounded-md bg-primary-200 pt-4"
                style={{ height: `calc(${chartHeight}px + 1.5rem)` }}
              />
            </div>
          </div>
          <div className="group mt-1 pt-1">
            <div className="w-full border-t border-transparent">
              <div className="border-b" />
            </div>
          </div>
        </div>
      </>
    );
  }

  const chartProps = {
    ..._chartProps,
    leftAxisProps: {
      ..._leftAxisProps,
      tickFormatter: (tick: number) => {
        if (
          groupByYMetric &&
          visibleMeasures.some((m) => m.type === "metadata")
        ) {
          return tick.toLocaleString(undefined, {
            maximumFractionDigits: 1,
          });
        }
        return formatValueForSelectionType(
          tick,
          groupByYMetric ? visibleMeasures[0] : yMetric!,
        );
      },
    },
    bottomAxisProps: {
      ..._bottomAxisProps,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      tickFormatter: (tick: any) => {
        if (
          isEqualSelectionType(xMetric, X_AXIS_EXPERIMENT) ||
          isEqualSelectionType(xMetric, X_AXIS_TIME)
        ) {
          return null;
        }
        return formatValueForSelectionType(tick, xMetric);
      },
      timestamps: isEqualSelectionType(xMetric, X_AXIS_TIME),
    },
  };

  const seriesLabels: LegendLabelData[] = toLegendLabelData(colorMap, "points");
  const symbolLabels: LegendLabelData[] = !isEqualSelectionType(
    symbolGrouping,
    GROUP_BY_NONE,
  )
    ? toLegendLabelData(symbolMap, "symbols")
    : [];

  const legendLabels = [...seriesLabels, ...symbolLabels];
  const maxVisibleLegendLabels = Math.floor(chartHeight / 22);
  const canLegendExpand = legendLabels.length > maxVisibleLegendLabels;

  const showSymbolGroupings =
    metadataFields && metadataFields?.length > 0 && chartType !== "bar";
  const showXMetricAggregations =
    isCategoricalXMetric(xMetric) ||
    (isEqualSelectionType(xMetric, X_AXIS_COMPARISON) && !groupByYMetric);

  return (
    <>
      <div className="flex items-center gap-2 pt-3">
        <div className="flex-1 text-sm">Experiment analysis</div>
        <div className="flex">
          <Combobox<{
            label: string;
            value: string;
            Icon: LucideIcon;
            selectionType: SelectionType;
          }>
            options={groupByOptions}
            onChange={(v, { selectionType }) => {
              if (isEqualSelectionType(selectionType, colorGrouping)) {
                setColorGrouping(GROUP_BY_SCORE);
              } else {
                setColorGrouping(selectionTypeSchema.parse(selectionType));
              }
            }}
            placeholderLabel={
              <span className="flex items-center gap-1">
                <StretchHorizontal className="size-3" />
                Group
              </span>
            }
            renderComboboxDisplayLabel={({ selectionType }) => {
              const value = isEqualSelectionType(selectionType, GROUP_BY_SCORE)
                ? isCategoricalXMetric(xMetric)
                  ? formatSelectionTypeName(xMetric)
                  : "none"
                : formatSelectionTypeName(selectionType);

              return (
                <span className="flex items-center gap-1">
                  <StretchHorizontal className="size-3" />
                  {value === "none" ? (
                    "Group"
                  ) : (
                    <ComboboxDisplayLabel label="Group by" value={value} />
                  )}
                </span>
              );
            }}
            renderOptionLabel={({ label, Icon }) =>
              Icon ? (
                <>
                  <Icon className="mr-1 size-3 flex-none text-primary-500" />
                  {label}
                </>
              ) : (
                label
              )
            }
            variant="button"
            buttonSize="xs"
            buttonClassName={cn({
              "rounded-r-none": showSymbolGroupings,
            })}
            iconClassName="hidden"
            selectedValue={selectionTypeKey(colorGrouping)}
            searchPlaceholder="Select grouping"
            noResultsLabel="No metadata"
          />
          {showSymbolGroupings && (
            <Combobox<{
              label: string;
              value: string;
              Icon: LucideIcon;
              selectionType: SelectionType;
            }>
              options={symbolGroupingOptions}
              onChange={(v, { selectionType }) => {
                if (isEqualSelectionType(selectionType, symbolGrouping)) {
                  setSymbolGrouping(GROUP_BY_NONE);
                } else {
                  setSymbolGrouping(selectionTypeSchema.parse(selectionType));
                }
              }}
              placeholderLabel={<span className="font-normal">Symbols</span>}
              iconClassName="hidden"
              renderOptionLabel={({ label, Icon }) =>
                Icon ? (
                  <>
                    <Icon className="mr-1 size-3 flex-none text-primary-500" />
                    {label}
                  </>
                ) : (
                  label
                )
              }
              renderComboboxDisplayLabel={({ selectionType }) =>
                isEqualSelectionType(selectionType, GROUP_BY_NONE) ? (
                  <span className="font-normal">Symbols</span>
                ) : (
                  <ComboboxDisplayLabel
                    label="Symbols by"
                    value={formatSelectionTypeName(selectionType)}
                  />
                )
              }
              variant="button"
              buttonSize="xs"
              buttonClassName="rounded-l-none border-l-0"
              selectedValue={selectionTypeKey(symbolGrouping)}
              searchPlaceholder="Select grouping"
              noResultsLabel="No metadata"
            />
          )}
        </div>
        {children}
      </div>
      <div className={cn("pt-3 w-full", className)}>
        <div className="flex">
          <div className="relative flex grow pb-8 pl-8">
            <div className="absolute bottom-6 left-0 top-0 z-10 flex w-6 items-center justify-center">
              <Combobox<{
                label: string;
                value: string;
                selectionType: SelectionType;
              }>
                side="left"
                align="start"
                options={[
                  {
                    label: "Scores",
                    options: valuesToOptions(scoreNames, "score"),
                  },
                  {
                    label: "Metrics",
                    options: valuesToOptions(metricNames, "metric"),
                  },
                  ...(numericMetadataFields?.length
                    ? [
                        {
                          label: "Metadata",
                          options: metadataFieldsToOptions(
                            numericMetadataFields,
                          ),
                        },
                      ]
                    : []),
                ]}
                onChange={(v, { selectionType }) => {
                  if (groupByYMetric) {
                    toggleSelectedMeasures([selectionType]);
                    return;
                  }

                  setYMetric(selectionType);
                }}
                stayOpenOnChange={groupByYMetric}
                placeholderLabel={
                  <>
                    <span className="font-medium">
                      {visibleMeasures.length} item
                      {visibleMeasures.length !== 1 && "s"}
                    </span>
                  </>
                }
                renderComboboxDisplayLabel={({ selectionType }) => (
                  <ComboboxDisplayLabel
                    value={formatSelectionTypeName(selectionType)}
                  />
                )}
                iconClassName="hidden"
                bottomActions={
                  groupByYMetric
                    ? [
                        {
                          label: "Select all scores",
                          onSelect: () => {
                            toggleSelectedMeasures(
                              scoreNames
                                .filter(
                                  (s) => selectedMeasures[s]?.score == null,
                                )
                                .map((s) => ({ type: "score", value: s })),
                            );
                          },
                          disabled: scoreNames.every(
                            (v) => selectedMeasures[v]?.score != null,
                          ),
                        },
                        {
                          label: "Deselect all scores",
                          onSelect: () => {
                            toggleSelectedMeasures(
                              scoreNames
                                .filter(
                                  (s) => selectedMeasures[s]?.score != null,
                                )
                                .map((s) => ({ type: "score", value: s })),
                            );
                          },
                          disabled: scoreNames.every(
                            (v) => selectedMeasures[v]?.score == null,
                          ),
                        },
                      ]
                    : undefined
                }
                variant="button"
                buttonSize="xs"
                buttonClassName="-rotate-90 origin-center h-6"
                selectedValue={
                  groupByYMetric || !yMetric
                    ? undefined
                    : selectionTypeKey(yMetric)
                }
                selectedValues={
                  groupByYMetric
                    ? visibleMeasures.map((st) => selectionTypeKey(st))
                    : undefined
                }
                searchPlaceholder="Select y-axis"
                noResultsLabel="No metrics"
              />
            </div>
            <div className="absolute bottom-0 left-8 right-0 flex justify-center">
              <Combobox<{
                label: string;
                value: string;
                selectionType: SelectionType;
              }>
                options={xAxisOptions}
                onChange={(_, { selectionType }) => {
                  setXMetric(selectionType);
                  if (isEqualSelectionType(colorGrouping, selectionType)) {
                    setColorGrouping(GROUP_BY_SCORE);
                  }

                  if (
                    isCategoricalXMetric(selectionType) &&
                    aggregationType !== "all"
                  ) {
                    setSymbolGrouping(GROUP_BY_NONE);
                  }
                }}
                placeholderLabel={<span className="font-normal">X Axis</span>}
                renderComboboxDisplayLabel={({ label, selectionType }) => {
                  const showLabel = !isCategoricalXMetric(selectionType);
                  const selectionTypeName =
                    formatSelectionTypeName(selectionType);
                  return (
                    <ComboboxDisplayLabel
                      value={
                        showLabel
                          ? label
                          : isEqualSelectionType(
                                selectionType,
                                colorGrouping,
                              ) ||
                              isEqualSelectionType(
                                colorGrouping,
                                GROUP_BY_SCORE,
                              )
                            ? `Group by ${selectionTypeName}`
                            : `Group by ${formatSelectionTypeName(colorGrouping)}, then by ${selectionTypeName}`
                      }
                    />
                  );
                }}
                variant="button"
                buttonSize="xs"
                buttonClassName={cn("h-6", {
                  "rounded-r-none": showXMetricAggregations,
                })}
                selectedValue={selectionTypeKey(xMetric)}
                searchPlaceholder="Select x-axis"
                noResultsLabel="No metrics"
              />
              {showXMetricAggregations && (
                <Combobox
                  options={[
                    { label: "Avg", value: "avg" },
                    { label: "Max", value: "max" },
                    //{ label: "All points", value: "all" },
                  ]}
                  onChange={(v) => {
                    if (!v) {
                      return;
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    setAggregationType(v as any);
                    setSymbolGrouping(GROUP_BY_NONE);
                  }}
                  variant="button"
                  buttonSize="xs"
                  buttonClassName="rounded-l-none border-l-0 h-6"
                  selectedValue={aggregationType}
                  searchPlaceholder="Select aggregation"
                  noResultsLabel="No aggregation"
                />
              )}
            </div>
            <Chart {...chartProps} className="grow pt-4" />
          </div>
          <Tooltip
            open={canLegendExpand ? isLegendExpanded : false}
            onOpenChange={(open) => {
              setIsLegendExpanded(open);
            }}
            delayDuration={50}
          >
            <TooltipTrigger asChild>
              <div
                className={cn("ml-7", {
                  invisible: canLegendExpand && isLegendExpanded,
                })}
              >
                <LegendLabels
                  className="min-w-32 max-w-[35vw]"
                  legendLabelData={legendLabels.slice(
                    0,
                    maxVisibleLegendLabels,
                  )}
                  highlightState={highlightState}
                  onHighlight={(v) => {
                    if (legendLabels.length > maxVisibleLegendLabels) {
                      return;
                    }
                    onHighlight(v);
                  }}
                />
                {legendLabels.length > maxVisibleLegendLabels && (
                  <div className="text-right text-xs text-primary-500">
                    {legendLabels.length - maxVisibleLegendLabels} more
                  </div>
                )}
              </div>
            </TooltipTrigger>
            <TooltipPortal>
              <TooltipContent
                className="rounded-md border-0 bg-primary-50 shadow-md duration-0 data-[side=top]:duration-0"
                side="top"
                align="end"
                alignOffset={-12}
                sideOffset={6}
                style={{
                  // make the tooltip appear over top of the content
                  transform: "translate(0, 100%)",
                  maxHeight:
                    "calc(100vh - var(--radix-tooltip-content-available-height))",
                }}
                avoidCollisions={false}
              >
                <LegendLabels
                  className="min-w-32 max-w-[20vw]"
                  legendLabelData={legendLabels}
                  highlightState={highlightState}
                  onHighlight={onHighlight}
                />
              </TooltipContent>
            </TooltipPortal>
          </Tooltip>
        </div>
        <div
          className="group mt-1 cursor-row-resize pt-1"
          onMouseDown={handleResize}
        >
          <div className="w-full border-t border-transparent group-hover:border-accent-300">
            <div className="border-b group-hover:border-blue-300" />
          </div>
        </div>
      </div>
    </>
  );
};

function valuesToOptions(values: string[], s: SelectionTypesEnum) {
  return values.map((v) => {
    const selectionType = {
      value: v,
      type: s,
    };
    return {
      label: s === "metric" ? formatSelectionTypeName(selectionType) : v,
      value: selectionTypeKey(selectionType),
      selectionType,
    };
  });
}

function metadataFieldsToOptions(
  fieldPaths: string[][] | undefined,
  filter: (s: SelectionType) => boolean = () => true,
) {
  const result: {
    label: string;
    value: string;
    Icon: LucideIcon;
    selectionType: SelectionType;
  }[] = [];
  fieldPaths?.forEach((fieldPath) => {
    const selectionType: SelectionType = {
      value: JSON.stringify(fieldPath),
      type: "metadata",
    };
    if (!filter(selectionType)) {
      return;
    }
    result.push({
      label: fieldPath.join("."),
      value: selectionTypeKey(selectionType),
      Icon: CurlyBraces,
      selectionType,
    });
  });

  return result;
}

const TextAnnotation = ({
  annotation,
  onChange,
  onRemoveAnnotation,
}: {
  annotation: AnnotationData;
  onChange: ChangeEventHandler<HTMLInputElement>;
  onRemoveAnnotation: () => void;
}) => {
  const ref = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (!ref.current || annotation.text) {
      return;
    }
    ref.current.focus();
  }, [annotation.text]);

  return (
    <div
      className={cn(
        "group flex rounded-md shadow-lg",
        // copy input styles
        "border border-primary-200 bg-primary-50 dark:bg-primary-100 text-sm ring-offset-background focus-within:outline-none focus-within:ring",
      )}
    >
      <AutosizeInput
        ref={ref}
        className={
          "h-auto border-none bg-transparent px-2 py-1 text-xs ring-0 focus-within:ring-0"
        }
        minWidthClassName="min-w-24"
        spacerClassName={"px-2 text-xs"}
        placeholder="Enter label"
        value={annotation.text}
        onChange={onChange}
        onKeyDown={(e) => {
          if (e.key === "Escape") {
            onRemoveAnnotation();
          }
        }}
      />
      <Button
        className="absolute -right-1.5 -top-1.5 hidden rounded-full border bg-background p-0.5 group-hover:flex"
        size="inline"
        transparent
        onClick={onRemoveAnnotation}
      >
        <XIcon className="size-2 text-primary-600" />
      </Button>
    </div>
  );
};

const ChartTooltip = ({
  title,
  subTitle,
  groups,
  measures,
  extraBottomText,
}: {
  title: string;
  subTitle?: string;
  groups: { label: string; value: React.ReactNode }[];
  measures: {
    colorClassName?: string;
    symbolIndex?: number;
    selectionType: SelectionType | null;
    value: string | number | null | undefined;
    nullValue?: string;
  }[];
  extraBottomText?: string;
}) => {
  return (
    <div className="flex flex-col gap-1 text-xs">
      {title && <div className="line-clamp-3 font-medium">{title}</div>}
      {subTitle && <div className="mb-2 text-primary-500">{subTitle}</div>}
      {groups.map(({ label, value }, i) => (
        <div key={i} className="flex justify-between">
          <span>{label}</span>
          <span className="ml-4 truncate font-medium">
            {value ?? <span className="text-primary-500">null</span>}
          </span>
        </div>
      ))}
      {measures.map(
        (
          { colorClassName, symbolIndex, selectionType, value, nullValue },
          i,
        ) => {
          if (!selectionType) return null;
          return (
            <div key={i} className="mb-0.5 flex text-xs tabular-nums">
              <div className="flex flex-1 items-center gap-0.5 overflow-hidden">
                <ChartSymbol
                  className={colorClassName ?? DEFAULT_COLOR_CLASSNAME}
                  index={symbolIndex ?? 0}
                  size={8}
                />
                <span className="flex-1 truncate">
                  {formatSelectionTypeName(selectionType)}
                </span>
              </div>
              <span className="ml-4 truncate font-medium">
                {(value != null
                  ? formatValueForSelectionType(value, selectionType)
                  : nullValue) ?? <NullFormatter />}
              </span>
            </div>
          );
        },
      )}

      {extraBottomText && (
        <div className="mt-2 text-xs text-primary-500">{extraBottomText}</div>
      )}
    </div>
  );
};

const ComboboxDisplayLabel = ({
  label,
  value,
}: {
  label?: string;
  value: string;
}) => {
  return (
    <>
      {label && (
        <>
          <span className="font-normal">{label}</span>{" "}
        </>
      )}
      <span className="font-medium">{value}</span>
    </>
  );
};

function toLegendLabelData(
  groupData: Record<string, Record<SelectionTypesEnum, number>>,
  legendType: HighlightedGroupTypeEnum,
): LegendLabelData[] {
  return Object.entries(groupData)
    .flatMap(([groupName, groupTypes]) =>
      Object.entries(groupTypes).map(([type, v]) => {
        const selectionType = {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          type: type as SelectionTypesEnum,
          value: groupName,
        };
        return {
          groupVal: selectionType,
          label: formatSelectionTypeName(selectionType),
          ...(legendType === "points"
            ? { type: "points" as const, colorIndex: v }
            : { type: "symbols" as const, symbolIndex: v }),
        };
      }),
    )
    .toSorted(chartLegendCompareFn);
}
const chartLegendCompareFn = (
  {
    groupVal: selectionTypeA,
    label: labelA,
  }: { groupVal: SelectionType; label: string },
  {
    groupVal: selectionTypeB,
    label: labelB,
  }: { groupVal: SelectionType; label: string },
) => {
  if (selectionTypeA.type !== selectionTypeB.type) {
    return compareSelectionTypes(selectionTypeA.type, selectionTypeB.type);
  }

  const aFloat = parseFloat(labelA);
  const bFloat = parseFloat(labelB);
  if (!Number.isNaN(aFloat) && !Number.isNaN(bFloat)) {
    return aFloat - bFloat;
  }

  if (labelA === labelB) return 0;
  if (labelB === "null") return -1;
  else if (labelA === "null") return 1;
  else if (labelA < labelB) return -1;
  else if (labelA > labelB) return 1;
  else return 0;
};

type LegendLabelData = {
  groupVal: SelectionType;
  label: string;
} & (
  | {
      type: "points";
      colorIndex: number;
    }
  | { type: "symbols"; symbolIndex: number }
);

const LegendLabels = ({
  className,
  legendLabelData,
  highlightState,
  onHighlight,
}: {
  className?: string;
  legendLabelData: LegendLabelData[];
  highlightState?: HighlightState;
  onHighlight: (v: HighlightedGroup | undefined) => void;
}) => {
  return (
    <div className={cn("max-w-[20vw] min-w-32", className)}>
      {legendLabelData.map((v, i) => {
        return (
          <LegendLabel
            key={i}
            // not sure why, but margin bottom here prevents flickering between labels
            className={cn("pb-1.5 mb-px transition-opacity", {
              "opacity-60": !isLineHighlighted(v.groupVal, highlightState),
            })}
            onMouseEnter={() =>
              onHighlight({
                groupVal: v.groupVal,
                type: v.type,
              })
            }
            onMouseLeave={() => onHighlight(undefined)}
            {...v}
          />
        );
      })}
    </div>
  );
};

const LegendLabel = (
  props: LegendLabelData & {
    className?: string;
    onMouseEnter?: React.MouseEventHandler;
    onMouseLeave?: React.MouseEventHandler;
  },
) => {
  const { className, label, type, onMouseEnter, onMouseLeave } = props;
  const symbol =
    type === "points" ? (
      <ChartSymbol
        className={cn(
          CHART_COLOR_CLASSNAMES[props.colorIndex] || DEFAULT_COLOR_CLASSNAME,
          className,
        )}
        index={0}
        size={8}
      />
    ) : (
      <ChartSymbol
        className={cn("fill-primary-800", className)}
        index={props.symbolIndex}
        size={8}
      />
    );
  return (
    <div
      className={cn("flex justify-end", className)}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="flex overflow-hidden">
        <div className={cn("mr-0.5 truncate text-xs")} title={label}>
          {label}
        </div>
        {symbol}
      </div>
    </div>
  );
};
