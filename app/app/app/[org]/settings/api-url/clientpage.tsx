"use client";

import { LATEST_VERSION } from "#/ui/api-version/api-version";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { Button } from "#/ui/button";
import { BlueLink } from "#/ui/link";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { useOrg, useUser } from "#/utils/user";
import { useEffect, useMemo, useState } from "react";
import * as semver from "semver";
import CodeToCopy from "#/ui/code-to-copy";
import { AlertCircle, CheckCircle, PencilLine, Terminal } from "lucide-react";
import { type getOrganization } from "#/app/app/actions";
import {
  type getBrainstoreLicense,
  type patchIsUniversalApi,
  type patchOrganizationUrls,
} from "./actions";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import {
  type BtSessionToken,
  isAuthenticatedSession,
  sessionFetchProps,
  useSessionToken,
} from "#/utils/auth/session-token";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { _urljoin } from "braintrust/util";
import { type Permission } from "@braintrust/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useAuth } from "@clerk/nextjs";
import { CSPForm } from "./csp";
import { cn } from "#/utils/classnames";
import { useIsClient } from "#/utils/use-is-client";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { Skeleton } from "#/ui/skeleton";
import { DataPlaneSettingsForm } from "./data-plane-settings-form";
import { useRouter } from "next/navigation";
async function respErrorStr(resp: Response): Promise<string> {
  return `${resp.status} error: ${await resp.text()}`;
}

function errorStr(e: unknown): string {
  if (e instanceof Error) {
    return e.toString();
  } else {
    return JSON.stringify(e);
  }
}

export default function Page({
  orgName,
  orgPermissions,
  isCspEnabled,
}: {
  orgName: string;
  orgPermissions: Permission[];
  isCspEnabled: boolean;
}) {
  const { user, invalidate: refreshUser } = useUser();
  const org = useOrg();

  const {
    data: initialOrgInfo,
    isLoading: isLoadingOrgInfo,
    invalidate: refreshOrgInfo,
  } = useQueryFunc<typeof getOrganization>({
    fName: "getOrganization",
    args: { org_name: orgName },
  });

  const { data: brainstoreLicense, isLoading: isLoadingBrainstoreLicense } =
    useQueryFunc<typeof getBrainstoreLicense>({
      fName: "getBrainstoreLicense",
      args: { org_name: orgName },
    });

  const apiUrl = initialOrgInfo?.api_url;
  const isUniversal = initialOrgInfo?.is_universal_api;
  const proxyUrl = initialOrgInfo?.proxy_url;
  const realtimeUrl = initialOrgInfo?.realtime_url;

  const { getToken } = useAuth();
  const router = useRouter();

  // We don't want to update the user's configuration via an effect that
  // just triggers when you load the page, so mark the flag as dirty if the
  // user has changed their API URL.
  const [canSaveUniversalStatus, setCanSaveUniversalStatus] = useState(false);

  const saveSettings = async (data: {
    apiUrl: string | null;
    proxyUrl: string | null;
    realtimeUrl: string | null;
  }) => {
    console.log(data);
    await invokeServerAction<typeof patchOrganizationUrls>({
      fName: "patchOrganizationUrls",
      args: {
        org_name: orgName,
        api_url: data.apiUrl ?? null,
        proxy_url: data.proxyUrl ?? null,
        realtime_url: data.realtimeUrl ?? null,
      },
      getToken,
    });
    await refreshAPIVersion();
    setCanSaveUniversalStatus(true);
    refreshUser();
    toast.success("Data plane settings updated");
    router.refresh();
    setEditingField(null);

    await refreshOrgInfo();
  };

  const {
    version: apiVersion,
    universal,
    refresh: refreshAPIVersion,
  } = useAPIVersion();

  useEffect(() => {
    if (
      universal !== undefined &&
      universal !== isUniversal &&
      apiUrl &&
      canSaveUniversalStatus
    ) {
      setCanSaveUniversalStatus(false);
      invokeServerAction<typeof patchIsUniversalApi>({
        fName: "patchIsUniversalApi",
        args: {
          org_name: orgName,
          is_universal_api: universal,
        },
        getToken,
      })
        .then(() => {
          toast.success(
            `Updated universal API status to ${universal ? "true" : "false"}`,
          );
        })
        .catch((e) => {
          toast.error(`Failed to update universal API status`, {
            description: `${e}`,
          });
        })
        .finally(() => {
          refreshUser();
        });
    }
  }, [
    apiUrl,
    canSaveUniversalStatus,
    orgName,
    refreshUser,
    universal,
    isUniversal,
    getToken,
  ]);

  const isAllowedToEditURLs = orgPermissions.includes("update");

  const [editingField, setEditingField] = useState<
    "api" | "proxy" | "realtime" | null
  >(null);

  const isClient = useIsClient();

  if (!user) {
    return null;
  }

  const heading = (
    <>
      <h2 className="text-lg font-semibold">Data plane</h2>
      <p className="mb-6 block text-sm text-primary-600">
        Configure data plane settings for this organization
      </p>
    </>
  );

  if (isLoadingOrgInfo || isLoadingBrainstoreLicense) {
    return (
      <>
        {heading}
        <Skeleton className="mb-2 h-20 w-full" />
        <Skeleton className="mb-2 h-20 w-full" />
        <Skeleton className="mb-2 h-20 w-full" />
      </>
    );
  }

  if (!(apiUrl || brainstoreLicense) && isClient) {
    // We don't want to let people self-host without talking to us. So if they're not already
    // self-hosting, let's hide this page. And we can use the Brainstore license as a way to turn
    // on access to it.
    return (
      <div>
        {heading}
        <TableEmptyState
          label={
            <>
              This organization is running on the Braintrust hosted platform. If
              you are interested in self-hosting, please reach out to us at{" "}
              <BlueLink href="mailto:<EMAIL>">
                <EMAIL>
              </BlueLink>
              .
            </>
          }
        />
      </div>
    );
  }

  return (
    <>
      {heading}

      {!isAllowedToEditURLs && (
        <TableEmptyState
          label='To edit these settings, ask your administrator to grant the
          "Manage settings" permission for this organization.'
          className="mb-6"
        />
      )}

      <DataPlaneSettingsForm
        focusField={editingField}
        open={editingField !== null && isAllowedToEditURLs}
        setOpen={(open) => {
          if (!open) {
            setEditingField(null);
          }
        }}
        onSubmit={saveSettings}
        initialData={{
          apiUrl: apiUrl ?? null,
          proxyUrl: proxyUrl ?? null,
          realtimeUrl: realtimeUrl ?? null,
        }}
      />
      <div className="mb-6">
        <APIUrl
          apiUrl={apiUrl ?? org.api_url}
          apiVersion={apiVersion}
          onEdit={() => {
            setEditingField("api");
          }}
          canEdit={isAllowedToEditURLs}
        />
      </div>
      <div className="mb-6">
        <ProxyUrl
          url={proxyUrl ?? org.proxy_url}
          canEdit={isAllowedToEditURLs}
          onEdit={() => {
            setEditingField("proxy");
          }}
        />
      </div>
      <div className="mb-6">
        <RealtimeUrl
          url={realtimeUrl ?? org.realtime_url}
          canEdit={isAllowedToEditURLs}
          onEdit={() => {
            setEditingField("realtime");
          }}
        />
      </div>
      {brainstoreLicense && (
        <div className="mb-6 flex flex-col gap-2 rounded-md border bg-primary-50 p-4">
          <div className="mb-3 flex-1">
            <div className="mb-1 text-sm">Brainstore license</div>
            <div className="mt-1 text-sm text-primary-600">
              Copy your license below to use it in your production environment.
            </div>
          </div>
          <CodeToCopy
            data={"brainstore-*************"}
            textToCopy={brainstoreLicense ?? "foo"}
            language="bash"
          />
        </div>
      )}
      {org.id && isCspEnabled && (
        <CSPForm
          orgId={org.id}
          orgName={orgName}
          orgPermissions={orgPermissions}
          refreshUser={refreshUser}
        />
      )}
    </>
  );
}

function APIUrl({
  apiUrl,
  apiVersion,
  onEdit,
  canEdit,
}: {
  apiUrl: string;
  apiVersion: string;
  onEdit: VoidFunction;
  canEdit: boolean;
}) {
  const { getOrRefreshToken } = useSessionToken();

  const [verifiedPing, setVerifiedPing] = useState(false);
  const [pingError, setPingError] = useState<string | null>(null);
  const [sessionToken, setSessionToken] = useState<BtSessionToken | undefined>(
    undefined,
  );

  const isClient = useIsClient();

  useEffect(() => {
    (async () => {
      if (apiUrl) {
        if (!apiUrl.startsWith("http")) {
          setPingError("Invalid API URL. Must start with http:// or https://");
          setVerifiedPing(true);
          return;
        }

        try {
          const sessionToken = await getOrRefreshToken();
          setSessionToken(sessionToken);
          const resp = await apiFetchGet(`${apiUrl}/ping`, sessionToken);
          if (!resp.ok) {
            setPingError(await respErrorStr(resp));
          } else {
            setPingError(null);
          }
        } catch (e) {
          console.error(e);
          setPingError(errorStr(e));
        } finally {
          setVerifiedPing(true);
        }
      }
    })();
  }, [apiUrl, getOrRefreshToken]);

  return (
    <div className="flex gap-2 rounded-md border bg-primary-50 p-4">
      <div className="flex-1">
        <div className="mb-1 text-sm">API URL</div>
        <div className="font-mono text-lg">{apiUrl}</div>
        <div className="mt-1 text-sm text-primary-600">
          {isClient &&
          apiVersion &&
          semver.valid(apiVersion) &&
          semver.lt(apiVersion, LATEST_VERSION) ? (
            <>
              Your API version ({apiVersion}) is out of date with the latest (
              {LATEST_VERSION}). Please update your API (
              <BlueLink
                className="text-accent-500 underline"
                href="/docs/guides/self-hosting"
                target="_blank"
              >
                view docs
              </BlueLink>
              ).
            </>
          ) : isClient && apiVersion ? (
            <>You are using the latest API version {apiVersion}.</>
          ) : (
            <Skeleton className="w-96">Loading</Skeleton>
          )}
        </div>
        <PingStatus verifiedPing={verifiedPing} pingError={pingError} />
      </div>
      <TestEndpoint url={apiUrl} sessionToken={sessionToken} endpoint="ping" />
      <Button size="sm" Icon={PencilLine} onClick={onEdit} disabled={!canEdit}>
        Edit
      </Button>
    </div>
  );
}

function ProxyUrl({
  url,
  canEdit,
  onEdit,
}: {
  url: string;
  canEdit: boolean;
  onEdit: VoidFunction;
}) {
  const [verifiedPing, setVerifiedPing] = useState(false);
  const [pingError, setPingError] = useState<string | null>(null);

  const queryUrl = useMemo((): string | undefined => {
    if (!url) return undefined;
    try {
      const urlObject = new URL(url);
      if (!["http:", "https:"].includes(urlObject.protocol)) {
        throw new Error("Must start with http:// or https://");
      }
      return url;
    } catch (e) {
      const eStr = e instanceof Error ? e.toString() : `${e}`;
      setPingError(`Invalid proxy url: ${eStr}`);
      setVerifiedPing(true);
      return undefined;
    }
  }, [url]);

  useEffect(() => {
    (async () => {
      if (!queryUrl) return;
      try {
        const resp = await fetch(queryUrl, {
          mode: "cors",
          credentials: "include",
        });
        if (!resp.ok) {
          setPingError(await respErrorStr(resp));
        } else {
          setPingError(null);
        }
      } catch (e) {
        setPingError(errorStr(e));
      } finally {
        setVerifiedPing(true);
      }
    })();
  }, [queryUrl]);

  return (
    <div className="flex gap-2 rounded-md border bg-primary-50 p-4">
      <div className="flex-1">
        <div className="mb-1 text-sm">Proxy URL</div>
        <div className="font-mono text-lg ">{queryUrl}</div>
        <PingStatus verifiedPing={verifiedPing} pingError={pingError} />
      </div>
      {queryUrl && <TestEndpoint url={queryUrl} endpoint="" />}
      <Button size="sm" Icon={PencilLine} onClick={onEdit} disabled={!canEdit}>
        Edit
      </Button>
    </div>
  );
}

function RealtimeUrl({
  url,
  canEdit,
  onEdit,
}: {
  url: string;
  canEdit: boolean;
  onEdit: VoidFunction;
}) {
  const [verifiedPing, setVerifiedPing] = useState(false);
  const [pingError, setPingError] = useState<string | null>(null);
  const { websocketUrl, httpUrl } = useMemo(() => {
    const empty = { websocketUrl: undefined, httpUrl: undefined };
    if (!url) return empty;
    try {
      const urlObject = new URL(url);
      if (!["ws:", "wss:"].includes(urlObject.protocol)) {
        throw new Error("Must start with ws:// or wss://");
      }
      const httpUrl = new URL(urlObject);
      httpUrl.protocol = httpUrl.protocol.replace("ws", "http");
      return {
        websocketUrl: urlObject.toString(),
        httpUrl: httpUrl.toString(),
      };
    } catch (e) {
      const eStr = e instanceof Error ? e.toString() : `${e}`;
      setPingError(`Invalid realtime url: ${eStr}`);
      setVerifiedPing(true);
      return empty;
    }
  }, [url]);

  const NUM_TRIES = 3;
  useEffect(() => {
    (async () => {
      if (websocketUrl) {
        try {
          for (let tries = 0; tries < NUM_TRIES; tries++) {
            const ws = new WebSocket(
              _urljoin(websocketUrl, "channel/ping/websocket"),
            );
            try {
              await new Promise((resolve, reject) => {
                ws.addEventListener("open", () => {
                  resolve(null);
                });
                ws.addEventListener("close", () => {
                  resolve(null);
                });
                ws.addEventListener("error", (e) => {
                  reject(e);
                });
              });
            } catch (e) {
              if (tries === NUM_TRIES - 1) {
                throw e;
              } else {
                console.warn("Failed to connect to websocket, retrying", e);
                // For some customers' deployments (e.g. Netflix), this fetch
                // can help "warm up" their firewall before initializing the
                // websocket.
                await fetch(httpUrl, { method: "GET" });
              }
            }
            setPingError(null);
          }
        } catch (e) {
          console.error(e);
          setPingError(errorStr(e));
        } finally {
          setVerifiedPing(true);
        }
      }
    })();
  }, [httpUrl, websocketUrl]);

  return (
    <div className="flex gap-2 rounded-md border bg-primary-50 p-4">
      <div className="flex-1">
        <div className="mb-1 text-sm">Realtime URL</div>
        <div className="font-mono text-lg ">{httpUrl ?? url}</div>
        <PingStatus verifiedPing={verifiedPing} pingError={pingError} />
      </div>
      {httpUrl && <TestEndpoint url={httpUrl} endpoint="" />}
      <Button size="sm" Icon={PencilLine} onClick={onEdit} disabled={!canEdit}>
        Edit
      </Button>
    </div>
  );
}

function TestEndpoint({
  url,
  sessionToken,
  endpoint,
}: {
  url: string;
  sessionToken?: BtSessionToken;
  endpoint: string;
}) {
  const headers =
    sessionToken && isAuthenticatedSession(sessionToken)
      ? sessionFetchProps(sessionToken).sessionHeaders
      : {};

  const curlCommand = (endpoint: string, show: boolean) => {
    let base = `curl -X GET "${_urljoin(url, endpoint)}"`;

    if (Object.keys(headers).length > 0) {
      base += ` \\
  ${
    show
      ? Object.entries(headers)
          .map(([k, v]) => `--header "${k}: ${v}"`)
          .join("\\\n  ")
      : "--header Authorization: Bearer <COPY_TO_GET_THE_VALUE>"
  }`;
    }
    return base;
  };

  const curlCommandContents = curlCommand(endpoint, false);
  const copyCurlCommandContents = curlCommand(endpoint, true);
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button size="sm" Icon={Terminal}>
          Test
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="w-96"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <div className="mb-2 text-sm">
          Test <code>{endpoint}</code> from the command line
          {Object.keys(headers).length > 0 && (
            <div className="text-xs text-primary-500">
              Copy to get the command with token
            </div>
          )}
        </div>
        <CodeToCopy
          data={curlCommandContents}
          textToCopy={copyCurlCommandContents}
          language="bash"
          highlighterClassName="text-xs"
        />
      </PopoverContent>
    </Popover>
  );
}

const PingStatus = ({
  verifiedPing,
  pingError,
}: {
  verifiedPing: boolean;
  pingError: string | null;
}) => {
  return (
    <Button
      isLoading={!verifiedPing}
      size="xs"
      transparent
      Icon={pingError ? AlertCircle : CheckCircle}
      className={cn("pointer-events-none px-0 h-auto mt-2", {
        "text-bad-700": pingError,
        "text-good-700": !pingError,
      })}
    >
      {pingError || "Ping successful"}
    </Button>
  );
};
