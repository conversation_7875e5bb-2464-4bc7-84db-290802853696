"use client";

import { useAuth } from "@clerk/nextjs";
import { useQueryFunc } from "#/utils/react-query";

import { Button } from "#/ui/button";
import { useState } from "react";
import { toast } from "sonner";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { type Permission } from "@braintrust/typespecs";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { ExternalLink } from "#/ui/link";

import {
  type getOrganizationContentSecurityPolicies,
  type setOrganizationContentSecurityPolicies,
} from "#/app/app/actions";
import TextArea from "#/ui/text-area";

type CSPFormProps = {
  orgId: string;
  orgName: string;
  orgPermissions: Permission[];
  initialCsp: string | null;
  initialCspReportOnly: string | null;
  refreshCsps: () => void;
  refreshUser: () => void;
};

function CSPFormContent({
  orgId,
  orgName,
  orgPermissions,
  initialCsp,
  initialCspReportOnly,
  refreshCsps,
  refreshUser,
}: CSPFormProps) {
  const { getToken } = useAuth();

  const [csp, setCSP] = useState<string>(initialCsp ?? "");

  const [cspReportOnly, setCSPReportOnly] = useState<string>(
    initialCspReportOnly ?? "",
  );

  const [saveState, setSaveState] = useState<"unsaved" | "saving" | "saved">(
    "saved",
  );

  const isAllowedToEditCSP = orgPermissions.includes("update");

  const saveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaveState("saving");
    try {
      await invokeServerAction<typeof setOrganizationContentSecurityPolicies>({
        fName: "setOrganizationContentSecurityPolicies",
        args: {
          org_id: orgId,
          policy: csp,
          policy_report_only: cspReportOnly,
        },
        getToken,
      });
      toast.success("Content Security Policy settings updated");
      refreshCsps();
      refreshUser();
    } catch (e) {
      toast.error(`Failed to update urls`, {
        description: `${e}`,
      });
    } finally {
      setSaveState("saved");
    }
  };

  return (
    <div className="mb-6 flex flex-col gap-2 rounded-md border bg-primary-50 px-4 py-3">
      {!isAllowedToEditCSP && (
        <TableEmptyState
          label='To edit CSP settings, ask your administrator to grant the
          "Manage settings" permission for this organization.'
          className="mb-4"
        />
      )}
      <form onSubmit={saveSettings}>
        <div className="mb-6">
          <div className="mb-3 text-sm">Content Security Policy</div>
          <TextArea
            value={csp}
            onChange={(e) => {
              setCSP(e.target.value);
              setSaveState("unsaved");
            }}
            placeholder="Enter Content Security Policy"
            disabled={!isAllowedToEditCSP}
            className="max-w-screen-sm"
            minRows={3}
          />
          <div className="pt-2 text-sm text-primary-600">
            Define your Content Security Policy. Leave blank to use default
            settings.{" "}
            <ExternalLink href="https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP">
              Learn more about CSP
            </ExternalLink>
          </div>
        </div>
        <div className="mb-6">
          <div className="mb-2 text-sm text-primary-800">
            Content Security Policy Report Only
          </div>
          <TextArea
            value={cspReportOnly}
            onChange={(e) => {
              setCSPReportOnly(e.target.value);
              setSaveState("unsaved");
            }}
            placeholder="Enter Content Security Policy Report Only"
            disabled={!isAllowedToEditCSP}
            className="max-w-screen-sm"
            minRows={3}
          />
          <div className="pt-2 text-sm text-primary-600">
            Define a Content Security Policy that will only report violations
            without enforcing them.
          </div>
        </div>

        <div>
          <Button
            type="submit"
            disabled={
              !isAllowedToEditCSP ||
              saveState === "saved" ||
              saveState === "saving"
            }
          >
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}

function CSPForm({
  orgId,
  orgName,
  orgPermissions,
  refreshUser,
}: {
  orgId: string;
  orgName: string;
  orgPermissions: Permission[];
  refreshUser: () => void;
}) {
  const {
    invalidate: refreshCSPs,
    data,
    isLoading,
  } = useQueryFunc<typeof getOrganizationContentSecurityPolicies>({
    fName: "getOrganizationContentSecurityPolicies",
    args: { org_name: orgName },
  });

  if (isLoading) {
    return null;
  }

  const cspData = data ?? {
    policy: "",
    policy_report_only: "",
  };

  return (
    <CSPFormContent
      orgId={orgId}
      orgName={orgName}
      orgPermissions={orgPermissions}
      initialCsp={cspData.policy}
      initialCspReportOnly={cspData.policy_report_only}
      refreshCsps={refreshCSPs}
      refreshUser={refreshUser}
    />
  );
}

export { CSPForm };
