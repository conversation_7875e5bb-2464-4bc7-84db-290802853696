import React, { type ElementRef, useEffect, useRef, useState } from "react";
import { PlainInput } from "#/ui/plain-input";
import NameTag from "./nameTag";
import { cn } from "#/utils/classnames";

interface EmailTagsInputProps {
  emails: string[];
  inputText: string;
  onInputChange: (text: string) => void;
  onAddEmail: (text: string) => void;
  onDeleteEmail: (email: string) => void;
  onValidateEmail: (text: string) => void;
  onClearValidation: () => void;
  placeholder?: string;
  className?: string;
  size?: "default" | "lg";
}

export function EmailTagsInput({
  emails,
  inputText,
  onInputChange,
  onAddEmail,
  onDeleteEmail,
  onValidateEmail,
  onClearValidation,
  placeholder = "Enter email addresses",
  className = "",
  size = "default",
}: EmailTagsInputProps) {
  const emailTagRefs = useRef<(ElementRef<typeof NameTag> | null)[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const inputWidthSpanRef = useRef<HTMLSpanElement>(null);
  const [inputWidth, setInputWidth] = useState("0");

  // Calculate input width dynamically
  useEffect(() => {
    setInputWidth(
      inputWidthSpanRef.current?.offsetWidth
        ? `${inputWidthSpanRef.current?.offsetWidth}px`
        : "0",
    );
  }, [inputText]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === "Enter" || e.key === "Tab") && inputText !== "") {
      onValidateEmail(inputText);
      onAddEmail(inputText);
      onInputChange("");
      return;
    }

    if (e.key === "Backspace" && inputText === "" && emails.length > 0) {
      emailTagRefs.current[emails.length - 1]?.focus();
      return;
    }
  };

  const handleBlur = () => {
    if (inputText) {
      onValidateEmail(inputText);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onInputChange(e.target.value);
    onClearValidation();
  };

  return (
    <div
      className={`flex flex-1 flex-col rounded-md border border-primary-200 px-2 py-1 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-700 ${className}`}
      onClick={() => {
        inputRef.current?.focus();
      }}
    >
      <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
        {emails.map((email, index) => (
          <NameTag
            key={index}
            ref={(ref) => {
              emailTagRefs.current[index] = ref;
            }}
            tabIndex={0}
            className={cn("break-all text-sm focus:bg-accent-50", {
              "text-base": size === "lg",
            })}
            name={email}
            onClick={(e) => {
              e.stopPropagation();
              emailTagRefs.current[index]?.focus();
            }}
            onDelete={() => onDeleteEmail(email)}
            onKeyDown={(e) => {
              if (e.key === "Backspace") {
                onDeleteEmail(email);
                inputRef.current?.focus();
                return;
              }
            }}
          />
        ))}
        <div
          className="flex w-0 flex-1 flex-col text-sm"
          style={{ flexBasis: inputWidth }}
        >
          <PlainInput
            className={cn("h-[26px] border-none p-0 focus:ring-0", {
              "h-9 text-base px-2": size === "lg",
            })}
            aria-label="input email"
            placeholder={placeholder}
            ref={inputRef}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            onChange={handleChange}
            value={inputText}
          />
          <span
            className="invisible h-0 self-start whitespace-pre"
            ref={inputWidthSpanRef}
          >
            {inputText}
          </span>
        </div>
      </div>
    </div>
  );
}
