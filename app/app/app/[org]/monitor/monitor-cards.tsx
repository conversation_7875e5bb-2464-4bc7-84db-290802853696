import { useFullscreenMonitorCardState as useFullscreenMonitorCardState } from "#/ui/query-parameters";
import { useCallback, useEffect, useMemo, useState } from "react";
import { type ChartTimeFrame } from "./time-controls/time-range";
import { EmptyMonitorState } from "./empty-monitor-state";
import { cn } from "#/utils/classnames";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useHotkeys } from "react-hotkeys-hook";
import { type Search } from "#/utils/search/search";

import {
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { restrictToParentElement } from "@dnd-kit/modifiers";
import StyledDndContext from "#/ui/styled-dnd-context";
import { SortableMonitorCard } from "./card/sortable-monitor-card";
import type { ChartConfig } from "./chart-config/custom-charts-schema";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { searchToExpressions } from "./search-to-expressions";

export interface CardState {
  isLoading: boolean;
  hasData: boolean;
  hasError: boolean;
  error?: Error;
}

function MonitorCards({
  orderedCharts,
  cardOrdering,
  setCardOrdering,
  chartTimeFrame,
  projectIds,
  search,
  groupBy,
  from,
  experimentIds,
  onBrush,
  isFirstLoad,
  tzUTC,
  onEdit,
}: {
  orderedCharts: (ChartConfig | undefined)[];
  cardOrdering: string[];
  setCardOrdering: (order: string[]) => void;
  chartTimeFrame: ChartTimeFrame;
  projectIds: string[];
  search?: Search | null;
  groupBy?: string;
  from: "project_logs" | "experiment";
  experimentIds: string[];
  onBrush: (v: [number, number] | null) => void;
  isFirstLoad?: boolean;
  tzUTC?: boolean;
  onEdit: (chartId: string) => void;
}) {
  const [fullscreenCardState, setFullscreenCardState] =
    useFullscreenMonitorCardState();
  const [cardStates, setCardStates] = useState<Record<string, CardState>>({});

  const customDashboard = useIsFeatureEnabled("customDashboard");

  const cardStatesArray = useMemo(() => {
    return Object.values(cardStates);
  }, [cardStates]);

  useHotkeys(
    ["Escape"],
    () => {
      setFullscreenCardState(null);
    },
    [setFullscreenCardState],
    { description: "Exit fullscreen", enabled: Boolean(fullscreenCardState) },
  );

  const additionalFilters = useMemo(() => {
    return searchToExpressions(search, from);
  }, [search, from]);

  const commonProps = useMemo(() => {
    return {
      projectIds,
      experimentIds,
      chartTimeFrame,
      filters: additionalFilters,
      groupBy,
      from,
      tzUTC,
      onBrush,
    };
  }, [
    projectIds,
    experimentIds,
    chartTimeFrame,
    additionalFilters,
    groupBy,
    from,
    tzUTC,
    onBrush,
  ]);

  // only show nodata message once
  const [shownCount, setShownCount] = useState<number>(0);
  const allLoadedNoData = useMemo(() => {
    return (
      isFirstLoad &&
      !!cardStatesArray.length &&
      cardStatesArray.every((c) => !c.isLoading && !c.hasData)
    );
  }, [cardStatesArray, isFirstLoad]);

  useEffect(() => {
    if (allLoadedNoData) {
      setShownCount((p) => p + 1);
    }
  }, [allLoadedNoData]);

  // Sensors to detect input methods (pointer and keyboard)
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  // Function to handle the end of a drag event
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      // If the item is dropped over a different item, update the order
      if (over && active.id !== over.id) {
        const oldIndex = cardOrdering.indexOf(String(active.id));
        const newIndex = cardOrdering.indexOf(String(over.id));
        const newOrder = arrayMove(cardOrdering, oldIndex, newIndex);
        setCardOrdering(newOrder);
      }
    },
    [cardOrdering, setCardOrdering],
  );

  const unavailable = useMemo(() => {
    return cardStatesArray.every(
      (c) => c.error && c.error.message.includes("too costly"),
    );
  }, [cardStatesArray]);

  const setCardState = useCallback((state: CardState, cardId: string) => {
    setCardStates((p) => {
      const copy = {
        ...p,
        [cardId]: state,
      };
      return copy;
    });
  }, []);

  return (
    <>
      {shownCount <= 1 &&
        allLoadedNoData &&
        (!projectIds.length ? (
          <TableEmptyState label="Create a project to get started with monitoring" />
        ) : (
          <EmptyMonitorState
            className="mb-2"
            rowType={from === "experiment" ? "experiment" : "logs"}
          />
        ))}
      {unavailable && (
        <TableEmptyState label="The monitor page is currently unavailable when running a self-hosted setup with Postgres-only" />
      )}
      <StyledDndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToParentElement]}
      >
        <SortableContext items={cardOrdering} strategy={rectSortingStrategy}>
          <div
            className={cn(
              "grid gap-4 justify-start grid-cols-[repeat(auto-fill,minmax(380px,1fr))]",
              {
                "flex flex-1 flex-row gap-0": fullscreenCardState,
              },
            )}
          >
            {orderedCharts.map((config, i) => {
              const chartId = cardOrdering[i];
              const isFullscreen = chartId === fullscreenCardState;
              return (
                <SortableMonitorCard
                  key={`monitor-page-${chartId}`}
                  commonProps={commonProps}
                  setCardState={setCardState}
                  isFullscreen={isFullscreen}
                  pageInFullscreen={Boolean(fullscreenCardState)}
                  dragEnabled={customDashboard}
                  chartId={chartId}
                  chartConfig={config}
                  onEdit={customDashboard ? onEdit : undefined}
                />
              );
            })}
          </div>
        </SortableContext>
      </StyledDndContext>
    </>
  );
}

export { MonitorCards };
