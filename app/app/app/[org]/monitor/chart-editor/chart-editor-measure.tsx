import { use<PERSON><PERSON>back, use<PERSON>emo, useState } from "react";
import { ChartEditorMetricSelect } from "./chart-editor-metric-select";
import { Input } from "#/ui/input";
import { Button } from "#/ui/button";
import { Code2, TextCursorInput, Trash2 } from "lucide-react";
import {
  AGGR_FUNCTIONS,
  ChartEditorAggrSelect,
} from "./chart-editor-aggr-select";
import { BtqlEditor } from "../../btql/btql-editor";
import { cn } from "#/utils/classnames";
import type { ProjectSummary } from "../../org-actions";
import { type Loc, Parser } from "@braintrust/btql/parser";
import type { ChartTimeFrame } from "../time-controls/time-range";
import type { DataObjectType } from "#/utils/btapi/btapi";

// copied from private parse method
function getSubstringFromLoc(base: string, loc: Loc) {
  const lines = base.split("\n");
  if (loc.start.line === loc.end.line) {
    return lines[loc.start.line - 1].substring(
      loc.start.col - 1,
      loc.end.col - 1,
    );
  }
  const startLine = lines[loc.start.line - 1].substring(loc.start.col - 1);
  const middleLines = lines.slice(loc.start.line, loc.end.line - 1);
  const endLine = lines[loc.end.line - 1].substring(0, loc.end.col - 1);
  return [startLine, ...middleLines, endLine].join("\n");
}

/**
 * Return parsed parts that are easy to work with
 * Return null if doesn't meet any parsing expectations
 */
function parseBtqlExpression(btql: string) {
  let parser, parsed;
  try {
    parser = new Parser(btql);
    parsed = parser.parseExpr();
  } catch {
    return null;
  }

  // only consider function expressions
  if (!parsed || parsed.op !== "function") {
    return null;
  }

  // only support aggregator functions
  const functionName = parsed.name.name.join("");
  if (!functionName || !AGGR_FUNCTIONS.includes(functionName)) {
    return null;
  }

  // for percentile we require extra argument
  const params: number[] = [];
  if (functionName === "percentile") {
    const pArg = parsed.args[1];
    if (!pArg || pArg.op !== "literal" || typeof pArg.value !== "number") {
      return null;
    }
    params.push(pArg.value);
  } else if (parsed.args.length !== 1) {
    // else no args supported
    return null;
  }

  const expr = parsed.args[0];
  if (!expr || !expr.loc) {
    return null;
  }

  // get the full expression string out of original
  const exprString = getSubstringFromLoc(btql, expr.loc);
  return { functionName, exprString, params };
}

type Measure = { btql: string; displayName?: string };

// consider valid if parse is not null
function isMeasureValid(m: Measure) {
  return parseBtqlExpression(m.btql) != null;
}

export const ChartEditorMeasure = ({
  measure,
  objectType,
  objectIds,
  projectSummary,
  chartTimeFrame,
  onChange,
  deleteMeasure,
}: {
  measure: Measure;
  objectType: DataObjectType;
  objectIds: string[];
  projectSummary?: ProjectSummary;
  chartTimeFrame: ChartTimeFrame;
  onChange: (m: Measure) => void;
  deleteMeasure?: () => void;
}) => {
  const [editedMeasure, setEditedMeasure] = useState<Measure>(measure);
  const editedMeasureIsValid = useMemo(() => {
    return isMeasureValid(editedMeasure);
  }, [editedMeasure]);

  // use visual mode when possible
  const simpleParse = useMemo(() => {
    return parseBtqlExpression(editedMeasure.btql);
  }, [editedMeasure]);

  const [advancedMode, setAdvancedMode] = useState<boolean>(false);
  const enableVisual =
    !advancedMode && editedMeasureIsValid && simpleParse != null;

  const [pInput, setPInput] = useState<string | null>(null);

  const updateEditedMeasure = useCallback(
    (m: Measure) => {
      setEditedMeasure(m); // always update local state based on user input

      // update to new measure only if it is valid
      // saves us some query grief, but some chance that parsing is wrong
      if (isMeasureValid(m)) {
        onChange(m);
      }
    },
    [onChange],
  );

  const updateMeasure = useCallback(
    ({
      aggr,
      metricExpr,
      percentile,
    }: {
      aggr?: string;
      metricExpr?: string;
      percentile?: number;
    }) => {
      const reducedAggr = aggr ?? simpleParse?.functionName;
      const reducedMetricExpr = metricExpr ?? simpleParse?.exprString;

      let newBtql = `${reducedAggr}(${reducedMetricExpr})`;
      if (reducedAggr === "percentile") {
        newBtql = `${reducedAggr}(${reducedMetricExpr}, ${percentile ?? simpleParse?.params[0] ?? 0.95})`;
      }

      const newMeasure = {
        btql: newBtql,
        displayName: editedMeasure.displayName,
      };
      updateEditedMeasure(newMeasure);
    },
    [
      simpleParse?.functionName,
      simpleParse?.exprString,
      simpleParse?.params,
      editedMeasure.displayName,
      updateEditedMeasure,
    ],
  );

  const updatePercentile = useCallback(
    (p: string) => {
      setPInput(p);

      const parsedValue = Number.isFinite(parseFloat(p)) ? parseFloat(p) : 95;
      const percentile = Math.max(0, Math.min(1, parsedValue / 100));
      updateMeasure({ percentile });
    },
    [updateMeasure],
  );

  const rowButtons = useMemo(() => {
    return (
      <div className="flex w-full gap-2">
        <Input
          className="h-7 flex-1 text-xs"
          type="text"
          value={editedMeasure.displayName}
          onChange={(v) => {
            updateEditedMeasure({
              btql: editedMeasure.btql,
              displayName: v.target.value,
            });
          }}
          placeholder="Measure name"
          title="Measure display name"
        />
        <Button
          disabled={simpleParse === null}
          Icon={enableVisual ? Code2 : TextCursorInput}
          size="xs"
          variant="ghost"
          className="flex-none text-primary-500"
          title={enableVisual ? "Switch to text edit" : "Switch to visual edit"}
          onClick={() => setAdvancedMode(enableVisual ? true : false)}
        />
        <Button
          disabled={deleteMeasure === undefined}
          Icon={Trash2}
          size="xs"
          variant="ghost"
          className="flex-none text-primary-500"
          title="Delete measure"
          onClick={deleteMeasure}
        />
      </div>
    );
  }, [
    editedMeasure.displayName,
    editedMeasure.btql,
    simpleParse,
    enableVisual,
    deleteMeasure,
    updateEditedMeasure,
  ]);

  const pInputValue = useMemo(() => {
    // prefer manually entered text if it exists
    if (pInput != null) {
      return pInput;
    }

    // use the parsed value
    if (simpleParse && Number.isFinite(simpleParse.params[0])) {
      return (100 * simpleParse.params[0]).toFixed(0);
    }

    // else undefined (placeholder of "95")
  }, [pInput, simpleParse]);

  const dataSources = useMemo(
    () => ({
      projects: projectSummary
        ? [
            {
              id: projectSummary.project_id,
              name: projectSummary.project_name,
              type: "project" as const,
            },
          ]
        : [],
      datasets: [],
      experiments: [],
      promptSessions: [],
      orgs: [],
    }),
    [projectSummary],
  );

  /** Text fallback */
  if (!enableVisual) {
    return (
      <div className="flex flex-wrap justify-between gap-2">
        {rowButtons}
        <div className="flex w-full gap-2">
          <BtqlEditor
            dataSources={dataSources}
            mode="expr"
            onValueChange={(v) =>
              updateEditedMeasure({
                btql: v,
                displayName: editedMeasure.displayName,
              })
            }
            value={editedMeasure.btql}
            autoFocus
            placeholder="measure"
            onMetaEnter={() => {}}
            className={cn(
              "flex-1 text-xs py-1 px-2 min-h-7 border rounded-md",
              { "bg-bad-50 dark:bg-bad-50/40": !editedMeasureIsValid },
            )}
          />
        </div>
      </div>
    );
  }

  /** visual editor */
  return (
    <div className="flex flex-wrap justify-between gap-2">
      {rowButtons}
      <div className="flex w-full">
        {simpleParse?.functionName === "percentile" && (
          <Input
            className="h-7 w-12 flex-none rounded-r-none pr-2 text-xs"
            type="number"
            max={100}
            min={0}
            placeholder="95"
            value={pInputValue}
            onChange={(v) => updatePercentile(v.target.value)}
            title="Percentile"
          />
        )}
        <ChartEditorAggrSelect
          value={simpleParse.functionName}
          buttonClassName={cn("flex-none", {
            "rounded-l-none border-l-0":
              simpleParse.functionName === "percentile",
          })}
          onChange={(aggr) => updateMeasure({ aggr })}
        />
        <div className="flex flex-1 overflow-hidden pl-2">
          <ChartEditorMetricSelect
            objectType={objectType}
            objectIds={objectIds}
            value={simpleParse.exprString}
            chartTimeFrame={chartTimeFrame}
            onChange={(v) => updateMeasure({ metricExpr: v ?? undefined })}
          />
        </div>
      </div>
    </div>
  );
};
