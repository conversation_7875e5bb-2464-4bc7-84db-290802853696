"use client";

import { useAnalytics } from "#/ui/use-analytics";
import type { FormatterMap } from "#/ui/field-to-column";
import { Button, buttonVariants } from "#/ui/button";
import { useCreateProjectDialog } from "#/ui/dialogs/create-project";
import { PlainInput } from "#/ui/plain-input";
import { VizQuery } from "#/ui/viz-query";
import {
  type ProjectSummary,
  type getProjectSummary,
} from "#/app/app/[org]/org-actions";
import {
  IdField,
  useMaterializedArrow,
  useMaterializedRecords,
} from "#/utils/duckdb";
import { useOrg } from "#/utils/user";
import { Field, Int32, Schema, TimestampMillisecond, Utf8 } from "apache-arrow";
import { useMemo, useRef, useState } from "react";
import { CheckOrg } from "./clientlayout";
import { useProjectRowEvents } from "./p/[project]/experiments-formatters";
import { Plus, Search, Sparkle, Trash, UsersRound } from "lucide-react";
import { useFeatureFlags } from "#/lib/feature-flags";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import { useBtql, useIsRootBtqlSnippet } from "#/utils/btql/btql";
import * as Query from "#/utils/btql/query-builder";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { Views } from "#/ui/views";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { getExperimentsLink } from "./p/[project]/experiments/[experiment]/getExperimentLink";
import Link from "next/link";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { useEntityBatchActions } from "./p/useEntityBatchActions";
import { noopChecker } from "#/utils/search/search";
import { CreatorFormatter } from "#/ui/table/formatters/creator-formatter";
import { type Permission } from "@braintrust/typespecs";
import { getOrgSettingsLink } from "./getOrgLink";
import Footer from "#/ui/landing/footer";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { BodyWrapper } from "../body-wrapper";
import { newObjectName } from "#/utils/metadata";
import { useIsSidenavDocked } from "./sidenav-state";

// This schema mirrors the output of ProjectSummary defined in
// app/app/app/[org]/org-actions.ts.
const emptyProjectSchema = new Schema([
  Field.new({ name: "project_id", type: new Utf8() }),
  Field.new({ name: "project_name", type: new Utf8() }),
  Field.new({
    name: "project_created_at",
    type: new TimestampMillisecond(),
  }),
  Field.new({ name: "project_created_by", type: new Utf8() }),
  Field.new({ name: "num_experiments", type: new Int32() }),
  Field.new({ name: "num_playgrounds", type: new Int32() }),
  Field.new({ name: "num_datasets", type: new Int32() }),
  Field.new({ name: "created_by_name", type: new Utf8() }),
  Field.new({ name: "created_by_email", type: new Utf8() }),
  Field.new({
    name: "created_by_avatar_url",
    type: new Utf8(),
  }),
]);

export interface Params {
  org: string;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const formatters: FormatterMap<{}, any> = makeFormatterMap({
  creator: {
    cell: CreatorFormatter,
  },
});

const summaryFields = {
  num_logs: "Logs (7d)",
  num_errors: "Errors (7d)",
  tokens: "Tokens (7d)",
  ttf: "TTF token (avg 7d)",
  p50_duration: "Duration (avg 7d)",
  p95_duration: "Duration (p95 7d)",
};

const lastUpdatedLazyLoadFields = {
  num_logs: "Logs (30d)",
};

export default function ClientPage({
  params,
  projectSummary: projectSummaryServer,
  orgProjectPermissions,
}: {
  params: Params;
  projectSummary: ProjectSummary[];
  orgProjectPermissions: Permission[];
}) {
  const hasProjectCreatePermission = orgProjectPermissions.includes("create");

  useAnalytics({ page: { category: "projects" } });
  const { name: orgName, id: orgId } = useOrg();

  const { data: projects, invalidate } = useQueryFunc<typeof getProjectSummary>(
    {
      fName: "getProjectSummary",
      args: { org_name: orgName },
      serverData: projectSummaryServer,
    },
  );

  const { refreshed: projectsReady, name: projectsTable } =
    useMaterializedRecords(
      "projects_summary_" + orgId,
      projects,
      emptyProjectSchema,
    );

  const {
    flags: { enableAdvancedMetrics: enableAdvancedMetricsRaw, errorCount },
  } = useFeatureFlags();

  // Sometimes we want to manually override this flag, so we alias the flag to a
  // variable here.
  const advancedMetrics = enableAdvancedMetricsRaw;

  const isRootBtqlSnippet = useIsRootBtqlSnippet();

  const {
    data: projectSummary,
    error: projectSummaryError,
    unsupported: projectSummaryUnsupported,
  } = useBtql({
    name: "Project metrics query",
    query: useMemo(
      () =>
        advancedMetrics
          ? {
              from: Query.from(
                "project_logs",
                projects.map((project) => project.project_id) ?? [],
              ),
              dimensions: [
                { alias: "project_id", expr: { btql: "project_id" } },
              ],
              measures: [
                {
                  alias: "num_logs",
                  expr: { btql: `sum(${isRootBtqlSnippet})` },
                },
                {
                  alias: "tokens",
                  expr: {
                    btql: "sum(metrics.tokens)",
                  },
                },
                {
                  alias: "ttf",
                  expr: { btql: "avg(metrics.time_to_first_token)" },
                },
                {
                  alias: "p50_duration",
                  expr: {
                    btql: `percentile(${isRootBtqlSnippet} ? metrics.end-metrics.start : null, 0.5)`,
                  },
                },
                {
                  alias: "p95_duration",
                  expr: {
                    btql: `percentile(${isRootBtqlSnippet} ? metrics.end-metrics.start : null, 0.95)`,
                  },
                },
              ].concat(
                errorCount
                  ? [
                      {
                        alias: "num_errors",
                        expr: {
                          btql: `count(error)`,
                        },
                      },
                    ]
                  : [],
              ),
              filter: {
                op: "ge",
                left: {
                  btql: "created",
                },
                right: {
                  btql: "NOW() - INTERVAL 7 DAY",
                },
              },
            }
          : null,
      [advancedMetrics, errorCount, projects, isRootBtqlSnippet],
    ),
    disableLimit: true,
    expensive: true,
    // This is a special flag that means even for un-backfilled projects, it's ok to use brainstore (if the
    // user has asked to). We're ok with that for this page, because it just means you'll see empty metrics
    // for the un-backfilled projects.
    brainstoreIncludeUnbackfilled: true,
    // It's ok not to have the very latest rows in this query, which is particularly expensive.
    brainstoreRealtime: false,
  });

  const { refreshed: projectSummaryReady, name: projectSummaryTable } =
    useMaterializedArrow(`project_summary_${orgId}`, projectSummary ?? null);

  const {
    data: lastUpdatedSummary,
    error: lastUpdatedError,
    unsupported: lastUpdatedUnsupported,
  } = useBtql({
    name: "Project log count query",
    query: useMemo(
      () =>
        advancedMetrics
          ? {
              from: Query.from(
                "project_logs",
                projects.map((project) => project.project_id) ?? [],
              ),
              dimensions: [
                { alias: "project_id", expr: { btql: "project_id" } },
              ],
              measures: [
                {
                  alias: "num_logs",
                  expr: { btql: `sum(${isRootBtqlSnippet})` },
                },
                {
                  alias: "last_updated",
                  expr: { btql: "max(created)" },
                },
              ],
              filter: {
                op: "ge",
                left: {
                  btql: "created",
                },
                right: {
                  btql: "NOW() - INTERVAL 30 DAY",
                },
              },
            }
          : null,
      [advancedMetrics, projects, isRootBtqlSnippet],
    ),
    disableLimit: true,
    expensive: true,
    // See explanation of flags above
    brainstoreIncludeUnbackfilled: true,
    brainstoreRealtime: false,
  });

  const { refreshed: lastUpdatedReady, name: lastUpdatedTable } =
    useMaterializedArrow(`last_updated_${orgId}`, lastUpdatedSummary ?? null);

  const isSidenavDocked = useIsSidenavDocked();

  const [search, setSearch] = useState("");

  const sizeConstraintsMap = useMemo(
    () => ({
      name: {
        minSize: 400,
      },
    }),
    [],
  );

  const summaryTableAvailable = projectSummaryTable && projectSummaryReady > 0;
  const lastUpdatedTableAvailable = lastUpdatedTable && lastUpdatedReady > 0;

  const viewParams: ViewParams | undefined = orgId
    ? {
        objectType: "org_project",
        objectId: orgId,
        viewType: "projects",
      }
    : undefined;
  const pageIdentifier = `organization-projects-list-${orgId}`;
  const viewProps = useViewStates({
    viewParams,
    clauseChecker: noopChecker,
    pageIdentifier,
  });

  const projectsQuery = useMemo(() => {
    const orderByClauses: string[] = [];
    if (viewProps.search.sort?.length) {
      orderByClauses.push(...viewProps.search.sort.map((s) => s.text));
    }
    orderByClauses.push("created DESC NULLS LAST");
    const orderBy = orderByClauses.join(", ");
    return (
      projectsTable &&
      `SELECT
      projects.project_id as "id",
      project_name as "name",
      num_playgrounds as "playgrounds",
      num_experiments as "experiments",
      num_datasets as "datasets",
      JSON_OBJECT('id', project_created_by,
        'name', created_by_name,
        'email', created_by_email,
        'avatar', created_by_avatar_url) as "creator",
      project_created_at AS "created",
       ${
         advancedMetrics
           ? Object.entries(lastUpdatedLazyLoadFields)
               .map(
                 ([f, a]) =>
                   (lastUpdatedTableAvailable ? `last_updated.${f}` : "null") +
                   ` as ${doubleQuote(a)}`,
               )
               .join(", ") + ","
           : ""
       }
      ${
        advancedMetrics
          ? Object.entries(summaryFields)
              .filter(([f, _]) => errorCount || f !== "num_errors")
              .map(
                ([f, a]) =>
                  (summaryTableAvailable ? `summary.${f}` : "null") +
                  ` as ${doubleQuote(a)}`,
              )
              .join(", ") + ","
          : ""
      }
      (${
        lastUpdatedTableAvailable
          ? "last_updated.last_updated"
          : "project_created_at"
      })::timestamp as "last_updated"
    FROM "${projectsTable}" projects
    ${
      summaryTableAvailable
        ? `LEFT JOIN "${projectSummaryTable}" summary ON projects.project_id = summary.project_id`
        : ""
    }
    ${
      lastUpdatedTableAvailable
        ? `LEFT JOIN "${lastUpdatedTable}" last_updated ON projects.project_id = last_updated.project_id`
        : ""
    }
    ${
      search
        ? `WHERE contains(lower(project_name), ${singleQuote(
            search.toLowerCase(),
          )})`
        : ""
    }
    ORDER BY ${orderBy}
    `
    );
  }, [
    advancedMetrics,
    errorCount,
    lastUpdatedTable,
    lastUpdatedTableAvailable,
    projectSummaryTable,
    projectsTable,
    search,
    summaryTableAvailable,
    viewProps.search.sort,
  ]);

  const columnVisibilityMap = useMemo(
    () => ({
      created: false,
    }),
    [],
  );

  const rowEvents = useProjectRowEvents({
    entity: "project",
    entityNameProp: "name",
    orgName,
  });

  const { modal: createProjectModal, open: setCreateProjectOpen } =
    useCreateProjectDialog({
      onSuccessfulCreate: ({ projectName }) => {
        invalidate();
        const projectLink = getExperimentsLink({ orgName, projectName });
        toast.success(`Project created`, {
          action: (
            <Link href={projectLink} className={buttonVariants({ size: "xs" })}>
              Go to project
            </Link>
          ),
        });
      },
      orgId,
    });

  const {
    selectedRows: rowSelectionProjects,
    setSelectedRows: setRowSelectionProjects,
    getSelectedRowsWithData: getSelectedRowsWithDataProjects,
    selectedRowsNumber: selectedRowsNumberProjects,
    deselectAllTableRows: deselectAllTableRowsProjects,
    tableRef: tableRefProjects,
  } = useTableSelection();

  const { actions: deleteProjectsActions, modals: deleteProjectsModal } =
    useEntityBatchActions({
      entityType: "project",
      onUpdate: () => {
        invalidate();
        deselectAllTableRowsProjects();
      },
      entityName: orgName,
    });

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (!projects) {
    return <CheckOrg params={{ org: orgName }}>{null}</CheckOrg>;
  }

  const error =
    lastUpdatedUnsupported || projectSummaryUnsupported
      ? undefined
      : (projectSummaryError ?? lastUpdatedError);

  return (
    <CheckOrg params={{ org: orgName }}>
      <BodyWrapper
        innerClassName="flex overflow-hidden"
        isSidenavDocked={isSidenavDocked}
      >
        <div
          className="flex flex-1 flex-col overflow-auto px-3"
          ref={scrollContainerRef}
        >
          <VizQuery
            className="pt-3"
            hasNoRowsComponent={
              !search && (
                <TableEmptyState
                  label={
                    <>
                      <h2 className="mb-2 font-medium text-primary-900">
                        Projects
                      </h2>
                      No projects in this organization yet. Get started by
                      creating a project where you can use monitor, evaluate,
                      and compare models.
                      <div className="mb-5 mt-3">
                        <Button
                          variant="inverted"
                          onClick={() => {
                            setCreateProjectOpen({
                              name: `${orgName}-first-project`,
                              entryPoint: "projectsHomePageEmptyState",
                            });
                          }}
                        >
                          Create project
                        </Button>
                      </div>
                    </>
                  }
                ></TableEmptyState>
              )
            }
            tableRef={tableRefProjects}
            viewProps={viewProps}
            query={projectsQuery}
            scrollContainerRef={scrollContainerRef}
            error={error && `${error}`}
            loadingColumns={(summaryTableAvailable
              ? []
              : Object.values(summaryFields)
            ).concat(
              lastUpdatedTableAvailable
                ? []
                : Object.values(lastUpdatedLazyLoadFields),
            )}
            initiallyVisibleColumns={columnVisibilityMap}
            // don't put this in columnVisibilityMap until we move to string-based column names for views
            neverVisibleColumns={new Set([IdField])}
            extraLeftControls={
              <>
                {hasProjectCreatePermission && (
                  <Button
                    variant="primary"
                    size="xs"
                    onClick={() => {
                      setCreateProjectOpen({
                        name: newObjectName("project"),
                        entryPoint: "projectsHomePageNewProjectButton",
                      });
                    }}
                    Icon={Plus}
                  >
                    Project
                  </Button>
                )}
                <Views
                  pageIdentifier={pageIdentifier}
                  viewParams={viewParams}
                  viewProps={viewProps}
                  defaultViewName="All projects"
                />
              </>
            }
            extraRightControls={
              <>
                <div className="relative flex flex-1">
                  <Search className="pointer-events-none absolute left-2 top-[8px] size-3 text-primary-500" />
                  <PlainInput
                    placeholder="Find projects"
                    onChange={(e) => setSearch(e.target.value)}
                    className="h-7 flex-1 border-0 bg-transparent pl-7 text-xs outline-none transition-all hover:bg-primary-100 focus:bg-primary-100"
                  />
                </div>
                <Link
                  className={buttonVariants({ size: "xs" })}
                  href={`${getOrgSettingsLink({ orgName })}/team`}
                >
                  <UsersRound className="size-3" />
                  Invite members
                </Link>
                <Link
                  className={buttonVariants({ size: "xs" })}
                  href={`${getOrgSettingsLink({ orgName })}/secrets`}
                >
                  <Sparkle className="size-3" />
                  Setup AI providers
                </Link>
              </>
            }
            tableType="list"
            signals={[projectsReady]
              .concat(summaryTableAvailable ? [projectSummaryReady] : [])
              .concat(lastUpdatedTableAvailable ? [lastUpdatedReady] : [])}
            rowEvents={rowEvents}
            formatters={formatters}
            sizeConstraintsMap={sizeConstraintsMap}
            rowSelection={rowSelectionProjects}
            setRowSelection={setRowSelectionProjects}
            toolbarSlot={
              selectedRowsNumberProjects > 0 ? (
                <>
                  <CancelSelectionButton
                    onCancelSelection={deselectAllTableRowsProjects}
                    selectedRowsNumber={selectedRowsNumberProjects}
                  />
                  <SelectionBarButton
                    onClick={() => {
                      deleteProjectsActions.deleteEntities({
                        entityIds: getSelectedRowsWithDataProjects().map(
                          (row) => row.id,
                        ),
                      });
                    }}
                    Icon={Trash}
                  />
                </>
              ) : undefined
            }
          />
          {createProjectModal}
          {deleteProjectsModal}

          <div className="grow" />
          <Footer
            className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
            inApp
            orgName={orgName}
          />
        </div>
      </BodyWrapper>
    </CheckOrg>
  );
}
