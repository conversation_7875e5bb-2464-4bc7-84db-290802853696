"use client";

import { useState } from "react";
import { FunctionsList } from "./functions-list";
import { PlainInput } from "#/ui/plain-input";
import { Search } from "lucide-react";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import type { FunctionObjectType, ViewType } from "@braintrust/typespecs";

function SearchInput({
  value,
  onChange,
  placeholder = "Find items",
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}) {
  // Internal state for immediate UI updates
  const [inputValue, setInputValue] = useState(value);
  const debouncedOnChange = useDebouncedCallback(onChange, 300);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue); // Update UI immediately
    debouncedOnChange(newValue); // Debounce the search
  };

  return (
    <div className="relative flex flex-1">
      <Search className="pointer-events-none absolute left-2 top-[8px] size-3 text-primary-500" />
      <PlainInput
        name="FunctionsSearch"
        placeholder={placeholder}
        value={inputValue}
        onChange={handleInputChange}
        className="h-7 flex-1 border-0 bg-transparent pl-7 text-xs outline-none transition-all hover:bg-primary-100 focus:bg-primary-100"
        autoComplete="off"
      />
    </div>
  );
}

// Component that combines FunctionsList with SearchInput
export function FunctionsListWithSearch({
  functionObjectType,
  scrollContainerRef,
  viewType,
  placeholder = "Find items",
}: {
  functionObjectType: FunctionObjectType;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  viewType: ViewType;
  placeholder?: string;
}) {
  const [textSearch, setTextSearch] = useState("");

  const searchInput = (
    <SearchInput
      value={textSearch}
      onChange={setTextSearch}
      placeholder={placeholder}
    />
  );

  return (
    <FunctionsList
      functionObjectType={functionObjectType}
      scrollContainerRef={scrollContainerRef}
      viewType={viewType}
      extraRightControls={searchInput}
      textSearch={textSearch.trim()}
    />
  );
}
