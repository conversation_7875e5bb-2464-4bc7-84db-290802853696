import { type RefObject, useCallback, useMemo, useRef } from "react";
import { Field, Schema, Utf8 } from "apache-arrow";
import { <PERSON><PERSON> } from "#/ui/button";
import { Info, Sparkle } from "lucide-react";
import { useAvailableModels } from "#/ui/prompts/models";
import { isEmpty } from "#/utils/object";
import { type TextEditorHandle } from "#/ui/text-editor";

import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsTrigger, TabsList } from "#/ui/tabs";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { useFeatureFlags } from "#/lib/feature-flags";
import dynamic from "next/dynamic";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { PLACEHOLDER_PY, PLACEHOLDER_TS } from "./code-editor-placeholders";
import { <PERSON><PERSON><PERSON>, TypescriptLogo } from "#/app/app/[org]/onboarding-logos";
import { InfoBanner } from "#/ui/info-banner";
import { PromptEditorSynced } from "#/app/app/[org]/prompt/[prompt]/prompt-editor-synced";
import { type FunctionTab, type ModeType } from "./types";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import {
  type PromptData,
  type FunctionObjectType,
} from "@braintrust/typespecs";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { useUpsellContext } from "#/app/playground/upsell-dialog";
import { type Extension } from "@uiw/react-codemirror";
import { cn } from "#/utils/classnames";
import { type UIFunction, type UIFunctionData } from "#/ui/prompts/schema";
import { getFunctionEditorTab } from "./use-function-editor-tabs";

const ON_RUN_FALLBACK = () => {};

const DynamicCodeEditor = dynamic(
  () => import("./code-editor").then((mod) => mod.CodeEditor),
  {
    ssr: false,
  },
);

export const emptyPromptMetaSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "search_text", type: new Utf8() }),
]);

export function FunctionEditor({
  beforeEditor,
  orgName,
  type,
  extensions,
  copilotContext,
  modeType,
  pyEditorRef,
  jsEditorRef,
  onRun,
  onTabChange: onTabChangeProp,
  datasetId,
  index = 0,
  rowData,
  hideLabel = false,
  classNames,
}: {
  beforeEditor?: React.ReactNode;
  orgName: string;
  type: FunctionObjectType;
  extensions?: Extension[];
  copilotContext?: CopilotContextBuilder;
  modeType: ModeType;
  pyEditorRef?: RefObject<TextEditorHandle<string> | null>;
  jsEditorRef?: RefObject<TextEditorHandle<string> | null>;
  onRun?: VoidFunction;
  onTabChange?: (tab: FunctionTab) => void;
  datasetId?: string;
  index?: number;
  rowData?: Record<string, unknown>;
  hideLabel?: boolean;
  classNames?: {
    codeEditorContainer?: string;
    codeEditor?: string;
    tab?: string;
    promptEditorModelDropdown?: string;
    promptEditorBody?: string;
  };
}) {
  const isReadOnly = modeType === "view_saved" || modeType === "view_unsaved";

  const {
    flags: { customFunctions },
  } = useFeatureFlags();

  const {
    allAvailableModels,
    configuredModelsByProvider,
    noConfiguredSecrets,
  } = useAvailableModels({ orgName });
  const { onUpsell } = useUpsellContext();
  const showNoConfiguredSecretsMessage = noConfiguredSecrets && !onUpsell;

  const {
    sortedSyncedPromptsAtom_ROOT,
    saveSyncedPrompt,
    updateCode_NO_SAVE__ROOT,
    updateScorerTab__ROOT,
  } = useSyncedPrompts();

  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[index]),
      [sortedSyncedPromptsAtom_ROOT, index],
    ),
  );
  const tab = getFunctionEditorTab(prompt.function_data);
  const promptId = prompt.id;

  const onSaveCode = useCallback(
    async () => await saveSyncedPrompt(promptId),
    [promptId, saveSyncedPrompt],
  );
  const onChangeCode = useCallback(
    (code: string) => {
      updateCode_NO_SAVE__ROOT({ id: promptId, code });
    },
    [promptId, updateCode_NO_SAVE__ROOT],
  );

  const savedEditorContentRef = useRef<{
    [key in FunctionTab]:
      | {
          prompt_data?: PromptData;
          function_data: UIFunctionData;
        }
      | undefined;
  }>({
    llm: undefined,
    ts: undefined,
    py: undefined,
  });
  const onTabChange = useCallback(
    (newTab: FunctionTab) => {
      savedEditorContentRef.current[tab] = {
        prompt_data: prompt.prompt_data,
        function_data: prompt.function_data,
      };
      updateScorerTab__ROOT({
        id: promptId,
        tab: newTab,
        savedContent: savedEditorContentRef.current[newTab],
      });
      onTabChangeProp?.(newTab);
    },
    [
      tab,
      prompt.prompt_data,
      prompt.function_data,
      updateScorerTab__ROOT,
      promptId,
      onTabChangeProp,
    ],
  );

  const showTabs =
    modeType === "create" &&
    prompt.function_data.type !== "global" &&
    type === "scorer";

  const bundledPreview =
    prompt.function_data?.type === "code" &&
    prompt.function_data?.data?.type === "bundle" &&
    prompt.function_data?.data?.preview;
  const tooComplex = isTooComplex(prompt);
  const showTooComplex = tooComplex && !bundledPreview;

  const codeBetaMessage = (
    <div className="mt-auto px-1 pt-2 text-xs text-primary-500">
      Code runs in a sandboxed environment with restrictions on imports and
      filesystem access
    </div>
  );
  const { code_execution } = useAPIVersion();
  const noCodeExecution = <>Upgrade your stack and enable code execution</>;
  const codeDisabled = showTooComplex || !customFunctions || !code_execution;

  return (
    <>
      {!showTooComplex && type !== "agent" && (
        <Tabs
          className="flex min-h-0 flex-auto flex-col gap-4"
          value={tab}
          onValueChange={(t) => {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            onTabChange(t as FunctionTab);
          }}
        >
          {showTabs && (
            <div>
              <div className="mb-2 text-xs font-medium">Type</div>
              <TabsList className="mb-2 inline-flex h-auto gap-1 rounded-md border bg-background p-1">
                <TabsTrigger value="llm" asChild>
                  <Button
                    size="xs"
                    disabled={showTooComplex}
                    variant={tab === "llm" ? "primary" : "ghost"}
                    className="gap-2 rounded text-xs data-[state=active]:bg-primary-200 data-[state=active]:shadow-none"
                  >
                    <Sparkle className="size-3 text-primary-500" />
                    LLM judge
                  </Button>
                </TabsTrigger>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <TabsTrigger value="ts" asChild>
                        <Button
                          size="xs"
                          disabled={codeDisabled}
                          variant={tab === "ts" ? "primary" : "ghost"}
                          className="gap-2 rounded text-xs data-[state=active]:bg-primary-200 data-[state=active]:shadow-none"
                        >
                          <TypescriptLogo className="size-3 text-primary-500" />
                          TypeScript
                        </Button>
                      </TabsTrigger>
                    </div>
                  </TooltipTrigger>
                  {!code_execution && (
                    <TooltipContent>{noCodeExecution}</TooltipContent>
                  )}
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <TabsTrigger value="py" asChild>
                        <Button
                          size="xs"
                          disabled={codeDisabled}
                          variant={tab === "py" ? "primary" : "ghost"}
                          className="gap-2 rounded text-xs data-[state=active]:bg-primary-200 data-[state=active]:shadow-none"
                        >
                          <PythonLogo className="size-3 text-primary-500" />
                          Python
                        </Button>
                      </TabsTrigger>
                    </div>
                  </TooltipTrigger>
                  {!code_execution && (
                    <TooltipContent>{noCodeExecution}</TooltipContent>
                  )}
                </Tooltip>
              </TabsList>
            </div>
          )}
          <TabsContent
            value="llm"
            className={cn(
              "mt-0 flex min-h-0 flex-col",
              tab !== "llm" && "hidden",
              classNames?.tab,
            )}
          >
            {!hideLabel && (
              <div className="mb-2 text-xs font-medium">Prompt</div>
            )}
            <PromptEditorSynced
              isReadOnly={isReadOnly}
              orgName={orgName}
              modelOptionsByProvider={configuredModelsByProvider}
              allAvailableModels={allAvailableModels}
              extensions={extensions}
              promptData={prompt.prompt_data}
              onRun={onRun ?? ON_RUN_FALLBACK}
              copilotContext={copilotContext}
              promptId={prompt.id}
              type={type}
              datasetId={datasetId}
              rowData={rowData}
              enableHotkeys
              classNames={{
                modelDropdown: classNames?.promptEditorModelDropdown,
                body: classNames?.promptEditorBody,
              }}
              beforeEditor={beforeEditor}
              showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
            />
          </TabsContent>
          <TabsContent
            value="ts"
            className={cn(
              "mt-0 flex min-h-0 flex-col",
              tab !== "ts" && "hidden",
              classNames?.tab,
            )}
          >
            {!hideLabel && (
              <div className="mb-2 flex items-center gap-1 text-xs font-medium">
                {!showTabs && <TypescriptLogo className="size-3" />}
                TypeScript
              </div>
            )}
            {beforeEditor}
            {customFunctions ? (
              <div
                className={cn(
                  "flex flex-1 flex-col rounded-md border p-2 bg-primary-100",
                  classNames?.codeEditorContainer,
                )}
              >
                <DynamicCodeEditor
                  savedCode={
                    bundledPreview
                      ? bundledPreview
                      : prompt.function_data.type === "code" &&
                          prompt.function_data.data.type === "inline" &&
                          prompt.function_data.data.runtime_context.runtime ===
                            "node"
                        ? prompt.function_data.data.code
                        : PLACEHOLDER_TS
                  }
                  readOnly={!!bundledPreview || isReadOnly}
                  editorRef={jsEditorRef}
                  language="ts"
                  copilotContext={copilotContext}
                  className={classNames?.codeEditor}
                  onSave={onSaveCode}
                  onChange={onChangeCode}
                />
                {codeBetaMessage}
              </div>
            ) : (
              noCodeExecution
            )}
          </TabsContent>
          <TabsContent
            value="py"
            className={cn(
              "mt-0 flex min-h-0 flex-col",
              tab !== "py" && "hidden",
              classNames?.tab,
            )}
          >
            {!hideLabel && (
              <div className="mb-2 flex items-center gap-1 text-xs font-medium">
                {!showTabs && <PythonLogo className="size-3" />}
                Python
              </div>
            )}
            {beforeEditor}
            {customFunctions ? (
              <div className="flex flex-1 flex-col rounded-md border bg-primary-100 p-2">
                <DynamicCodeEditor
                  savedCode={
                    bundledPreview
                      ? bundledPreview
                      : prompt.function_data.type === "code" &&
                          prompt.function_data.data.type === "inline" &&
                          prompt.function_data.data.runtime_context.runtime ===
                            "python"
                        ? prompt.function_data.data.code
                        : PLACEHOLDER_PY
                  }
                  readOnly={!!bundledPreview || isReadOnly}
                  editorRef={pyEditorRef}
                  language="py"
                  copilotContext={copilotContext}
                  className={classNames?.codeEditor}
                  onSave={onSaveCode}
                  onChange={onChangeCode}
                />
                {codeBetaMessage}
              </div>
            ) : (
              noCodeExecution
            )}
          </TabsContent>
        </Tabs>
      )}
      {showTooComplex && (
        <div className={cn(classNames?.tab)}>
          <div className="flex items-center rounded-md border border-accent-100 bg-accent-50 p-3 text-sm text-primary-700">
            <Info className="mr-1.5 inline-block size-3 flex-none" />
            This function is too complex to edit in the UI. You can run the
            function here, or edit it via the API.
          </div>
        </div>
      )}
      {type === "agent" && (
        <InfoBanner>
          Agents are currently only visible in playgrounds
        </InfoBanner>
      )}
    </>
  );
}

export function isTooComplex(func: Partial<UIFunction>) {
  return (
    (func.function_data?.type === "global" &&
      isEmpty(func.prompt_data?.prompt)) ||
    (func.function_data?.type === "code" &&
      func.function_data?.data?.type === "bundle")
  );
}
