import { useOrg } from "#/utils/user";
import { type RefObject, useCallback, useEffect, useMemo, useRef } from "react";
import { But<PERSON> } from "#/ui/button";
import { Play } from "lucide-react";
import { type PromptData, type UIFunction } from "#/ui/prompts/schema";
import { isEmpty } from "#/utils/object";
import {
  type DataEditorCopilotContextFn,
  DataTextEditor,
} from "#/ui/data-text-editor";
import { NoAISecretsEmptyState } from "#/ui/prompts/empty";

import { type SetValue, useEntityStorage } from "#/lib/clientDataStorage";
import { Spinner } from "#/ui/icons/spinner";
import { type CopilotContextFormat } from "@braintrust/local/copilot";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import {
  CompletionBlock,
  type CompletionBlockHandle,
} from "#/app/app/[org]/prompt/[prompt]/completion-block";
import { type Message } from "@braintrust/typespecs";
import isEqual from "lodash.isequal";
import { usePromptVariables } from "./use-prompt-variables";

export const FunctionRunSection = ({
  showNoConfiguredSecretsMessage,
  copilotContext,
  dataEditorValue,
  setDataEditorValue,
  promptData,
  initialFunction,
  variableData,
  runPrompt,
  ref,
  onAddMessageToPrompt,
  shouldInitializeMissingPromptVariables = false,
}: {
  showNoConfiguredSecretsMessage: boolean;
  copilotContext?: CopilotContextBuilder;
  dataEditorValue: Record<string, unknown>;
  setDataEditorValue: SetValue<Record<string, unknown>>;
  promptData?: PromptData;
  initialFunction: UIFunction | null;
  variableData?: Record<string, unknown>;
  runPrompt: VoidFunction;
  ref: RefObject<CompletionBlockHandle | null>;
  onAddMessageToPrompt?: (message: Message) => void;
  shouldInitializeMissingPromptVariables?: boolean;
}) => {
  const org = useOrg();
  const proxyUrl = org.proxy_url;

  const touchedEditorRef = useRef(false);

  const functionSchema = useMemo(
    () =>
      initialFunction?.function_schema?.parameters &&
      typeof initialFunction?.function_schema?.parameters === "object" &&
      "properties" in initialFunction?.function_schema?.parameters
        ? Object.keys(
            initialFunction?.function_schema?.parameters?.properties ?? {},
          )
        : undefined,
    [initialFunction?.function_schema?.parameters],
  );

  const { isMissingPromptVariables, addPromptVariables } = usePromptVariables({
    promptContent: promptData?.prompt,
    functionSchema,
    runData: dataEditorValue,
    setRunData: setDataEditorValue,
    touchedEditorRef,
  });

  const didInitializeVariablesRef = useRef(false);
  useEffect(() => {
    if (
      shouldInitializeMissingPromptVariables &&
      isMissingPromptVariables &&
      !didInitializeVariablesRef.current
    ) {
      didInitializeVariablesRef.current = true;
      addPromptVariables();
    }
  }, [
    isMissingPromptVariables,
    addPromptVariables,
    shouldInitializeMissingPromptVariables,
  ]);

  useEffect(() => {
    if (!isEmpty(variableData)) {
      setDataEditorValue((prev) => {
        // set data editor to the provided variable data if the previous value is empty or the default value
        if (
          isEmpty(prev) ||
          Object.keys(prev).length === 0 ||
          isEqual(prev, DEFAULT_SCORER_DATA_EDITOR_VALUE) ||
          isEqual(prev, DEFAULT_PROMPT_DATA_EDITOR_VALUE)
        ) {
          return variableData;
        }
        return prev;
      });
    }
  }, [variableData, setDataEditorValue]);

  const makeDataAutocompleteContext: DataEditorCopilotContextFn = useCallback(
    (fmt: CopilotContextFormat) => {
      return copilotContext?.makeCopilotContext({
        type: "row",
        field: "input",
        objectType: null,
        objectName: null,
        row: dataEditorValue ?? {},
        fmt,
      });
    },
    [copilotContext, dataEditorValue],
  );
  const [isCodeExecutionWarmed] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.id ?? "",
    key: "codeExecutionWarmed",
  });
  // TODO: clarify intent with code execution warming/api url
  const showCodeExecutionWarming = !isCodeExecutionWarmed && !org.api_url;

  return (
    <>
      {showNoConfiguredSecretsMessage ? (
        <NoAISecretsEmptyState orgName={org.name} />
      ) : showCodeExecutionWarming ? (
        <div className="flex items-center gap-1 rounded-md bg-primary-50 p-3 text-xs text-primary-800">
          <Spinner className="size-3" />
          Code execution is initializing. This could take up to 60 seconds.
        </div>
      ) : (
        <>
          <DataTextEditor
            value={dataEditorValue}
            allowedRenderOptions={["yaml", "json"]}
            onChange={(v) => {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              setDataEditorValue(v as Record<string, unknown>);
            }}
            onBlur={() => {
              touchedEditorRef.current = true;
            }}
            formatOnBlur
            makeCopilotContext={makeDataAutocompleteContext}
          />

          <div className="mb-3 mt-2 flex justify-start gap-2">
            <Button
              size="xs"
              onClick={(e) => {
                e.preventDefault();
                runPrompt();
              }}
              Icon={Play}
            >
              Test
            </Button>
            {isMissingPromptVariables && (
              <Button
                size="xs"
                onClick={(e) => {
                  e.preventDefault();
                  addPromptVariables();
                }}
                className="text-primary-500"
                variant="ghost"
              >
                Insert variables from messages
              </Button>
            )}
          </div>
          <CompletionBlock
            ref={ref}
            proxyUrl={proxyUrl}
            onAddMessageToPrompt={onAddMessageToPrompt}
          />
        </>
      )}
    </>
  );
};

export const DEFAULT_SCORER_DATA_EDITOR_VALUE = {
  output: "",
  expected: "",
};

export const DEFAULT_PROMPT_DATA_EDITOR_VALUE = {
  input: "",
};
