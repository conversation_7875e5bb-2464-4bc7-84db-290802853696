import { Input } from "#/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { useCallback, useMemo, useState } from "react";
import {
  CircleCheck,
  Copy,
  Layers2,
  Lock,
  PenLine,
  Pin,
  RotateCcw,
  Search,
  Settings2,
  Trash,
} from "lucide-react";
import { searchMatch } from "#/utils/string-search";
import { type PropsWithChildren } from "react";
import { Button } from "#/ui/button";
import { type View } from "#/utils/view/use-view-generic";
import { BasicTooltip } from "#/ui/tooltip";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { cn } from "#/utils/classnames";

interface ViewDropdownProps {
  views: View[] | undefined;
  isLoadingViews: boolean;
  loadView: (view: View | null) => void;
  selectedView?: View;
  viewNameOverride?: string;
  setCreateViewDialogOpen: (open: boolean) => void;
  setRenameViewDialogOpen: (open: boolean) => void;
  setDeleteViewDialogOpen: (open: boolean) => void;
  pageIdentifier: string;
  descriptionText?: string;
  saveView: () => void;
  resetView: (view: View | null) => void;
  isDirty?: boolean;
}

export function ViewDropdown({
  views,
  selectedView,
  viewNameOverride,
  loadView,
  isLoadingViews,
  setCreateViewDialogOpen,
  setRenameViewDialogOpen,
  setDeleteViewDialogOpen,
  pageIdentifier,
  descriptionText,
  saveView,
  resetView,
  isDirty = false,
}: PropsWithChildren<ViewDropdownProps>) {
  const [query, setQuery] = useState("");

  const filteredViews = useMemo(() => {
    return views?.filter((v) => searchMatch({ query, text: v.name ?? "" }));
  }, [views, query]);

  const isEditable = Boolean(selectedView?.id && !selectedView?.builtin);

  const [defaultViewName, setDefaultViewName] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "defaultView",
  });

  const setCurrentViewAsDefault = useCallback(() => {
    setDefaultViewName(selectedView?.name ?? null);
  }, [selectedView, setDefaultViewName]);

  return (
    <div className="flex">
      <DropdownMenu
        onOpenChange={(open) => {
          if (!open) {
            setQuery("");
          }
        }}
      >
        <DropdownMenuTrigger asChild>
          <Button
            size="xs"
            disabled={!selectedView || isLoadingViews}
            className={cn({
              "border-accent-200 hover:bg-accent-50 dark:border-accent-100":
                isDirty,
            })}
            isDropdown
          >
            <Layers2
              className={cn("size-3", {
                "text-accent-500": isDirty,
              })}
            />
            <span className="max-w-40 truncate">
              {viewNameOverride ?? selectedView?.name ?? "Views"}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          autoFocus
          align="start"
          className="min-w-48 max-w-80"
        >
          {isDirty && (
            <>
              <DropdownMenuGroup>
                <DropdownMenuLabel>
                  This view has unsaved changes
                </DropdownMenuLabel>
                {isEditable && (
                  <DropdownMenuItem
                    onSelect={saveView}
                    className="font-medium text-accent-700"
                  >
                    <CircleCheck className="size-3 flex-none" />
                    Save view
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  className="font-medium text-accent-700"
                  onSelect={() => {
                    setCreateViewDialogOpen(true);
                  }}
                >
                  <Copy className="size-3 flex-none" />
                  Save view as...
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </>
          )}
          <DropdownMenuGroup>
            {!isDirty && (
              <DropdownMenuItem
                onSelect={() => {
                  setCreateViewDialogOpen(true);
                }}
              >
                <Copy className="size-3" />
                Duplicate view
              </DropdownMenuItem>
            )}
            {isDirty && (
              <DropdownMenuItem
                onSelect={() => resetView(selectedView ?? null)}
              >
                <RotateCcw className="size-3" />
                Reset view
              </DropdownMenuItem>
            )}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Settings2 className="size-3" />
                Manage view
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {isEditable && (
                  <DropdownMenuItem
                    onSelect={() => {
                      setRenameViewDialogOpen(true);
                    }}
                  >
                    <PenLine className="size-3" />
                    Rename view
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onSelect={() => {
                    setCurrentViewAsDefault();
                  }}
                >
                  <Pin className="size-3" />
                  Set as default view
                </DropdownMenuItem>
                {isEditable && (
                  <DropdownMenuItem
                    onSelect={() => {
                      setDeleteViewDialogOpen(true);
                    }}
                  >
                    <Trash className="size-3" />
                    Delete view
                  </DropdownMenuItem>
                )}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Switch view</DropdownMenuLabel>
          <label
            className="mb-1 flex h-8 items-center rounded-sm bg-primary-100 px-2"
            onKeyDown={(e) => e.stopPropagation()}
          >
            <Search className="size-3 text-primary-500" />
            <Input
              placeholder="Find a view"
              autoFocus
              className="h-8 border-0 bg-transparent pl-2 pr-0 text-xs outline-none ring-0 focus-visible:border-0 focus-visible:ring-0"
              onChange={(e) => {
                setQuery(e.target.value);
              }}
            />
          </label>
          {filteredViews?.length === 0 && (
            <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
              No views found
            </DropdownMenuLabel>
          )}
          {filteredViews && filteredViews.length > 0 && (
            <DropdownMenuGroup className="overflow-none max-h-[calc(100vh-300px)]">
              {filteredViews.map((v) => (
                <DropdownMenuCheckboxItem
                  key={v.id}
                  checked={v.id === selectedView?.id}
                  onSelect={() => {
                    loadView(v.id ? v : null);
                    setQuery("");
                  }}
                  className="flex items-center gap-2"
                >
                  <span className="flex-1">{v.name}</span>
                  {v.builtin && (
                    <BasicTooltip tooltipContent="This view is read-only and cannot be modified">
                      <Lock className="size-3 flex-none text-primary-400" />
                    </BasicTooltip>
                  )}
                  {defaultViewName === v.name && (
                    <BasicTooltip tooltipContent="This is your default view">
                      <Pin className="size-3 flex-none text-primary-400" />
                    </BasicTooltip>
                  )}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuGroup>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
