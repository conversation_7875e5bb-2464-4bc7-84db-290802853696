import { useCallback, useEffect, useMemo, useState } from "react";
import { Form, FormField, FormItem, FormLabel, FormMessage } from "#/ui/form";
import { type FieldValues, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input, inputClassName } from "#/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "#/ui/tabs";
import { Combobox } from "#/ui/combobox/combobox";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import { type ClauseChecker } from "#/utils/search/search";
import { z } from "zod";
import { isAlphaNumeric } from "#/utils/string";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";
import { useBtqlAutocompleteProjectDataSource } from "#/app/app/[org]/btql/use-btql-autocomplete-data-sources";

const customColumnSchema = z.object({
  name: z.string(),
  expr: z.string(),
});

const arrayIndexRegex = /\.?\[(\d+)\]/g;

export type CustomColumnDef = z.infer<typeof customColumnSchema>;

export const CustomColumnForm = ({
  clauseChecker,
  customColumnOptions,
  createCustomColumn,
  updateCustomColumn,
  initialValues,
  existingColumns,
  onFinish,
}: {
  clauseChecker: ClauseChecker | null;
  customColumnOptions?: Record<string, string[][]>;
  createCustomColumn?: (col: CustomColumnDef) => Promise<void>;
  updateCustomColumn?: ({
    columnId,
    columnData,
  }: {
    columnId: string;
    columnData: CustomColumnDef;
  }) => Promise<void>;
  initialValues?: CustomColumnDef & { id: string };
  existingColumns: Map<string, boolean>;
  onFinish: VoidFunction;
}) => {
  const [error, setError] = useState("");
  const [isBTQLValid, setBTQLValid] = useState(false);

  const { isValidBTQL } = useFilterSortBarSearch({
    clauseChecker,
  });

  const form = useForm<CustomColumnDef>({
    resolver: zodResolver(customColumnSchema),
    defaultValues: initialValues
      ? initialValues
      : {
          name: "",
          expr: "",
        },
  });

  const onSubmit = useCallback(
    (data: FieldValues) => {
      setError("");
      if (!data.expr || !isBTQLValid) {
        setError("Enter a valid BTQL expression");
        return;
      }
      const formData = customColumnSchema.parse(data);
      if (!initialValues && existingColumns.get(formData.name)) {
        setError("A column with this name already exists. Use a unique name");
        return;
      }

      if (initialValues) {
        updateCustomColumn?.({
          columnId: initialValues.id,
          columnData: { name: formData.name, expr: formData.expr },
        });
      } else {
        createCustomColumn?.({ name: formData.name, expr: formData.expr });
      }
      onFinish();
      form.reset();
    },
    [
      createCustomColumn,
      existingColumns,
      initialValues,
      isBTQLValid,
      onFinish,
      updateCustomColumn,
      form,
    ],
  );

  const expr = form.watch("expr");

  useEffect(() => {
    if (!expr) {
      setBTQLValid(false);
      return;
    }
    const check = async () => {
      const result = await isValidBTQL(expr);
      setBTQLValid(result.valid);
    };
    check();
  }, [expr, isValidBTQL]);

  const comboboxOptions = Object.entries(customColumnOptions || {})
    .map(([origin, fields]) => fieldsToOptions(origin, fields))
    .filter((opt) => opt.options.length);

  const selectedOpt = useMemo(() => {
    const allOpts = comboboxOptions.flatMap((opts) =>
      opts.options.map((opt) => opt.value),
    );
    return !!(
      allOpts.length &&
      (!initialValues || allOpts.indexOf(initialValues.expr) !== -1)
    );
  }, [comboboxOptions, initialValues]);

  const dataSources = useBtqlAutocompleteProjectDataSource();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="flex-1 space-y-1.5">
              <FormLabel className="text-primary-600">Name</FormLabel>
              <Input
                className="h-9 pb-2"
                type="text"
                placeholder="Enter column name"
                {...form.register(field.name)}
                required
              />
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="my-2">
          <Tabs defaultValue={selectedOpt ? "basic" : "btql"}>
            <TabsList className="mb-0 flex w-full items-end justify-start gap-4 rounded-none border-b border-primary-100 bg-transparent pb-0 dark:bg-transparent">
              <TabsTrigger
                value="basic"
                className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
              >
                Basic
              </TabsTrigger>
              <TabsTrigger
                value="btql"
                className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
              >
                BTQL
              </TabsTrigger>
            </TabsList>
            <TabsContent
              value="basic"
              className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
            >
              <Combobox<{
                label: string;
                value: string;
              }>
                contentWidth={462}
                align="start"
                variant="button"
                buttonClassName="px-2 max-w-[462px]"
                searchPlaceholder="Find field"
                noResultsLabel="No fields found"
                placeholderLabel="Select a field"
                placeholderClassName={cn("flex-1 text-left px-0 truncate", {
                  "text-primary-500": !expr,
                })}
                selectedValue={expr}
                onChange={(value) => value && form.setValue("expr", value)}
                options={comboboxOptions}
                modal
              />
            </TabsContent>
            <TabsContent
              value="btql"
              className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
            >
              <BtqlEditor
                dataSources={dataSources}
                mode="expr"
                onValueChange={(v) => form.setValue("expr", v)}
                value={expr}
                placeholder="Enter BTQL"
                onMetaEnter={() => {
                  onSubmit(form.getValues());
                }}
                className={cn(inputClassName, "h-auto", {
                  "border-bad-200 bg-bad-50":
                    expr && expr.length > 0 && !isBTQLValid,
                })}
              />
            </TabsContent>
          </Tabs>
        </div>
        {error && <div className="py-1 text-xs text-bad-600">{error}</div>}
        <div className="flex justify-end pt-2">
          <Button size="sm" variant="primary" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
};

function fieldsToOptions(origin: string, fieldPaths: string[][] | undefined) {
  const result = new Map<string, string>();
  fieldPaths?.forEach((fieldPath) => {
    if (fieldPath.length) {
      for (let i = 1; i <= fieldPath.length; i++) {
        const prefix = fieldPath.slice(0, i);
        const parsedPrefix = prefix.map((p) =>
          arrayIndexRegex.test(p) || isAlphaNumeric(p)
            ? p
            : `"${p.replace(/"/g, '/"')}"`,
        );
        const label = prefix.join(".").replace(arrayIndexRegex, "[$1]");
        const value = [origin, ...parsedPrefix]
          .join(".")
          .replace(arrayIndexRegex, "[$1]");
        if (!result.get(label)) {
          result.set(label, value);
        }
      }
    }
  });

  return {
    label: origin,
    options: Array.from(result).map(([key, val]) => ({
      label: key,
      value: val,
    })),
  };
}
