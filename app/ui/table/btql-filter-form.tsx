import { But<PERSON> } from "#/ui/button";
import { Check } from "lucide-react";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import {
  type ClauseType,
  type ClauseChecker,
  type Search,
  type ClauseSpec,
} from "#/utils/search/search";
import { type ClientOptions } from "openai";
import { type AISearchResult } from "@braintrust/local";
import { inputClassName } from "#/ui/input";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";
import { useBtqlAutocompleteProjectDataSource } from "#/app/app/[org]/btql/use-btql-autocomplete-data-sources";

export function BTQLFilterForm({
  clauseChe<PERSON>,
  setSearch,
  runAISearch,
  clauseToReplace,
  setOpen,
  value,
  setValue,
  experimentId,
}: {
  clauseChecker: ClauseChecker | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  runAISearch?: (
    openAIOpts: ClientOptions,
    query: string,
  ) => Promise<AISearchResult>;
  clauseToReplace?: ClauseSpec<ClauseType>;
  setOpen: (open: boolean) => void;
  value: string;
  setValue: (value: string) => void;
  experimentId?: string;
}) {
  const { applySearch, loading, isValidBTQL } = useFilterSortBarSearch({
    runAISearch,
    clauseChecker,
    setSearch,
  });

  const supportsAISearch = Boolean(runAISearch);

  const [isBTQLValid, setBTQLValid] = useState(false);

  useEffect(() => {
    if (!value) {
      setBTQLValid(false);
      return;
    }
    const check = async () => {
      const result = await isValidBTQL(value);
      setBTQLValid(result.valid);
    };
    check();
  }, [value, isValidBTQL]);

  const disabled = useMemo(
    () => !value || (!supportsAISearch && !isBTQLValid),
    [value, isBTQLValid, supportsAISearch],
  );

  const onSubmitBTQL = useCallback(async () => {
    if (disabled) return;
    await applySearch(value, clauseToReplace, {
      originType: "btql",
      label: value,
      ...(experimentId
        ? {
            comparison: {
              experimentId,
            },
          }
        : {}),
    });
    setOpen(false);
  }, [applySearch, value, setOpen, clauseToReplace, experimentId, disabled]);

  const dataSources = useBtqlAutocompleteProjectDataSource();

  return (
    <>
      <div className="p-4">
        <BtqlEditor
          dataSources={dataSources}
          mode="expr"
          onValueChange={setValue}
          value={value}
          autoFocus
          placeholder={
            supportsAISearch
              ? "Enter BTQL or natural language query"
              : "Enter BTQL"
          }
          onMetaEnter={onSubmitBTQL}
          className={cn(inputClassName, "h-auto")}
        />
      </div>
      <div className="flex flex-none items-center gap-3 border-t border-primary-100 px-4 py-2">
        <Button
          size="xs"
          variant="primary"
          isLoading={loading}
          disabled={disabled}
          onClick={onSubmitBTQL}
          Icon={isBTQLValid ? Check : undefined}
        >
          {isBTQLValid ? (clauseToReplace ? "Update" : "Apply") : "Submit"}
        </Button>
        <div className="w-full text-balance text-right text-xs text-primary-500">
          {supportsAISearch ? "Enter natural language to convert to " : ""}
          <Link
            className="font-medium text-accent-600"
            href="/docs/reference/btql"
            target="_blank"
          >
            BTQL
          </Link>
        </div>
      </div>
    </>
  );
}
