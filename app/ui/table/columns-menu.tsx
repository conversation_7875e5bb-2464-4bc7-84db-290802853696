import { Combobox } from "#/ui/combobox/combobox";
import {
  AppWindowMac,
  Columns2,
  LayoutList,
  <PERSON>cate,
  PencilLine,
  PercentIcon,
  Plus,
  Trash2,
} from "lucide-react";
import { type FormatterMap } from "#/ui/field-to-column";
import { type JSX, useEffect, useMemo, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { type ScoreSummary, type NestedField } from "@braintrust/local/query";
import { type ClauseChecker } from "#/utils/search/search";
import { cn } from "#/utils/classnames";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { type CustomColumnDef, CustomColumnForm } from "./custom-column-form";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { Button } from "#/ui/button";
import { type ScoreSummaryExperiment } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type Column, type VisibilityState } from "@tanstack/react-table";
import { HeaderAliases } from "@braintrust/local/api-schema";
import { type DragEndEvent } from "@dnd-kit/core";
import { type AdditionalActions } from "#/ui/combobox/combobox-command";
import Link from "next/link";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";

export function ColumnsMenu<TsData, TsValue>({
  customColumnDialogVariant,
  setCustomColumnDialogVariant,
  columns,
  formatters,
  allColumnsHidden,
  baseSummary,
  onSelectOrderByRegression,
  customColumns,
  customColumnOptions,
  columnOrderOverride,
  clauseChecker,
  initialFormValues,
  createCustomColumn,
  updateCustomColumn,
  deleteCustomColumn,
  onCloseCustomDialog,
  gridLayout,
  columnVisibility,
  handleDragEnd,
  customColumnsEnabled,
  enableScrollToColumn,
}: {
  customColumnDialogVariant?: CustomColumnDialogVariant;
  setCustomColumnDialogVariant: (variant?: CustomColumnDialogVariant) => void;
  columns: Column<TsData, unknown>[];
  formatters?: FormatterMap<TsData, TsValue>;
  allColumnsHidden: boolean;
  baseSummary:
    | {
        scores: ScoreSummary;
        experiment: ScoreSummaryExperiment;
      }
    | undefined;
  onSelectOrderByRegression: VoidFunction;
  customColumns?: CustomColumnDefinition[];
  customColumnOptions?: Record<string, NestedField[]>;
  columnOrderOverride: string | null;
  clauseChecker: ClauseChecker | null;
  initialFormValues?: CustomColumnDef & { id: string };
  createCustomColumn?: (col: CustomColumnDef) => Promise<void>;
  updateCustomColumn?: ({
    columnId,
    columnData,
  }: {
    columnId: string;
    columnData: CustomColumnDef;
  }) => Promise<void>;
  deleteCustomColumn?: (col: { id: string; name: string }) => void;
  onCloseCustomDialog: VoidFunction;
  gridLayout: boolean;
  columnVisibility: VisibilityState;
  handleDragEnd: (event: DragEndEvent) => void;
  customColumnsEnabled?: boolean;
  enableScrollToColumn?: boolean;
}) {
  useEffect(() => {
    if (initialFormValues) setCustomColumnDialogVariant("create");
  }, [initialFormValues, setCustomColumnDialogVariant]);

  const existingColumns = useMemo(
    () => new Map(columns.map((c) => [c.id, true])),
    [columns],
  );

  const bottomActions: AdditionalActions = [
    {
      label: "Select all",
      onSelect: () =>
        columns.forEach((c) => {
          c.toggleVisibility(true);
        }),
      disabled: columns.every(
        (c) => c.columnDef.meta?.pinnedColumnIndex != null || c.getIsVisible(),
      ),
    },
    {
      label: "Deselect all",
      onSelect: () => {
        columns.forEach((c) => {
          c.toggleVisibility(false);
        });
      },
      disabled: allColumnsHidden,
    },
    ...(customColumnsEnabled !== undefined
      ? [
          {
            label: (
              <Tooltip>
                <TooltipTrigger asChild disabled={!customColumnsEnabled}>
                  <div className="flex w-full items-center gap-2">
                    <Plus className="size-3" />
                    Add custom {gridLayout ? "field" : "column"}
                  </div>
                </TooltipTrigger>
                <TooltipPortal>
                  {!customColumnsEnabled && (
                    <TooltipContent className="text-xs">
                      Update your stack to enable custom columns on this table
                    </TooltipContent>
                  )}
                </TooltipPortal>
              </Tooltip>
            ),
            className: cn("gap-2", {
              "opacity-50 aria-selected:bg-transparent": !customColumnsEnabled,
            }),
            onSelect: () => {
              customColumnsEnabled && setCustomColumnDialogVariant("create");
            },
          },
          ...(customColumns && customColumns.length > 0
            ? [
                {
                  label: (
                    <Tooltip>
                      <TooltipTrigger asChild disabled={!customColumnsEnabled}>
                        <div className="flex w-full items-center gap-2">
                          <PencilLine className="size-3" />
                          Edit custom {gridLayout ? "fields" : "columns"}
                        </div>
                      </TooltipTrigger>
                      <TooltipPortal>
                        {!customColumnsEnabled && (
                          <TooltipContent className="text-xs">
                            Update your stack to enable custom columns on this
                            table
                          </TooltipContent>
                        )}
                      </TooltipPortal>
                    </Tooltip>
                  ),
                  className: cn("gap-2", {
                    "opacity-50 aria-selected:bg-transparent":
                      !customColumnsEnabled,
                  }),
                  onSelect: () => {
                    customColumnsEnabled &&
                      setCustomColumnDialogVariant("edit");
                  },
                },
              ]
            : []),
        ]
      : []),
  ];

  if (baseSummary) {
    // Action is wrapped an array so that "Order by regressions" is put in its own group
    bottomActions.unshift([
      {
        label: "Order by regressions",
        onSelect: onSelectOrderByRegression,
        hidden: !!gridLayout,
        disabled: allColumnsHidden,
        selected: columnOrderOverride === "regression",
      },
    ]);
  }

  return (
    <>
      <Combobox
        options={columns.map((c) => ({
          label: c.columnDef?.meta?.name ?? c.id,
          value: c.id,
          meta: c.columnDef?.meta,
          column: c,
          disabled:
            c.columnDef?.meta?.pinnedColumnIndex != null &&
            !c.columnDef?.meta?.isGridLayout,
          aliases: HeaderAliases[c.id] ? [HeaderAliases[c.id]] : [],
        }))}
        tooltipContent={gridLayout ? "Fields" : "Columns"}
        align="start"
        variant="button"
        selectedValues={columns.flatMap((c) =>
          columnVisibility[c.id] !== false ? [c.id] : [],
        )}
        // Include drag end handler to enable reorder functionality only when the table isn't in grid layout
        handleDragEnd={!gridLayout ? handleDragEnd : undefined}
        renderOptionLabel={({ meta, value, label }, _className, options) => {
          const formatter = formatters?.[label];
          const isScore = meta?.path?.[0] === "scores";
          const isSelected = columnVisibility[value] !== false;

          return (
            <>
              {formatter?.headerIcon ? (
                <formatter.headerIcon className="mr-1 size-3 flex-none text-primary-500" />
              ) : isScore ? (
                <PercentIcon className="mr-1 size-3 flex-none text-primary-500" />
              ) : meta?.type === "span_iframe" ? (
                <AppWindowMac className="mr-1 size-3 flex-none text-primary-500" />
              ) : null}
              <span className="flex-1 truncate">
                {formatter?.headerLabel || label}
              </span>
              {enableScrollToColumn && isSelected && (
                <Button
                  size="inline"
                  variant="ghost"
                  className="mr-2 hidden flex-none text-primary-400 hover:text-primary-600 group-hover:block"
                  Icon={Locate}
                  onClick={(e) => {
                    e.stopPropagation();
                    const event = new CustomEvent("bt:scroll-to-column", {
                      detail: { columnId: value },
                    });
                    window.dispatchEvent(event);
                    options?.onClose?.();
                  }}
                />
              )}
            </>
          );
        }}
        buttonSize="xs"
        buttonVariant="ghost"
        searchPlaceholder={gridLayout ? "Find a field" : "Find a column"}
        placeholderClassName="inline-flex items-center gap-1 text-xs"
        placeholderLabel={
          gridLayout ? (
            <>
              <LayoutList className="size-3" />
              <span className="hidden @xl/controls:block">Fields</span>
            </>
          ) : (
            <>
              <Columns2 className="size-3" />
              <span className="hidden @xl/controls:block">Columns</span>
            </>
          )
        }
        iconClassName="hidden"
        onChange={(v, o) => {
          const id = o.column.id;
          if (!id) {
            return;
          }
          o.column.toggleVisibility(
            columnVisibility[id] == null ? false : !columnVisibility[id],
          );
        }}
        noResultsLabel="No columns found"
        bottomActions={bottomActions}
        stayOpenOnChange
      />
      <CustomColumnConfigurationDialog
        variant={customColumnDialogVariant}
        onOpenChange={(open) => {
          setCustomColumnDialogVariant(undefined);
          !open && onCloseCustomDialog();
        }}
        customColumns={customColumns}
        customColumnOptions={customColumnOptions}
        clauseChecker={clauseChecker}
        initialValues={initialFormValues}
        existingColumns={existingColumns}
        createCustomColumn={createCustomColumn}
        updateCustomColumn={updateCustomColumn}
        deleteCustomColumn={deleteCustomColumn}
      />
    </>
  );
}

export type CustomColumnDialogVariant = "create" | "edit";

const CustomColumnConfigurationDialog = ({
  variant,
  onOpenChange,
  customColumns,
  customColumnOptions,
  clauseChecker,
  initialValues,
  existingColumns,
  createCustomColumn,
  updateCustomColumn,
  deleteCustomColumn,
}: {
  variant?: CustomColumnDialogVariant;
  onOpenChange: (open: boolean) => void;
  customColumns?: CustomColumnDefinition[];
  customColumnOptions?: Record<string, NestedField[]>;
  clauseChecker: ClauseChecker | null;
  initialValues?: CustomColumnDef & { id: string };
  existingColumns: Map<string, boolean>;
  createCustomColumn?: (col: CustomColumnDef) => Promise<void>;
  updateCustomColumn?: ({
    columnId,
    columnData,
  }: {
    columnId: string;
    columnData: CustomColumnDef;
  }) => Promise<void>;
  deleteCustomColumn?: (col: { id: string; name: string }) => void;
}) => {
  return (
    <Dialog
      open={!!variant}
      onOpenChange={(open) => {
        onOpenChange(open);
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {variant === "create"
              ? initialValues
                ? "Edit column"
                : "Create derived data column"
              : "Edit columns"}
          </DialogTitle>
          <DialogDescription>
            {variant === "create" ? (
              <>
                Configure a{" "}
                <Link
                  className="font-medium text-accent-600"
                  href="/docs/guides/evals/interpret#create-custom-columns"
                  target="_blank"
                >
                  custom column
                </Link>{" "}
                to pull data from the root span into its own column. To create a
                score column, check out the{" "}
                <Link
                  className="font-medium text-accent-600"
                  href="/docs/guides/evals/write#scorers"
                  target="_blank"
                >
                  writing evals guide
                </Link>
                .
              </>
            ) : (
              "Edit or delete existing columns"
            )}
          </DialogDescription>
        </DialogHeader>
        {variant === "create" ? (
          <CustomColumnForm
            clauseChecker={clauseChecker}
            customColumnOptions={customColumnOptions}
            createCustomColumn={createCustomColumn}
            updateCustomColumn={updateCustomColumn}
            initialValues={initialValues}
            existingColumns={existingColumns}
            onFinish={() => {
              onOpenChange(false);
            }}
          />
        ) : variant === "edit" && customColumns && customColumns.length > 0 ? (
          customColumns.reduce<JSX.Element[]>((acc, col, i) => {
            if (!col.builtIn) {
              acc.push(
                <CustomColumnLine
                  key={i}
                  col={col}
                  clauseChecker={clauseChecker}
                  customColumnOptions={customColumnOptions}
                  createCustomColumn={createCustomColumn}
                  updateCustomColumn={updateCustomColumn}
                  deleteCustomColumn={deleteCustomColumn}
                  existingColumns={existingColumns}
                />,
              );
            }
            return acc;
          }, [])
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

const CustomColumnLine = ({
  col: { id, name, expr },
  clauseChecker,
  customColumnOptions,
  createCustomColumn,
  updateCustomColumn,
  deleteCustomColumn,
  existingColumns,
}: {
  col: CustomColumnDefinition;
  clauseChecker: ClauseChecker | null;
  customColumnOptions?: Record<string, string[][]>;
  createCustomColumn?: (col: CustomColumnDef) => Promise<void>;
  updateCustomColumn?: ({
    columnId,
    columnData,
  }: {
    columnId: string;
    columnData: CustomColumnDef;
  }) => Promise<void>;
  deleteCustomColumn?: (col: { id: string; name: string }) => void;
  existingColumns: Map<string, boolean>;
}) => {
  const [open, setOpen] = useState(false);
  return (
    <div className="flex gap-2">
      <div className="flex flex-1 items-center text-sm">{name}</div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
            }}
            className="size-8 p-0"
            title="Edit column"
          >
            <PencilLine className="size-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          collisionPadding={8}
          side="right"
          align="start"
          className="flex w-[400px] flex-col gap-2"
        >
          <CustomColumnForm
            clauseChecker={clauseChecker}
            customColumnOptions={customColumnOptions}
            createCustomColumn={createCustomColumn}
            updateCustomColumn={updateCustomColumn}
            initialValues={{ name, expr, id }}
            existingColumns={existingColumns}
            onFinish={() => {
              setOpen(false);
            }}
          />
        </PopoverContent>
      </Popover>
      <Button
        onClick={(e) => {
          e.stopPropagation();
          deleteCustomColumn?.({ id, name });
        }}
        className="size-8 p-0"
        size="sm"
        title="Remove column"
        Icon={Trash2}
      />
    </div>
  );
};
