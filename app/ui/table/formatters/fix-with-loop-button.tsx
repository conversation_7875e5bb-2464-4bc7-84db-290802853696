import { Button } from "#/ui/button";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";
import { cn } from "#/utils/classnames";

export const FixWithLoopButton = ({
  error,
  className,
}: {
  error: string;
  className?: string;
}) => {
  const { setIsChatOpen, handleSendMessage } = useGlobalChat();

  return (
    <Button
      variant="border"
      size="xs"
      className={cn(
        "hidden size-fit text-xs font-normal bg-background group-hover:block",
        className,
      )}
      onClick={(e) => {
        e.stopPropagation();
        setIsChatOpen(true);
        handleSendMessage(
          {
            id: crypto.randomUUID(),
            type: "user_message",
            message: `Please fix the following error: ${error}`,
          },
          {
            clearContextObjects: false,
            clearUserMessage: false,
          },
        );
      }}
    >
      Fix with Loop
    </Button>
  );
};
