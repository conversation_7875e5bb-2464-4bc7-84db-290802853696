import React from "react";
import * as Sentry from "@sentry/nextjs";
import { AlertCircle } from "lucide-react";
import { BasicTooltip } from "#/ui/tooltip";

interface TableCellErrorBoundaryProps {
  children: React.ReactNode;
  columnName: string | undefined;
  value: unknown;
  tooltip?: boolean;
}

interface TableCellErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class TableCellErrorBoundary extends React.Component<
  TableCellErrorBoundaryProps,
  TableCellErrorBoundaryState
> {
  constructor(props: TableCellErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): TableCellErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Table cell rendering error:", {
      error,
      columnName: this.props.columnName,
      errorInfo,
    });

    if (Sentry) {
      Sentry.captureException(error, {
        tags: {
          page: "tooltip-error",
        },
        extra: {
          props: this.props,
          componentStack: errorInfo.componentStack,
        },
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <BasicTooltip
          tooltipContent={`Error rendering ${this.props.columnName}`}
        >
          <div className="flex items-center gap-2 rounded-md bg-red-50 px-2 py-1 text-xs text-red-600">
            <AlertCircle className="size-3.5 flex-none" />
            <span>{`Error rendering ${this.props.columnName}: ${this.state.error?.message}`}</span>
          </div>
        </BasicTooltip>
      );
    }

    return this.props.children;
  }
}
