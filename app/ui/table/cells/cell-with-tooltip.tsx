import React from "react";
import { cn } from "#/utils/classnames";
import { isObject } from "#/utils/object";
import { StructRow } from "apache-arrow";
import { isDiffObject } from "#/utils/diffs/diff-objects";
import { DataType } from "apache-arrow";
import { isNumericType } from "#/ui/trace/diff-score-object";
import { areAllValuesEqual } from "#/utils/are-all-values-equal";
import { type RowComparisonFormatterProps } from "#/ui/table/formatters/row-comparison-formatter";
import {
  type CellContentProps,
  CellContent,
  CellContainer,
  MultiCellContent,
} from "./cell-content";
import { type MergedValue } from "#/ui/field-to-column";

export function CellWithTooltips<TsData, TsValue>(
  cellContentProps: Omit<CellContentProps<TsData, TsValue>, "value"> & {
    onClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
    rowComparisonProps?: RowComparisonFormatterProps;
  },
) {
  const {
    className = "",
    size,
    cell,
    multilineRowCount,
    showLeftBorder,
    onClick,
    //groupBy,
    rowComparisonProps,
    streamingContentProps,
  } = cellContentProps;

  const meta = cell.column.columnDef.meta;
  const cellValue = cell.getContext().getValue();
  const isGroupRow = cell.row.getCanExpand();
  const { value, mergedValues } =
    isObject(cellValue) && "mergedValues" in cellValue
      ? cellValue
      : { value: cellValue, mergedValues: undefined };

  let content = null;
  if (
    !cell?.column.columnDef.meta?.ignoreMultilineRendering &&
    isDiffObject(value) &&
    meta &&
    DataType.isStruct(meta.type) &&
    value instanceof StructRow
  ) {
    const values = value.toArray();
    const isMetric = meta?.path?.[0] === "metrics";
    const isScore = meta?.path?.[0] === "scores";

    if (!values) {
      const arrowType = meta.type.children[0].type;
      content = (
        <CellContent
          {...cellContentProps}
          value={null}
          meta={{
            ...meta,
            type: arrowType,
            isNumeric: isNumericType(arrowType),
            isTimestamp: DataType.isTimestamp(arrowType),
          }}
        />
      );
    } else if (
      (!streamingContentProps || cell.column.columnDef.id !== "output") &&
      areAllValuesEqual(
        values.map(
          (v, i) =>
            meta?.parseValue?.(v, meta.type.children[i].type, meta.typeHint) ??
            v,
        ),
      ) &&
      !mergedValues &&
      !isMetric &&
      !isScore
    ) {
      const arrowType = meta.type.children[0].type;
      content = (
        <CellContent
          {...cellContentProps}
          value={values[0]}
          meta={{
            ...meta,
            type: arrowType,
            isNumeric: isNumericType(arrowType),
            isTimestamp: DataType.isTimestamp(arrowType),
          }}
        />
      );
    } else {
      content = (
        <CellContainer
          className="w-full"
          multilineRowCount={multilineRowCount}
          isGroupRow={isGroupRow}
          showLeftBorder={showLeftBorder}
          isGridLayout={meta.isGridLayout}
        >
          <div className="flex flex-col gap-1.5">
            {values.map((_v: unknown, i: number) => {
              return (
                <MultiCellContent
                  key={i}
                  cell={cell}
                  value={_v}
                  originalValue={value}
                  mergedValues={mergedValues}
                  index={i}
                  isGroupRow={isGroupRow}
                  rowComparisonProps={rowComparisonProps}
                  cellContentProps={cellContentProps}
                />
              );
            })}
          </div>
        </CellContainer>
      );
    }
  } else {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const mergedValuesCoerced = mergedValues as Record<
      string,
      MergedValue<TsData, TsValue>
    >;

    content = (
      <CellContent
        {...cellContentProps}
        value={value}
        mergedValues={mergedValuesCoerced}
      />
    );
  }

  return (
    <div
      style={{
        width: size,
      }}
      className={cn(
        "relative cursor-pointer flex-none z-10 h-full min-h-full shrink-0 grow-0",
        {
          "inline-flex items-center border-primary-200": isGroupRow,
        },
        className,
      )}
      onClick={(e) => {
        if (onClick && !e.defaultPrevented) {
          return onClick(e);
        }
        e.stopPropagation();
      }}
      onAuxClick={(e) => {
        // Treat middle-click as a click.
        // The parent will handle the middle-click and open a new tab
        if (onClick && e.button === 1) {
          return onClick(e);
        }
        e.stopPropagation();
      }}
    >
      {content}
      {meta?.path?.[0] === "scores" && typeof value === "number" && (
        <span
          className="absolute left-3 z-0 h-0.5 rounded-md bg-primary-300 opacity-80"
          style={{
            top: 30,
            width: `calc(${value * 100}% - 12px)`,
          }}
        />
      )}
    </div>
  );
}
