"use client";

import { prepareRowsForCopy } from "#/app/app/[org]/p/[project]/experiments/[experiment]/ExperimentSelectionSection";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import type { DataObjectType } from "#/utils/btapi/btapi";
import { cn } from "#/utils/classnames";
import {
  type UpdateRowFn,
  type CommentFn,
  type DeleteCommentFn,
} from "#/utils/mutable-object";
import { isEmpty, isObject } from "#/utils/object";
import { useOtherObjectInserter } from "#/utils/other-object-inserter";
import { useOrg, useUser } from "#/utils/user";
import { TRANSACTION_ID_FIELD } from "braintrust/util";
import {
  type ReactNode,
  type TransitionStartFunction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type BatchUpdateRowFn } from "#/ui/arrow-table";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { type SavingState } from "#/ui/saving";
import { ActionButton } from "#/ui/trace/ActionButton";
import {
  type LoadedTrace,
  type Span,
  type Trace,
  diffTraces,
  getSpanBySpanId,
  makeDiffSpan,
} from "#/ui/trace/graph";
import {
  diffKeynameForIndex,
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  flattenDiffObjects,
  isDiffObject,
  type RowId,
} from "#/utils/diffs/diff-objects";
import { useRowAuditLog } from "#/ui/trace/query";
import { useTraceShortcuts } from "#/ui/trace/shortcuts";
import {
  FoldVertical,
  Hourglass,
  Minimize2,
  PanelLeft,
  SearchSlash,
  StretchHorizontal,
  Timer,
  TimerOff,
  TriangleAlert,
  UnfoldVertical,
} from "lucide-react";
import { Tags } from "#/ui/trace/tags";
import { ExpandedHumanReviewModal } from "#/ui/trace/expanded-human-review-modal";
import { TraceTree } from "#/ui/trace/trace-tree";
import { type DataDisplayedField } from "#/ui/trace/data-display";
import { ScrollableContainerWithOptions } from "#/ui/trace/scrollable-container-with-options";
import {
  useHumanReviewState,
  useAttachmentBrowser,
  useTraceViewTypeState,
  useActiveRowAndSpan,
} from "#/ui/query-parameters";
import { type Roster } from "#/utils/realtime-data";
import { RosterAvatars } from "#/ui/roster";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHotkeysContext } from "react-hotkeys-hook";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { usePanelSize } from "#/ui/use-panel-size";
import { Spinner } from "#/ui/icons/spinner";
import { PaginatedTraceSearch } from "./trace-search";
import { TraceHeader } from "./trace-header";
import { useTraceCopilotContext } from "#/ui/copilot/trace";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { Skeleton } from "#/ui/skeleton";
import { SpanContents } from "./span-contents";

import { GoToOriginProvider } from "./go-to-origin-context";
import { TraceSearchResults } from "./trace-search-results";
import { type Dataset } from "@braintrust/typespecs";
import { Tag } from "#/ui/tag";
import { SpanHeader, SpanName } from "./span-header";
import {
  RowComparisonSelection,
  isDiffModeRowId,
} from "./RowComparisonSelection";
import { freeFormDataPath } from "./free-form-text-area";
import { Dialog, DialogContent, DialogTitle } from "#/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/color";
import { useComparisonRowData } from "#/ui/row-comparison/useComparisonRowData";
import { type ComparisonExperimentSpanSummary } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/useExperiment";
import { IFrameViewer, RefreshIframeButton } from "#/ui/iframe-viewer";
import { useExpandedSpanIframe } from "./use-expanded-span-iframe";
import { Button } from "#/ui/button";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import useEvent from "react-use-event-hook";
import { AttachmentBrowser } from "#/ui/attachment/attachment-browser";
import { type CustomColumnsRowParams } from "#/utils/custom-columns/use-expanded-row-custom-columns";
import { getExpandedRowParamsPlayground } from "./playground";
import { RESERVED_TAGS } from "#/app/app/[org]/p/[project]/configuration/tags/tag-dialog";
import { useExtractScores } from "./use-extract-scores";
import useSpanSelection from "./use-span-selection";
import { SpanField } from "./use-span-field-order.tsx";
import { useTraceSearch, useTraceSearchQuery } from "./trace-search-context";
import { type PlaygroundRecord } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/playx";
import { type Virtualizer } from "@tanstack/react-virtual";
import { TraceLayoutDropdown } from "./trace-layout-dropdown";
import { useVirtualTrace } from "./use-virtual-trace";
import { type ModelCosts } from "#/ui/prompts/models";
import { useQueryClient } from "@tanstack/react-query";
import { useLoadFullSpans } from "./use-load-full-spans";
import { makeFullSpanQueryKey } from "./loading/query-utils";
import { Assign } from "./assign";
import { z } from "zod";
import { BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";
import { getTraceDurations } from "./trace-utils";
import {
  calculateLoadingState,
  isPrimarySpanLoaded,
  isValidRootId,
  resolvePrimaryRowIdForUpdate,
} from "./loading/loading-state";
import { isRealtimeStateExhausted } from "#/ui/banners/realtime-state-banner";
import { ErrorBanner } from "#/ui/error-banner";
import { ErrorBoundary } from "#/utils/error-boundary";
import { PaginatedTraceThreadView, TraceThreadView } from "./trace-thread-view";
import { emptyArray } from "#/utils/react";
import { pluralizeWithCount } from "#/utils/plurals";
import { BasicTooltip } from "#/ui/tooltip";
import {
  useRelatedTraces,
  type TracePaginationProps,
} from "./use-related-traces";
import { type RowNavigationFn } from "./use-row-navigation";

export interface TraceProps {
  rowId: string | DiffObjectType<string>;
  rowIndex?: number;
  totalRows?: number;
  firstRowId?: RowId;
  traceViewParams: TraceViewParams;
  onClose: VoidFunction;
  onPrevRow?: RowNavigationFn;
  onNextRow?: RowNavigationFn;
  onJumpToRow?: (rowIndex: number) => void;
  onApplySearch: ApplySearch;
  isReadOnly?: boolean;
  isDelayedSpanChangeTransitioning: boolean;
  startSpanChangeTransition: TransitionStartFunction;
}
export interface ExpandedRowParams {
  primaryScan: string | null;
  primaryScanReady: number[];
  primaryDynamicObjectId: string | null;
  comparisonDynamicObjectId?: string | null;
  comparisonDynamicObjectType?: DataObjectType | null;
  auditLogScan: string | null;
  auditLogScanReady: number[];
  modelSpecScan?: string | null;
  allAvailableModelCosts?: Record<string, ModelCosts>;
  multiExperimentData?: {
    comparisonExperimentData: ComparisonExperimentSpanSummary[];
  };
  hasTrials?: boolean;
  customColumnsParams: CustomColumnsRowParams;
  isFastSummaryEnabled?: boolean;
}

export interface TraceViewParams {
  title: string;
  objectType: DataObjectType;
  objectName: string;
  objectId: string | undefined;
  expandedRowParams: ExpandedRowParams;
  roster: Roster;
  experimentNames?: { current: string; comparison?: string };
  selectableExperimentsList?: {
    id: string;
    name: string;
    created?: string | null;
    scan?: string | null;
    refreshed?: number;
  }[];
  editableFields: DataDisplayedField[];
  hiddenFields?: DataDisplayedField[];
  updateRow?: UpdateRowFn;
  batchUpdateRow?: BatchUpdateRowFn;
  commentFn?: CommentFn;
  deleteCommentFn?: DeleteCommentFn;
  savingState?: SavingState;
  playXProps?: {
    runPrompts: (args: {
      rowIdx?: number; // playx uses the id rather than the ordinal, so we can remove this once we've migrated
      datasetRecordId?: string;
      datasetXactId?: string;
      inputValue?: Partial<PlaygroundRecord>;
      updateRowIds?: string[];
    }) => void;
    stop: (rowIdx: number | null) => void;
    isRunning: boolean;
    datasetDml: {
      updateRow: UpdateRowFn;
      commentFn: CommentFn;
      deleteCommentFn: DeleteCommentFn;
    };
    datasetEditableFields: DataDisplayedField[];
  };
  grouping?: string;
}

const HUMAN_REVIEW_DEFAULT_COLLAPSED_FIELDS = [SpanField.SCORES];

export function TraceViewer({
  traceViewParams,
  rowId,
  rowIndex,
  totalRows,
  firstRowId,
  onClose,
  onPrevRow,
  onNextRow,
  onJumpToRow,
  onApplySearch,
  isReadOnly,
  isDelayedSpanChangeTransitioning,
  startSpanChangeTransition,
}: TraceProps) {
  const {
    mutateDatasets,
    projectId,
    projectName,
    config: projectConfig,
    isConfigLoading,
  } = useContext(ProjectContext);
  const { user } = useUser();
  const [_viewType, setViewType] = useTraceViewTypeState(projectId);

  const {
    objectType: objectTypeProp,
    objectName,
    objectId,
    expandedRowParams,
    experimentNames,
    batchUpdateRow,
    updateRow: updateRowProp,
    editableFields: editableFieldsProp,
    hiddenFields,
    selectableExperimentsList,
    roster,
    playXProps,
    grouping,
  } = traceViewParams;

  const dynamicRowType = getExpandedRowParamsPlayground(rowId);
  const objectType = dynamicRowType?.objectType ?? objectTypeProp;
  const isDatasetRow = objectType === "dataset";

  const viewType = isDatasetRow ? "trace" : _viewType;

  const { id: orgId, name: orgName } = useOrg();

  const addRowsToDataset = useOtherObjectInserter({
    objectType: "dataset",
  });

  const performAddRowsToDataset = useCallback(
    ({
      datasetId,
      datasetName,
      spans,
      selectedProjectId,
      selectedProjectName,
    }: {
      datasetId: string;
      datasetName: string;
      spans: Span[] | null;
      selectedProjectId?: string;
      selectedProjectName?: string;
    }) => {
      if (!projectId || spans === null || spans.length === 0) {
        return;
      }
      const rows = spans.map((span) => {
        // TODO(kevin): Remove hack.
        // Why does it have SpanData type but can be diff?
        const row = { ...span.data };
        for (const key of Object.keys(row)) {
          if (isDiffObject(row[key])) {
            row[key] = row[key][DiffLeftField];
          }
        }
        return row;
      });
      addRowsToDataset(
        prepareRowsForCopy({
          orgName,
          projectName: selectedProjectName ?? projectName,
          projectId: selectedProjectId ?? projectId,
          objectType: objectTypeProp,
          objectId,
          selectedRowsWithData: rows,
          targetDataset: {
            id: datasetId,
            name: datasetName,
          },
          expectedFieldSource: "auto",
          cleanup: () => {},
        }),
      );
    },
    [
      addRowsToDataset,
      orgName,
      projectId,
      projectName,
      objectId,
      objectTypeProp,
    ],
  );

  const { modal: createDatasetModal, open: openCreateDatasetDialog } =
    useCreateDatasetDialog({
      onSuccessfulCreate: async ({
        datasetId,
        datasetName,
        projectName,
        getRows,
      }) => {
        performAddRowsToDataset({
          datasetName: datasetName,
          datasetId: datasetId,
          // if the caller provides a function to customize the rows when opening dialog (e.g. for multi-turn prompts), use it
          spans: (await getRows?.()) ?? (selectedSpan ? [selectedSpan] : null),
          selectedProjectName: projectName,
        });
        mutateDatasets?.();
      },
      orgId,
      projectName,
    });

  const primaryRowId =
    (isDiffObject(rowId) ? rowId[DiffRightField] : rowId) || null;

  const dynamicObjectId =
    dynamicRowType?.objectId ?? expandedRowParams.primaryDynamicObjectId;
  const editableFields =
    objectType === "dataset" && playXProps
      ? playXProps.datasetEditableFields
      : editableFieldsProp;
  const updateRow =
    objectType === "dataset"
      ? (playXProps?.datasetDml.updateRow ?? updateRowProp)
      : updateRowProp;

  const {
    trace: primaryTrace,
    buildTraceError: traceError,
    comparisonKey,
    realtimeState: traceRealtimeState,
    hasNoData: hasNoTraceData,
    isQueryLoading: isTraceLoading,
  } = useVirtualTrace({
    rowId: primaryRowId,
    objectType,
    objectId: dynamicObjectId,
    modelCosts: expandedRowParams.allAvailableModelCosts,
  });
  const {
    groupingData,
    tracePaginationProps,
    buildTraceError: relatedTracesError,
    isLoading: isRelatedTracesLoading,
  } = useRelatedTraces({
    rowId: primaryRowId,
    objectType,
    objectId: dynamicObjectId,
    grouping,
    trace: primaryTrace,
    modelCosts: expandedRowParams.allAvailableModelCosts,
    customColumns: expandedRowParams.customColumnsParams?.customColumns,
  });
  const isTraceQueryLoading = isTraceLoading || isRelatedTracesLoading;
  const buildTraceError = traceError ?? relatedTracesError;

  const copilotContext = useTraceCopilotContext({
    objectType,
    objectName,
  });

  const [{ s: activeSpanId }, setActiveRowAndSpan] = useActiveRowAndSpan();
  const primarySpanRowId =
    // load root span if the span id is not valid
    (activeSpanId ? primaryTrace?.spans[activeSpanId]?.id : null) ??
    primaryTrace?.root.id ??
    primaryRowId;
  const {
    traceWithLoadedSpans: loadedTrace,
    loadedSpan: primaryLoadedSpan,
    isPending: isPrimaryRowPending,
    realtimeState: spanRealtimeState,
  } = useLoadFullSpans({
    trace: primaryTrace,
    spanRowId: primarySpanRowId,
    isRoot:
      primarySpanRowId === primaryRowId ||
      (!!primaryTrace?.root.id && primaryTrace.root.id === primarySpanRowId),
    objectType,
    objectId: dynamicObjectId ?? "",
    customColumns: expandedRowParams.customColumnsParams?.customColumns,
    relatedRowIds: tracePaginationProps?.fetchedRelatedRowIds,
  });

  const {
    queryEnabled: isComparisonRowQueryEnabled,
    isPending: isComparisonRowsPending,
    comparisonRowId: selectedCompareRowId,
    comparisonRowIds,
    comparisonExperiment,
    comparisonExperimentIndex,
    setComparisonExperimentId,
    setSelectedComparisonParams,
  } = useComparisonRowData({
    sourceData: {
      type: "query",
      enabled: !!expandedRowParams.hasTrials && isDiffModeRowId(rowId),
    },
    comparisonKey,
    comparisonExperimentData:
      expandedRowParams.multiExperimentData?.comparisonExperimentData,
  });
  const comparisonRowParams = comparisonExpandedRowParams({
    rowId,
    // Send an empty string when the query is enabled so that we don't fall back to the default comparison row
    // This helps with flickering loading states when switching between rows
    selectedRowId: isComparisonRowQueryEnabled
      ? (selectedCompareRowId ?? "")
      : null,
    expandedRowParams,
    selectedExperimentIndex: comparisonExperimentIndex,
    makeDynamicObjectId:
      expandedRowParams.primaryDynamicObjectId !== null ||
      !!expandedRowParams.comparisonDynamicObjectId,
    comparisonDynamicObjectId: expandedRowParams.comparisonDynamicObjectId,
  });
  const { trace: comparisonTracePreview } = useVirtualTrace({
    ...comparisonRowParams,
    objectType: expandedRowParams.comparisonDynamicObjectType ?? objectType,
    objectId: comparisonRowParams.dynamicObjectId,
    modelCosts: expandedRowParams.allAvailableModelCosts,
    allowEmpty: true,
  });
  const comparisonRowId = comparisonRowParams.rowId;

  const { trace: traceDiffPreview, spanIdsMap } = useMemo(() => {
    if (primaryTrace && comparisonTracePreview) {
      return diffTraces(comparisonTracePreview, primaryTrace);
    }
    return { trace: primaryTrace, spanIdsMap: {} };
  }, [primaryTrace, comparisonTracePreview]);
  const comparisonSpanPreview = getSpanBySpanId({
    spanId: activeSpanId,
    spanIdsMap,
    spans: comparisonTracePreview?.spans,
  });
  const { loadedSpan: comparisonLoadedSpan } = useLoadFullSpans({
    trace: comparisonTracePreview,
    spanRowId: comparisonSpanPreview?.id ?? null,
    isRoot:
      !!comparisonTracePreview?.root.id &&
      comparisonTracePreview.root.id === comparisonSpanPreview?.id,
    objectType: expandedRowParams.comparisonDynamicObjectType ?? objectType,
    objectId: comparisonRowParams.dynamicObjectId ?? "",
    customColumns: expandedRowParams.customColumnsParams?.customColumns,
  });

  const { setSelectedSpan, scrollTo, scrollToSpanId, setScrollTo } =
    useSpanSelection({
      copilotContext,
      startSpanChangeTransition,
      trace: primaryTrace,
      loadedTrace,
      comparisonTrace: comparisonTracePreview,
      skipUpdateUrl: true,
      relatedRowIds: tracePaginationProps?.allRelatedRowIds,
    });

  const [selectedLoadedTrace, setSelectedLoadedTrace] =
    useState<LoadedTrace | null>(null);
  const trace = selectedLoadedTrace;
  const [selectedLoadedSpan, setSelectedLoadedSpan] = useState<Span | null>(
    null,
  );
  const selectedSpan = selectedLoadedSpan;

  const hasPrimarySpan = !!primarySpanRowId;
  // In diff mode, the active span id may not be valid in the primary trace
  // but valid in the comparison trace.
  // Since we fallback to load the primary root span as for invalid active span ids,
  // we want to check if the active span id is actually valid in the primary trace.
  const isPrimaryActiveSpanId = !!primaryTrace?.spans[activeSpanId ?? ""];
  const primarySelectedSpan = isPrimarySpanLoaded({
    primaryLoadedSpan,
    primaryTrace,
    primaryRowId,
    activeSpanId,
  })
    ? (primaryLoadedSpan ?? null)
    : null;
  const hasComparisonSpan = !!comparisonSpanPreview?.id;
  const comparisonSelectedSpan =
    comparisonLoadedSpan?.data.id === comparisonSpanPreview?.id
      ? comparisonLoadedSpan
      : null;
  const hasLoadedTrace = !!loadedTrace;
  const activeLoadedTrace = isValidRootId({
    rowId: primaryRowId,
    rootSpan: loadedTrace?.root,
    relatedRowIds: tracePaginationProps?.allRelatedRowIds,
  })
    ? loadedTrace
    : null;
  const [selectedPreviewTrace, setSelectedPreviewTrace] =
    useState<Trace | null>(null);
  const selectedSpans = useMemo(() => {
    return [primarySelectedSpan?.data, comparisonSelectedSpan?.data].filter(
      (v) => v != null,
    );
  }, [primarySelectedSpan, comparisonSelectedSpan]);

  // handle flickering when switching between:
  // - selected spans
  // - diff mode
  // - diffed traces
  useEffect(() => {
    if (!hasLoadedTrace || !activeLoadedTrace) {
      return;
    }
    let span: Span | null = null;
    if (
      hasPrimarySpan &&
      isPrimaryActiveSpanId &&
      primarySelectedSpan &&
      hasComparisonSpan &&
      comparisonSelectedSpan
    ) {
      span = makeDiffSpan(primarySelectedSpan, comparisonSelectedSpan);
    } else if (
      !isPrimaryActiveSpanId &&
      hasComparisonSpan &&
      comparisonSelectedSpan
    ) {
      span = makeDiffSpan(undefined, comparisonSelectedSpan);
    } else if (hasPrimarySpan && !hasComparisonSpan) {
      span = primarySelectedSpan;
    }

    if (span) {
      setSelectedLoadedTrace(loadedTrace);
      setSelectedPreviewTrace((prev) => (!prev ? loadedTrace : prev));
      setSelectedLoadedSpan(span);
      setActiveRowAndSpan((prev) => {
        const r = resolvePrimaryRowIdForUpdate({
          existingRowId: prev.r,
          loadedTrace,
        });
        const s = prev.s !== span.data.span_id ? span.data.span_id : null;
        return {
          ...(r ? { r } : {}),
          ...(s ? { s } : {}),
        };
      });
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      (window as any).currentSpan = span;
    }
  }, [
    hasPrimarySpan,
    isPrimaryActiveSpanId,
    hasComparisonSpan,
    primarySelectedSpan,
    comparisonSelectedSpan,
    setSelectedLoadedSpan,
    hasLoadedTrace,
    activeLoadedTrace,
    loadedTrace,
    setActiveRowAndSpan,
  ]);

  const hasLoadedDiffTrace =
    !!primaryTrace && (!comparisonRowParams.rowId || !!comparisonTracePreview);
  const activeDiffTrace =
    activeLoadedTrace &&
    (comparisonTracePreview?.root.data.id ?? null) === comparisonRowParams.rowId
      ? traceDiffPreview
      : null;
  useEffect(() => {
    if (hasLoadedDiffTrace && activeDiffTrace) {
      setSelectedPreviewTrace(activeDiffTrace);
    }
  }, [hasLoadedDiffTrace, activeDiffTrace]);

  const {
    isSearchOpen,
    setSearchOpen,
    searchQuery,
    searchResultFields,
    resultIndex,
    selectedSpanIds,
    setSelectedSpanIds,
    resetSearch,
  } = useTraceSearch();
  useEffect(() => {
    if (
      !tracePaginationProps ||
      !tracePaginationProps.allRelatedRowIds.some(
        ({ id }) => primaryRowId === id,
      )
    ) {
      resetSearch();
    }
  }, [primaryRowId, tracePaginationProps, resetSearch]);

  const traceSearchParams: Parameters<typeof useTraceSearchQuery>[0] =
    useMemo(() => {
      return {
        primarySearchParams:
          dynamicObjectId && primaryTrace?.root.root_span_id
            ? {
                objectType,
                objectId: dynamicObjectId,
                rootSpanIds: tracePaginationProps
                  ? tracePaginationProps.allRelatedRowIds.map(
                      (r) => r.root_span_id,
                    )
                  : primaryTrace
                    ? [primaryTrace.root.root_span_id]
                    : [],
              }
            : undefined,
        comparisonSearch:
          comparisonRowParams.dynamicObjectId &&
          comparisonTracePreview?.root.root_span_id
            ? {
                params: {
                  objectType,
                  objectId: comparisonRowParams.dynamicObjectId,
                  rootSpanIds: [comparisonTracePreview.root.root_span_id],
                },
                comparisonExperimentIndex: comparisonExperiment
                  ? comparisonExperimentIndex + 1
                  : 0,
              }
            : undefined,
        spanIdsMap,
      };
    }, [
      dynamicObjectId,
      primaryTrace,
      objectType,
      comparisonRowParams.dynamicObjectId,
      comparisonTracePreview?.root.root_span_id,
      tracePaginationProps,
      comparisonExperimentIndex,
      comparisonExperiment,
      spanIdsMap,
    ]);
  useTraceSearchQuery(traceSearchParams);

  const [isAttachmentBrowserOpen] = useAttachmentBrowser();

  const rowNavigationTraces = useMemo(() => {
    return (
      tracePaginationProps?.traces ??
      [traceDiffPreview].filter((v) => v != null)
    );
  }, [tracePaginationProps, traceDiffPreview]);
  useTraceShortcuts({
    traces: rowNavigationTraces,
    selectedSpan,
    setActiveSpan: setSelectedSpan,
  });

  const { auditLogData } = useRowAuditLog({
    auditLogScan: expandedRowParams.auditLogScan,
    auditLogReady: expandedRowParams.auditLogScanReady,
    rowId: selectedSpan?.id ?? null,
    dynamicObjectId,
    objectType,
  });

  const [collapseSpanState, setCollapseSpanState] = useState<
    "collapsed" | "expanded" | "mixed"
    // important - do not set an initial collapse state
    // because it will override the local storage state
  >();
  const resetForcedSpanCollapse = useEvent(() => setCollapseSpanState("mixed"));

  const [humanReviewState, setHumanReviewState] = useHumanReviewState();
  const isHumanReviewExpanded = humanReviewState === "1";

  const tagsObject = trace?.root.data.tags;
  const assignedUsers = useMemo(() => {
    try {
      const metadata = JSON.parse(trace?.root.data.metadata);
      const parsed = z
        .array(z.string())
        .nullish()
        .safeParse(metadata[BT_ASSIGNMENTS_META_FIELD]);
      return parsed.success ? (parsed.data ?? []) : [];
    } catch {
      return [];
    }
  }, [trace?.root.data.metadata]);

  const assignSection = !isReadOnly && trace?.root.data && (
    <Assign
      buttonVariant="ghost"
      assignedUsers={assignedUsers}
      xactId={trace.root.data[TRANSACTION_ID_FIELD]}
      updateAssignments={
        updateRow &&
        (async (newUsers) => {
          const flatSpan = flattenDiffObjects(trace.root.data);
          return await updateRow(
            flatSpan,
            ["metadata", BT_ASSIGNMENTS_META_FIELD],
            newUsers,
          );
        })
      }
      rowId={trace.root.data.id}
    />
  );

  const tagSection = projectConfig && trace?.root?.data && !isReadOnly && (
    <>
      {
        // DEPRECATION_NOTICE:
        // Somewhat subtley, undefined means that the api did not send up tags,
        // and null means that the api sent up an empty value.
        // We can remove this check once everyone updates their API.
        tagsObject !== undefined ? (
          <Tags
            buttonVariant="ghost"
            tags={tagsObject}
            xactId={trace.root.data[TRANSACTION_ID_FIELD]}
            updateTag={
              updateRow &&
              (async (newTags) => {
                const flatSpan = flattenDiffObjects(trace.root.data);
                return await updateRow(flatSpan, ["tags"], newTags);
              })
            }
            rowId={trace.root.data.id}
          />
        ) : (
          <div className="ml-4 w-[300px] text-sm">
            Please upgrade your stack to get access to tags
          </div>
        )
      }
    </>
  );

  const hasError =
    !isEmpty(selectedSpan?.data.error) &&
    (!isDiffObject(selectedSpan?.data.error) ||
      !isEmpty(selectedSpan?.data.error[DiffRightField]));

  const currentTraceViewers = useMemo(() => {
    if (!trace) {
      return null;
    }
    return roster.filter(
      (u) =>
        u.sessions.some((session) => session.row?.id === trace.root.data.id) &&
        u.user_id !== user?.id,
    );
  }, [roster, trace, user?.id]);

  const currentViewersSection = currentTraceViewers && (
    <RosterAvatars roster={currentTraceViewers} context="trace" />
  );

  const [traceTreeCollapsedState, setTraceTreeCollapsed] = useEntityStorage({
    entityType: "traceTree",
    entityIdentifier: projectId ?? "",
    key: "isCollapsed",
    defaultValue: isHumanReviewExpanded ? false : undefined,
  });
  const [isHumanReviewTraceTreeCollapsed, setHumanReviewTraceTreeCollapsed] =
    useEntityStorage({
      entityType: "humanReview",
      entityIdentifier: projectId ?? "",
      key: "isTraceTreeCollapsed",
    });

  const [manualConfirmationResolve, setManualConfirmationResolve] = useState<
    ((confirmed: boolean) => void) | null
  >(null);

  const { enableScope, disableScope } = useHotkeysContext();

  const receiveManualConfirmation = useCallback(async () => {
    enableScope(HotkeyScope.ConfirmationModal);
    return new Promise((resolve) => {
      setManualConfirmationResolve(
        // Have to wrap in two callbacks, because if we try to set the state
        // directly as a function, react will think it's a callback
        (_: ((confirmed: boolean) => void) | null) => {
          return (confirmed: boolean) => {
            resolve(confirmed);
          };
        },
      );
    });
  }, [enableScope]);

  const containerRef = useRef<HTMLDivElement>(null);
  const minTreePanelWidth = usePanelSize(
    200,
    // eslint-disable-next-line react-compiler/react-compiler
    containerRef.current ?? undefined,
  );
  const maxTreePanelWidth = usePanelSize(
    400,
    // eslint-disable-next-line react-compiler/react-compiler
    containerRef.current ?? undefined,
  );
  const spanPanelMinSize = usePanelSize(280);
  const attachmentPanelMinSize = usePanelSize(280);

  const onBulkAddToDataset = useCallback(
    (dataset: Dataset, spanIds: string[]) => {
      if (!selectedSpanIds.size) return;
      const traceSpans = Object.values(trace?.spans ?? {});

      const selectedSpans = spanIds
        .map((id) => {
          return traceSpans.find((s) => s.id === id);
        })
        .filter((s) => s !== undefined);

      performAddRowsToDataset({
        datasetId: dataset.id,
        datasetName: dataset.name,
        spans: selectedSpans,
      });

      setSelectedSpanIds(new Set());
    },
    [selectedSpanIds, trace, performAddRowsToDataset, setSelectedSpanIds],
  );

  const { expandedFrame, setExpandedFrame, parsedSpanData } =
    useExpandedSpanIframe({
      objectType,
      spanIframes: projectConfig?.span_iframes,
      selectedSpan,
    });

  const {
    expectedData,
    autoScores,
    manualScores,
    rootManualScores,
    numExpectedScores,
    oldestExpectedScore,
  } = useExtractScores({
    objectType,
    projectConfigScores: projectConfig?.scores,
    span: selectedSpan,
    rootSpan: trace?.root,
  });

  const queryClient = useQueryClient();
  const onUpdateManualReviewScore = useEvent(
    async (updates: ManualScoreUpdate[]) => {
      if (!batchUpdateRow) return [];

      const spanIdToRowUpdates: Map<
        string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        { path: string[]; newValue: any }[]
      > = new Map();
      const spanIdToRightData: Map<string, Record<string, unknown>> = new Map();

      for (const { name, category, value } of updates) {
        const score = manualScores[name];
        const span =
          score && trace && score.spanId && trace.spans[score.spanId];
        if (!span) {
          console.error("Unknown score " + name);
          return [];
        }

        if (!spanIdToRightData.has(span.id)) {
          const rightData = Object.fromEntries(
            Object.entries(span.data).map(([k, v]) => [
              k,
              isDiffObject(v) ? v[DiffRightField] : v,
            ]),
          );
          spanIdToRightData.set(span.id, rightData);
        }

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let rowUpdates: { path: string[]; newValue: any }[];
        if (score.config.config?.destination === "expected") {
          // Since in the single human review score case we write to expected as a flat value,
          // when another human review score is added, we may have to guess what the existing
          // expected score referred to when we make updates
          // There are 4 cases:
          // numExpectedScores === 1
          //   - The existing value is a string | string[] | null: we should overwrite it
          //   - The existing value is a { [category: string]: string | string[] | null }: we
          //     should inject the new value into the category
          // numExpectedScores > 1
          //  - The existing value is a string | string[] | null: we should assume that this value
          //    corresponds to the first category (by timestamp), and inject the new value into the
          //    second category, resulting in { [category]: string | string[] | null }.
          //    If the value does not match any existing category, we inject it into the "expected" key
          //  - The existing value is a { [category: string]: string | string[] | null }: we
          //    should inject the new value into the category
          if (numExpectedScores === 1 && isObject(expectedData)) {
            rowUpdates = [
              {
                path: ["expected", name],
                newValue: category ?? undefined,
              },
            ];
          } else if (numExpectedScores === 1) {
            const expectedCategories = Array.isArray(expectedData)
              ? expectedData
              : [expectedData];
            const allMatch = expectedCategories.every(
              (category) =>
                score.config.score_type === "categorical" &&
                score.config.categories.find((s) => s.name === category),
            );

            const confirmed =
              allMatch ||
              isEmpty(expectedData) ||
              (await receiveManualConfirmation());
            if (confirmed) {
              // filter out categories that don't match the score config
              const allowedCategories =
                Array.isArray(category) &&
                score.config.score_type === "categorical" &&
                score.config.categories;
              const categoryMatches = allowedCategories
                ? category.filter(
                    (c) => !!allowedCategories.find((s) => s.name === c),
                  )
                : category;

              rowUpdates = [
                {
                  path: ["expected"],
                  newValue: categoryMatches,
                },
              ];
            } else {
              continue;
            }
          } else if (numExpectedScores > 1 && isObject(expectedData)) {
            rowUpdates = [
              {
                path: ["expected", name],
                newValue: category,
              },
            ];
          } else if (numExpectedScores > 1) {
            const existingDataArray = Array.isArray(expectedData)
              ? expectedData
              : [expectedData];
            const isValidExistingScore =
              oldestExpectedScore?.score_type === "categorical"
                ? existingDataArray.some((d) =>
                    oldestExpectedScore.categories.some((s) => s.name === d),
                  )
                : null;
            rowUpdates = [
              ...(isValidExistingScore && oldestExpectedScore
                ? [
                    {
                      path: ["expected", oldestExpectedScore.name],
                      newValue: expectedData,
                    },
                  ]
                : expectedData != null
                  ? [
                      {
                        path: ["expected", "expected"],
                        newValue: expectedData,
                      },
                    ]
                  : []),
              {
                path: ["expected", name],
                newValue: category,
              },
            ];
          } else {
            console.assert(false, "Unexpected number of expected scores");
            rowUpdates = [
              {
                path: ["expected", name],
                newValue: value,
              },
            ];
          }
        } else if (score.config.score_type === "free-form") {
          rowUpdates = [
            {
              path: freeFormDataPath({
                scoreType: score.config.score_type,
                destination: score.config.config?.destination,
                name: score.config.name,
              }),
              newValue: value,
            },
          ];
        } else {
          rowUpdates = [
            {
              path: ["scores", name],
              newValue: value,
            },
          ];
        }

        if (!spanIdToRowUpdates.has(span.id)) {
          spanIdToRowUpdates.set(span.id, []);
        }
        spanIdToRowUpdates.get(span.id)!.push(...rowUpdates);
      }

      return await Promise.all(
        Array.from(spanIdToRowUpdates.entries()).map(
          async ([spanRowId, rowUpdates]) => {
            const result = await batchUpdateRow(
              spanIdToRightData.get(spanRowId)!,
              rowUpdates,
            );

            queryClient.invalidateQueries({
              queryKey: makeFullSpanQueryKey(
                objectType,
                dynamicObjectId ?? undefined,
                projectId,
                spanRowId,
              ),
            });
            return result;
          },
        ),
      );
    },
  );

  const scrollableContainerRef = useRef<HTMLDivElement>(null);
  const expandedFrameRef = useRef<HTMLIFrameElement>(null);

  const traceSearchVirtualizer = useRef<Virtualizer<
    HTMLDivElement,
    Element
  > | null>(null);

  const loadingState = calculateLoadingState({
    isTraceQueryLoading,
    hasNoLoadedTraceData: hasNoTraceData,
    primaryTrace: primaryTrace,
    comparisonTrace: comparisonTracePreview,
    primaryRowId: primaryRowId,
    loadedSpan: selectedSpan,
    isConfigLoading,
    relatedRowIds: tracePaginationProps?.allRelatedRowIds,
  });

  useEffect(() => {
    if (!isHumanReviewExpanded || loadingState !== "not_found" || !firstRowId) {
      return;
    }
    // in case there's an invalid row id set for human review, just select the first one
    setActiveRowAndSpan({ r: firstRowId, s: null });
  }, [isHumanReviewExpanded, loadingState, setActiveRowAndSpan, firstRowId]);

  const { numRelatedRowIds, groupingField, groupingValue } = useMemo(() => {
    if (!groupingData) return {};
    return {
      numRelatedRowIds: tracePaginationProps?.allRelatedRowIds?.length,
      groupingField:
        groupingData.path[0] === "tags"
          ? "tag"
          : groupingData.path.slice(1).join("."),
      groupingValue:
        typeof groupingData.value === "string"
          ? groupingData.value
          : JSON.stringify(groupingData.value),
    };
  }, [groupingData, tracePaginationProps?.allRelatedRowIds?.length]);

  // If in human review mode, show a loading dialog when we are loading the trace,
  // and we don't yet know if the trace is missing
  const isHumanReviewStillLoading = !trace || !selectedSpan || isConfigLoading;
  if (isHumanReviewExpanded && isHumanReviewStillLoading) {
    return (
      <div className="flex flex-col gap-1 px-4 pt-2">
        <Skeleton className="mb-2 h-10" />
        <Skeleton className="h-64" />
        <Skeleton className="h-64" />

        <Dialog open>
          <DialogContent hideCloseButton>
            <VisuallyHidden>
              <DialogTitle>Loading</DialogTitle>
            </VisuallyHidden>
            <div className="flex items-center gap-3 text-sm">
              <Spinner className="size-3" />
              Loading human review
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
  const isProcessing =
    isRealtimeStateExhausted(traceRealtimeState) ||
    isRealtimeStateExhausted(spanRealtimeState);

  // If the trace tree state has not been manually set, and the trace has no children, collapse it
  const isTraceTreeCollapsed =
    selectedPreviewTrace &&
    traceTreeCollapsedState === null &&
    selectedPreviewTrace.root.children.length === 0
      ? true
      : traceTreeCollapsedState;

  const comparisonClassName =
    EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[comparisonExperimentIndex];

  const spanContents = (opts: {
    shouldRenderManualScores?: boolean;
    defaultCollapsedFields?: SpanField[];
    shouldRenderSpanDetails?: boolean;
  }) => {
    if (!selectedSpan || !trace) return null;
    return (
      <>
        {/* TODO: Move props to context. */}
        {expandedFrame ? (
          <IFrameViewer
            key={selectedSpan.id}
            urlTemplate={expandedFrame.url}
            postMessage={expandedFrame.post_message ?? false}
            value={parsedSpanData}
            className="flex-1"
            iframeRef={expandedFrameRef}
            onUpdate={(field, data) => {
              updateRow?.(selectedSpan.data, field.split("."), data);
            }}
          />
        ) : (
          <SpanContents
            isDatasetRow={isDatasetRow}
            isRoot={trace?.root.id === selectedSpan.id}
            span={selectedSpan}
            hasError={hasError}
            trace={trace}
            projectConfig={projectConfig}
            onApplySearch={onApplySearch}
            autoScores={autoScores}
            rootManualScores={rootManualScores}
            copilotContext={copilotContext}
            comparisonClassName={comparisonClassName}
            resetForcedSpanCollapse={resetForcedSpanCollapse}
            resultIndex={resultIndex}
            searchResultFields={searchResultFields}
            searchQuery={searchQuery}
            objectType={objectType}
            editableFields={editableFields}
            hiddenFields={hiddenFields}
            defaultCollapsedFields={opts.defaultCollapsedFields}
            onUpdateManualReviewScore={onUpdateManualReviewScore}
            shouldRenderSpanDetails={opts.shouldRenderSpanDetails}
            shouldRenderManualScores={opts.shouldRenderManualScores}
            commentFn={
              playXProps && objectType === "dataset"
                ? playXProps.datasetDml.commentFn
                : traceViewParams.commentFn
            }
            deleteCommentFn={
              playXProps && objectType === "dataset"
                ? playXProps.datasetDml.deleteCommentFn
                : traceViewParams.deleteCommentFn
            }
            collapseSpanState={collapseSpanState}
            manualScores={manualScores}
            experimentNames={experimentNames}
            updateRow={updateRow}
            customColumns={expandedRowParams.customColumnsParams?.customColumns}
            auditLogData={auditLogData}
            allAvailableModelCosts={expandedRowParams.allAvailableModelCosts}
            scrollTo={
              !scrollToSpanId || scrollToSpanId === selectedSpan.span_id
                ? scrollTo
                : undefined
            }
          />
        )}
      </>
    );
  };

  const spanName = (
    <SpanName
      hasError={hasError}
      resultIndex={resultIndex}
      searchResultFields={searchResultFields}
      span={selectedSpan}
      includeModel
    />
  );

  const parentId = selectedSpan?.parent_span_id;
  const parentSpanType = parentId
    ? trace?.spans[parentId]?.data.span_attributes?.type
    : null;

  const showSearchResults =
    !isDatasetRow && isSearchOpen && searchQuery !== "" && selectedSpan;

  const traceContent = selectedPreviewTrace ? (
    <>
      {isSearchOpen && selectedSpan && (
        <PaginatedTraceSearch
          virtualizerRef={traceSearchVirtualizer}
          isDataset={isDatasetRow}
          onGoToField={({ spanId, rootSpanId, field }) => {
            if (spanId !== selectedSpan.span_id) {
              setSelectedSpan(spanId, rootSpanId);
            }
            setScrollTo({ spanId, v: field });
          }}
          selectedSpans={selectedSpans}
        />
      )}
      <RowComparisonSelection
        rowId={rowId}
        selectableExperimentsList={selectableExperimentsList}
        selectedExperimentId={comparisonExperiment?.id ?? undefined}
        onExperimentSelected={(id) => {
          setComparisonExperimentId(id);
        }}
        comparisonClassName={comparisonClassName}
        comparableRowIds={comparisonRowIds}
        comparisonRowId={comparisonRowId ?? ""}
        onCompareRowIdSelected={(id) => {
          const comparisonExperimentId = comparisonExperiment?.id;
          if (comparisonExperimentId) {
            setSelectedComparisonParams((prev) => ({
              ...prev,
              [comparisonKey]: {
                [comparisonExperimentId]: id,
              },
            }));
          }
        }}
      />
      {isDiffModeRowId(rowId) &&
        !isPrimaryRowPending &&
        !comparisonRowId &&
        (!isComparisonRowQueryEnabled || !isComparisonRowsPending) && (
          <div className="flex h-10 items-center gap-2 border-b px-4 text-xs text-primary-500">
            <TriangleAlert className="size-3" />
            There are no input matches in the comparison experiment. Diffs will
            not be shown for this row.
          </div>
        )}
      {!isEmpty(groupingData) && (
        <BasicTooltip
          tooltipContent={`Showing ${pluralizeWithCount(numRelatedRowIds ?? 1, "trace")} grouped
            by ${groupingField}: ${groupingValue}`}
          side="left"
        >
          <div className="flex h-10 items-center gap-2 border-b px-4 text-xs text-primary-700">
            <StretchHorizontal className="size-3 shrink-0" />
            <div className="max-w-[50%] flex-none truncate font-medium">
              {groupingValue}
            </div>
            <span className="flex-1 truncate text-right text-primary-500">
              {pluralizeWithCount(numRelatedRowIds ?? 1, "trace")} grouped by{" "}
              {groupingField}
            </span>
          </div>
        </BasicTooltip>
      )}
      {!isHumanReviewExpanded && (
        <div className="flex-1 overflow-hidden @container" ref={containerRef}>
          <ResizablePanelGroup
            direction="horizontal"
            autoSaveId="tracePanel"
            className="flex flex-1 overflow-hidden"
            debugProps={{
              windowInnerWidth: window.innerWidth,
              minSize: minTreePanelWidth,
              maxSize: maxTreePanelWidth,
            }}
          >
            {(showSearchResults ||
              (!isDatasetRow &&
                (!isTraceTreeCollapsed ||
                  viewType === "thread" ||
                  viewType === "timeline"))) && (
              <>
                <ResizablePanel
                  order={0}
                  className="relative flex"
                  minSize={minTreePanelWidth}
                  maxSize={viewType !== "trace" ? undefined : maxTreePanelWidth}
                  id="tree"
                >
                  <CollapsibleTraceTree
                    label={
                      viewType === "trace"
                        ? "Trace tree"
                        : viewType === "timeline"
                          ? "Timeline"
                          : "Thread"
                    }
                    comparisonClassName={comparisonClassName}
                    firstRootTitle={traceViewParams.title}
                    searchResults={
                      showSearchResults && (
                        <TraceSearchResults
                          virtualizerRef={traceSearchVirtualizer}
                          openCreateDatasetDialog={() =>
                            openCreateDatasetDialog("Untitled")
                          }
                          onBulkAddToDataset={onBulkAddToDataset}
                          onGoToField={({ spanId, rootSpanId, field }) => {
                            if (spanId !== selectedSpan.span_id) {
                              setSelectedSpan(spanId, rootSpanId);
                            }
                            setScrollTo({ spanId, v: field });
                          }}
                        />
                      )
                    }
                    selectedSpan={selectedSpan}
                    setSelectedSpan={setSelectedSpan}
                    setTraceTreeCollapsed={setTraceTreeCollapsed}
                    trace={selectedPreviewTrace}
                    objectId={objectId}
                    objectType={objectType}
                    allAvailableModelCosts={
                      expandedRowParams.allAvailableModelCosts
                    }
                    tracePaginationProps={tracePaginationProps}
                  />
                </ResizablePanel>
                {viewType === "trace" && (
                  <ResizableHandle className="bg-primary-200/70" />
                )}
              </>
            )}
            <ResizablePanel
              order={1}
              minSize={spanPanelMinSize}
              className={cn("flex relative", {
                hidden: viewType !== "trace",
              })}
              id="span"
            >
              <ScrollableContainerWithOptions
                containerRef={scrollableContainerRef}
                className="relative flex flex-1 flex-col overflow-y-auto p-3 @container"
              >
                {selectedSpan && (
                  <div className="flex flex-1 flex-col gap-3.5 text-sm">
                    {isDatasetRow ? null : (
                      <SpanHeader
                        isTraceTreeCollapsed={isTraceTreeCollapsed}
                        isSearchOpen={isSearchOpen}
                        searchQuery={searchQuery}
                        span={selectedSpan}
                        isReadOnly={isReadOnly}
                        performAddRowsToDataset={performAddRowsToDataset}
                        collapseSpanState={collapseSpanState}
                        setCollapseSpanState={setCollapseSpanState}
                        spanName={spanName}
                        copilotContext={copilotContext}
                        openCreateDatasetDialog={openCreateDatasetDialog}
                        expandedFrame={expandedFrame}
                        setExpandedFrame={setExpandedFrame}
                        expandedFrameRef={expandedFrameRef}
                        parsedSpanData={parsedSpanData}
                        parentSpanType={parentSpanType}
                      />
                    )}
                    {spanContents({
                      shouldRenderManualScores: !playXProps,
                      shouldRenderSpanDetails: true,
                    })}
                  </div>
                )}
              </ScrollableContainerWithOptions>
            </ResizablePanel>
            {isAttachmentBrowserOpen && selectedSpan && (
              <>
                <ResizableHandle className="hidden bg-primary-200/70 @4xl:flex" />
                <ResizablePanel
                  order={2}
                  id="attachments"
                  className="hidden flex-col @4xl:flex"
                  minSize={attachmentPanelMinSize}
                >
                  <AttachmentBrowser spanData={selectedSpan.data} />
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>
        </div>
      )}
      <ExpandedHumanReviewModal
        savingState={traceViewParams.savingState}
        isDatasetRow={isDatasetRow}
        expandedRowState={
          loadingState === "loaded" ? "fully_loaded" : "changing_rows"
        }
        actions={
          <>
            <div className="flex flex-wrap items-center gap-1">
              {tagsObject
                ?.filter((t) => !RESERVED_TAGS.includes(t))
                .map((tag, idx) => {
                  const tagConfig = projectConfig.tags.find(
                    (t) => t.name === tag,
                  );
                  return (
                    <Tag
                      key={idx}
                      label={tagConfig?.name ?? tag}
                      color={tagConfig?.color}
                      description={tagConfig?.description ?? undefined}
                    />
                  );
                })}
            </div>
            {assignSection}
            {tagSection}
            {expandedFrame && (
              <>
                <Button
                  Icon={Minimize2}
                  size="xs"
                  onClick={() => setExpandedFrame(null)}
                >
                  Minimize span iframe
                </Button>
                <RefreshIframeButton
                  spanIframe={expandedFrame}
                  expandedFrameRef={expandedFrameRef}
                  parsedSpanData={parsedSpanData}
                  variant="border"
                />
              </>
            )}
          </>
        }
        rowIndex={rowIndex}
        totalRows={totalRows}
        open={isHumanReviewExpanded}
        onOpenChange={(open) => {
          setHumanReviewState(open ? "1" : null);
        }}
        onPrevRow={onPrevRow}
        onNextRow={onNextRow}
        onJumpToRow={onJumpToRow}
        updateScores={onUpdateManualReviewScore}
        scores={manualScores}
        rootScores={rootManualScores}
        rowKey={selectedSpan?.data.id}
        spanName={selectedSpan?.data.span_attributes?.name}
        xactId={
          /* XXX Should create one for the score */
          selectedSpan?.data[TRANSACTION_ID_FIELD] ?? null
        }
        currentTraceViewers={currentTraceViewers}
        traceTree={
          selectedPreviewTrace.root.children.length > 0 ? (
            <CollapsibleTraceTree
              label={"Trace tree"}
              comparisonClassName={comparisonClassName}
              firstRootTitle={traceViewParams.title}
              selectedSpan={selectedSpan}
              setSelectedSpan={setSelectedSpan}
              setTraceTreeCollapsed={setHumanReviewTraceTreeCollapsed}
              trace={selectedPreviewTrace}
              objectId={objectId}
              objectType={objectType}
              allAvailableModelCosts={expandedRowParams.allAvailableModelCosts}
              tracePaginationProps={tracePaginationProps}
            />
          ) : undefined
        }
        isTraceTreeCollapsed={!!isHumanReviewTraceTreeCollapsed}
        setTraceTreeCollapsed={setHumanReviewTraceTreeCollapsed}
      >
        {spanName}
        {spanContents({
          shouldRenderManualScores: false,
          defaultCollapsedFields: HUMAN_REVIEW_DEFAULT_COLLAPSED_FIELDS,
        })}
      </ExpandedHumanReviewModal>
      {createDatasetModal}
      {manualConfirmationResolve && (
        <ConfirmationDialog
          open={!!manualConfirmationResolve}
          onOpenChange={(open) => {
            setManualConfirmationResolve(
              (prev: ((confirmed: boolean) => void) | null) => {
                if (!open && prev) {
                  prev(false);
                  return null;
                } else {
                  return prev;
                }
              },
            );
            if (!open) {
              disableScope(HotkeyScope.ConfirmationModal);
            }
          }}
          title="Overwrite expected field"
          confirmText="Overwrite"
          onConfirm={() => {
            manualConfirmationResolve(true);
            setManualConfirmationResolve(null);
          }}
        >
          The expected field is not empty. By setting this score, the expected
          field will be replaced by the score value. Are you sure you want to
          set the score as the expected value?
        </ConfirmationDialog>
      )}
    </>
  ) : null;

  return (
    <>
      <TraceHeader
        isDataset={isDatasetRow}
        onClose={onClose}
        onPrevRow={onPrevRow}
        onNextRow={onNextRow}
        rowId={loadingState === "loaded" ? (trace?.root.id ?? null) : null}
        rowIndex={rowIndex}
        onToggleSearch={
          loadingState === "loaded"
            ? () => {
                setSearchOpen(!isSearchOpen);
                setViewType("trace");
              }
            : undefined
        }
        hideNavButtons={!trace || loadingState === "not_found"}
      >
        {buildTraceError ? (
          <div className="grow"></div>
        ) : loadingState === "loaded" || isHumanReviewExpanded ? (
          <>
            {currentViewersSection}
            {assignSection}
            {tagSection}
          </>
        ) : loadingState === "loading" ? (
          <Skeleton className="mx-2 h-7 grow" />
        ) : loadingState === "not_found" ? (
          <div className="grow"></div>
        ) : null}
      </TraceHeader>
      {buildTraceError ? (
        <div className="flex flex-1 p-4">
          <TableEmptyState
            label={`No root span was found in trace for id ${primaryRowId}`}
            labelClassName="text-sm"
            className="flex-1 justify-center"
            Icon={SearchSlash}
          />
        </div>
      ) : loadingState === "loaded" || isHumanReviewExpanded ? (
        <GoToOriginProvider origin={selectedSpan?.data.origin}>
          {traceContent}
        </GoToOriginProvider>
      ) : loadingState === "loading" ? (
        <div className="flex flex-col gap-2 p-4">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      ) : loadingState === "not_found" ? (
        <div className="flex flex-1 p-4">
          <TableEmptyState
            label={
              isProcessing
                ? `This row is being processed. Please check back again soon.`
                : `No row found with id ${primaryRowId}`
            }
            labelClassName="text-sm"
            className="flex-1 justify-center"
            Icon={isProcessing ? Hourglass : SearchSlash}
          />
        </div>
      ) : null}
    </>
  );
}

export type traceCollapseState =
  | { state: "expanded" }
  | { state: "collapsed"; ids: Set<string> }
  | { state: "mixed"; ids: Set<string> };

export function CollapsibleTraceTree({
  comparisonClassName,
  firstRootTitle,
  searchResults,
  selectedSpan,
  setSelectedSpan,
  setTraceTreeCollapsed,
  trace,
  objectId,
  objectType,
  allAvailableModelCosts,
  label = "Trace",
  showLayoutDropdown,
  tracePaginationProps,
}: {
  comparisonClassName: string;
  firstRootTitle?: string;
  searchResults?: React.ReactNode;
  selectedSpan: Span | null;
  setSelectedSpan: (span: Span) => void;
  setTraceTreeCollapsed: (isCollapsed: boolean) => void;
  trace: Trace | null;
  objectId?: string;
  objectType?: DataObjectType;
  allAvailableModelCosts?: Record<string, ModelCosts>;
  label?: ReactNode;
  showLayoutDropdown?: boolean;
  tracePaginationProps?: TracePaginationProps;
}) {
  const { projectId } = useContext(ProjectContext);
  const [viewType, setViewType] = useTraceViewTypeState(projectId);
  const isTimelineView = viewType === "timeline";
  const isThreadView = viewType === "thread";
  const traces = useMemo(() => {
    return tracePaginationProps?.traces &&
      tracePaginationProps.traces.length > 0
      ? tracePaginationProps.traces
      : trace
        ? [trace]
        : [];
  }, [tracePaginationProps?.traces, trace]);

  const [collapseState, setCollapseState] = useState<traceCollapseState>({
    state: "expanded",
  });
  const collapseTraceHandler = useCallback(() => {
    if (collapseState.state === "mixed" || collapseState.state === "expanded") {
      if (traces.length === 0) return;

      const allIds = new Set<string>();
      const collectIds = (spans: Span[]) => {
        spans.forEach((span) => {
          if (span.children.length > 0) {
            allIds.add(span.id);
            collectIds(span.children);
          }
        });
      };
      collectIds(traces.map((t) => t.root));

      setCollapseState({ state: "collapsed", ids: allIds });
    } else if (collapseState.state === "collapsed") {
      setCollapseState({ state: "expanded" });
    }
  }, [collapseState, traces]);

  const [shouldShowMetrics, setShouldShowMetricsState] = useState(true);
  const canShowMetrics = traces.every((t) => !isEmpty(t.root.data.metrics));
  const showMetrics = shouldShowMetrics && canShowMetrics;

  const { traceStart, totalDuration } = getTraceDurations({
    traces,
    calculateTotalDuration: showMetrics || isTimelineView,
  });

  const containerRef = useRef<HTMLDivElement>(null);

  // Conditionally render the trace tree within this component, so that state (like collapseState) is preserved
  // when traces change and are potentially null during the change.
  if (traces.length === 0) return null;
  if (searchResults) return searchResults;

  return (
    <ScrollableContainerWithOptions
      className={cn("flex-1 overflow-y-auto pt-3", {
        "overflow-hidden flex p-0": isThreadView,
      })}
      containerRef={containerRef}
      enableScrollToBottom={isThreadView}
      enableScrollToTop={false}
      hideOptions
    >
      <div
        className={cn(
          "flex h-10 items-center gap-0.5 px-3 pb-3 bg-background",
          {
            "pb-0": isTimelineView,
            hidden: isThreadView,
          },
        )}
      >
        {(showLayoutDropdown || !isThreadView) && (
          <div className="grow text-xs">
            {showLayoutDropdown ? <TraceLayoutDropdown label={label} /> : label}
          </div>
        )}
        {canShowMetrics && !isTimelineView && !isThreadView && (
          <ActionButton
            hotkey="m"
            buttonVariant="ghost"
            actionHandler={() => setShouldShowMetricsState((prev) => !prev)}
            tooltipText={`${showMetrics ? "Hide" : "Show"} metrics`}
            className="text-primary-400"
            icon={
              showMetrics ? (
                <Timer className="size-3" />
              ) : (
                <TimerOff className="size-3" />
              )
            }
          />
        )}
        {!isThreadView && (
          <ActionButton
            hotkey="."
            buttonVariant="ghost"
            actionHandler={collapseTraceHandler}
            tooltipText={
              collapseState.state === "mixed" ||
              collapseState.state === "expanded"
                ? `Collapse all`
                : `Expand all`
            }
            className="text-primary-400"
            icon={
              collapseState.state === "mixed" ||
              collapseState.state === "expanded" ? (
                <FoldVertical className="size-3" />
              ) : (
                <UnfoldVertical className="size-3" />
              )
            }
          />
        )}
        {!isTimelineView && !isThreadView && (
          <ActionButton
            key="collapse-trace"
            hotkey="\"
            buttonVariant="ghost"
            actionHandler={() => setTraceTreeCollapsed(true)}
            tooltipText="Hide trace tree"
            className="text-primary-400"
            icon={<PanelLeft className="size-3" />}
          />
        )}
      </div>
      {viewType === "thread" ? (
        <ErrorBoundary
          fallback={
            <ErrorBanner skipErrorReporting>
              There was a problem rendering this thread
            </ErrorBanner>
          }
        >
          {objectId && objectType ? (
            <PaginatedTraceThreadView
              traces={traces}
              setSelectedSpan={setSelectedSpan}
              setViewType={setViewType}
              containerRef={containerRef}
              objectType={objectType}
              objectId={objectId}
              allAvailableModelCosts={allAvailableModelCosts}
              tracePaginationProps={tracePaginationProps}
            />
          ) : trace ? (
            <TraceThreadView
              trace={trace}
              setSelectedSpan={setSelectedSpan}
              setViewType={setViewType}
              containerRef={containerRef}
              allAvailableModelCosts={allAvailableModelCosts}
            />
          ) : null}
        </ErrorBoundary>
      ) : (
        <TraceTree
          traces={traces}
          containerRef={containerRef}
          seen={new Set()}
          comparisonClassName={comparisonClassName}
          selectedSpan={selectedSpan}
          setSelectedSpan={setSelectedSpan}
          firstRootTitle={firstRootTitle}
          showMetrics={showMetrics || isTimelineView}
          collapseState={collapseState}
          onCollapseStateChange={setCollapseState}
          totalDuration={totalDuration}
          traceStart={traceStart}
          tracePaginationProps={tracePaginationProps}
        />
      )}
    </ScrollableContainerWithOptions>
  );
}

function comparisonExpandedRowParams({
  rowId,
  selectedRowId,
  expandedRowParams,
  selectedExperimentIndex,
  makeDynamicObjectId,
  comparisonDynamicObjectId,
}: {
  rowId: TraceProps["rowId"];
  selectedRowId: string | null | undefined;
  expandedRowParams: ExpandedRowParams;
  selectedExperimentIndex: number;
  makeDynamicObjectId: boolean;
  comparisonDynamicObjectId?: string | null;
}) {
  const { multiExperimentData } = expandedRowParams;
  if (!isDiffModeRowId(rowId) || !multiExperimentData) {
    return {
      rowId: null,
      scan: null,
      ready: [],
      channel: null,
      dynamicObjectId: null,
    };
  }

  return {
    rowId:
      selectedRowId ??
      rowId[diffKeynameForIndex(selectedExperimentIndex + 1, true)] ??
      null,
    scan: multiExperimentData.comparisonExperimentData[selectedExperimentIndex]
      ?.scan,
    ready: [
      multiExperimentData.comparisonExperimentData[selectedExperimentIndex]
        ?.refreshed ?? 0,
    ],
    dynamicObjectId: makeDynamicObjectId
      ? (comparisonDynamicObjectId ??
        multiExperimentData.comparisonExperimentData[selectedExperimentIndex]
          ?.id ??
        null)
      : null,
    channel: null,
    customColumnsParams: {
      scan:
        multiExperimentData?.comparisonExperimentData[selectedExperimentIndex]
          ?.tableScan ?? null,
      scanQueryKeys:
        multiExperimentData?.comparisonExperimentData[selectedExperimentIndex]
          ?.queryKeys ?? emptyArray,
      customColumns:
        multiExperimentData?.comparisonExperimentData[selectedExperimentIndex]
          ?.customColumns,
    },
  };
}

export interface ManualScoreUpdate {
  name: string;
  category: string | string[] | null;
  value: string | number | null;
}
