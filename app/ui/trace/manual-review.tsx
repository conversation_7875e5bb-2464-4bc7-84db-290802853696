import { type TransactionId } from "#/utils/duckdb";
import {
  forwardRef,
  useCallback,
  useImperative<PERSON>andle,
  useMemo,
  useState,
} from "react";
import { ToggleGroup, ToggleGroupItem } from "#/ui/toggle-group";
import { useOptimisticState } from "#/utils/optimistic-update";
import { type SpanScore, type ConfiguredScore } from "./graph";
import { isEmpty } from "#/utils/object";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  BasicTooltip,
} from "#/ui/tooltip";
import { MarkdownViewer } from "#/ui/markdown";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { cn } from "#/utils/classnames";
import { type ProjectScoreCategory } from "@braintrust/typespecs";
import { average, formatScoreValue, Percent } from "./diff-score-object";
import { formatScoreLabel } from "#/app/app/[org]/p/[project]/configuration/configure-score-modal";
import { FreeFormTextArea } from "./free-form-text-area";
import { UncontrolledNumericSlider } from "#/ui/numeric-slider";
import { type ManualScoreUpdate } from "./trace";

export function ManualReview({
  scores,
  rootScores,
  rowKey,
  xactId,
  updateScores,
}: {
  scores: Record<string, ConfiguredScore>;
  rootScores?: Record<string, SpanScore>;
  rowKey: string;
  xactId: TransactionId;
  updateScores?: (
    scoreUpdates: ManualScoreUpdate[],
  ) => Promise<(TransactionId | null)[]>;
}) {
  const updateScore = useMemo(
    () =>
      updateScores
        ? async (
            scoreUpdate: ManualScoreUpdate,
          ): Promise<TransactionId | null> =>
            (await updateScores([scoreUpdate]))?.[0] ?? null
        : undefined,
    [updateScores],
  );

  return (
    <>
      {Object.values(scores).map((score) => {
        const value = average(score.right);

        return (
          <div key={score.config.name} className="flex w-full flex-col gap-2">
            <div className="flex items-center justify-between">
              <Tooltip delayDuration={200} disableHoverableContent>
                <TooltipTrigger asChild>
                  <div className="w-full break-words text-xs text-primary-500">
                    {score.config.name}
                  </div>
                </TooltipTrigger>
                {score.config.description && (
                  <TooltipPortal>
                    <TooltipContent className="tex-sm z-30 max-w-sm py-0 text-primary-800">
                      <MarkdownViewer value={score.config.description} />
                    </TooltipContent>
                  </TooltipPortal>
                )}
              </Tooltip>
              {rootScores?.[score.config.name]?.right && (
                <RootScorePercent
                  name={score.config.name}
                  values={rootScores[score.config.name].right}
                />
              )}
            </div>
            <div className="flex w-full text-xs">
              {score.config.score_type === "slider" ? (
                <UncontrolledNumericSlider
                  title={score.config.name}
                  value={!isEmpty(value) ? value * 100 : undefined}
                  setValue={async (v) =>
                    updateScore
                      ? await updateScore({
                          name: score.config.name,
                          category: null,
                          value: v !== undefined ? v / 100 : null,
                        })
                      : null
                  }
                  min={0}
                  max={100}
                  step={1}
                  unit="%"
                  compact
                  disabled={updateScore === undefined}
                  rowKey={rowKey}
                  xactId={xactId}
                />
              ) : score.config.score_type === "categorical" &&
                score.config.config?.destination === "expected" ? (
                <UncontrolledCategoricalToggleGroup
                  score={score}
                  rowKey={rowKey}
                  xactId={xactId}
                  isMultiSelect={!!score.config.config?.multi_select}
                  updateCategories={
                    updateScore === undefined
                      ? undefined
                      : (n) =>
                          updateScore({
                            name: score.config.name,
                            category: n,
                            value: null,
                          })
                  }
                />
              ) : score.config.score_type === "categorical" ? (
                <ScoreToggleGroup
                  score={score}
                  rowKey={rowKey}
                  xactId={xactId}
                  updateScore={
                    updateScore === undefined
                      ? undefined
                      : (n, v) =>
                          updateScore({
                            name: score.config.name,
                            category: n,
                            value: v,
                          })
                  }
                />
              ) : score.config.score_type === "free-form" ? (
                <FreeFormTextArea
                  score={score}
                  rowKey={rowKey}
                  xactId={xactId}
                  onAutoSave={
                    updateScore === undefined
                      ? undefined
                      : (n) =>
                          updateScore({
                            name: score.config.name,
                            category: null,
                            value: n,
                          })
                  }
                />
              ) : (
                `Invalid score (${score.config.score_type})`
              )}
            </div>
          </div>
        );
      })}
    </>
  );
}

export interface ScoreToggleGroupHandle {
  save(matchingCategory?: ProjectScoreCategory): Promise<void>;
}

interface ScoreToggleGroupProps {
  score: ConfiguredScore;
  rowKey?: string;
  xactId: TransactionId | null;
  groupClassName?: string;
  itemClassName?: string;
  selectedItemClassName?: string;
  selectedItemKeyboardShortcutClassName?: string;
  showKeyboardShortcuts?: boolean;
  updateScore?: (
    category: string | null,
    value: number | null,
  ) => Promise<TransactionId | null>;
}

export const ScoreToggleGroup = forwardRef<
  ScoreToggleGroupHandle,
  ScoreToggleGroupProps
>(
  (
    {
      score,
      rowKey,
      xactId,
      updateScore,
      groupClassName,
      itemClassName,
      selectedItemClassName,
      selectedItemKeyboardShortcutClassName,
      showKeyboardShortcuts,
    },
    ref,
  ) => {
    const categories = useMemo(
      () =>
        score.config.score_type === "categorical"
          ? score.config.categories
          : [],
      [score.config.categories, score.config.score_type],
    );

    const [selectedValue, setSelectedValue] = useState<number | null>(
      average(score.right),
    );

    const optimisticSave = useCallback(
      async (value: number | null) => {
        const matchingCategory =
          value !== null
            ? categories.find((c) => c.value === value)
            : undefined;

        setSelectedValue(value);
        return (
          (await updateScore?.(matchingCategory?.name ?? null, value)) ?? null
        );
      },
      [categories, updateScore],
    );

    const { save } = useOptimisticState({
      xactId,
      value: average(score.right),
      save: optimisticSave,
      rowKey,
      onUpdatedValue: useCallback(
        (v: number | null) => setSelectedValue(v),
        [setSelectedValue],
      ),
    });

    const categoryValue = categories.find(
      (c) => c.value === selectedValue,
    )?.name;

    useImperativeHandle(
      ref,
      () => ({
        save: async (matchingCategory?: ProjectScoreCategory) => {
          await save(matchingCategory?.value ?? null);
        },
      }),
      [save],
    );

    const groupClass = cn("flex-wrap justify-start", groupClassName);
    const items = (
      <>
        {categories.map((category, idx) => (
          <ToggleGroupItem
            value={category.name}
            key={category.name}
            aria-label={category.name}
            className={cn(
              "h-auto min-h-8 px-2 text-xs items-center font-medium",
              itemClassName,
              {
                [selectedItemClassName ?? ""]: category.name === categoryValue,
              },
            )}
            size="dynamic"
          >
            {formatScoreLabel(category.name)}
            {showKeyboardShortcuts && idx < 10 && (
              <span
                className={cn(
                  "ml-2 text-xs text-primary-400 transition-colors",
                  {
                    [selectedItemKeyboardShortcutClassName ??
                    "text-primary-600"]: category.name === categoryValue,
                  },
                )}
              >
                {idx === 9 ? "0" : idx + 1}
              </span>
            )}
          </ToggleGroupItem>
        ))}
      </>
    );

    return (
      <ToggleGroup
        type="single"
        className={groupClass}
        disabled={updateScore === undefined}
        // never send undefined so that we keep the ToggleGroup as a controlled component
        value={categoryValue ?? ""}
        onValueChange={async (v) => {
          const matchingCategory = categories.find((c) => c.name === v);
          await save(matchingCategory?.value ?? null);
        }}
      >
        {items}
      </ToggleGroup>
    );
  },
);
ScoreToggleGroup.displayName = "ScoreToggleGroup";

export interface CategoricalToggleGroupHandle {
  save(matchingCategory?: ProjectScoreCategory): Promise<void>;
}

interface CategoricalToggleGroupProps {
  score: ConfiguredScore;
  isMultiSelect: boolean;
  rowKey?: string;
  xactId: TransactionId | null;
  groupClassName?: string;
  itemClassName?: string;
  selectedItemClassName?: string;
  selectedItemKeyboardShortcutClassName?: string;
  showKeyboardShortcuts?: boolean;
  updateCategories?: (
    category: string | string[] | null,
  ) => Promise<TransactionId | null>;
}

export const UncontrolledCategoricalToggleGroup = forwardRef<
  CategoricalToggleGroupHandle,
  CategoricalToggleGroupProps
>(
  (
    {
      score,
      isMultiSelect,
      rowKey,
      xactId,
      updateCategories,
      groupClassName,
      itemClassName,
      selectedItemClassName,
      selectedItemKeyboardShortcutClassName,
      showKeyboardShortcuts,
    },
    ref,
  ) => {
    const categories = useMemo(
      () =>
        score.config.score_type === "categorical"
          ? score.config.categories
          : [],
      [score.config.categories, score.config.score_type],
    );

    const memoizedExpected = useMemo(() => score.expected ?? [], [score]);

    const [selectedValues, setSelectedValues] =
      useState<string[]>(memoizedExpected);

    const optimisticSave = useCallback(
      async (newCategories: string[]) => {
        const prevCategories = [...selectedValues];
        setSelectedValues(newCategories);

        if (updateCategories) {
          const ret = await updateCategories(
            newCategories.length === 0
              ? null
              : isMultiSelect
                ? newCategories
                : newCategories[0],
          );
          if (ret === null) {
            setSelectedValues(prevCategories);
          }
          return ret;
        } else {
          return null;
        }
      },
      [isMultiSelect, selectedValues, updateCategories],
    );

    const { save } = useOptimisticState({
      xactId,
      value: memoizedExpected,
      save: optimisticSave,
      rowKey,
      onUpdatedValue: setSelectedValues,
    });

    useImperativeHandle(
      ref,
      () => ({
        save: async (matchingCategory?: ProjectScoreCategory) => {
          if (!matchingCategory) {
            await save([]);
          } else if (isMultiSelect) {
            const newCategories = selectedValues.includes(matchingCategory.name)
              ? selectedValues.filter((v) => v !== matchingCategory.name)
              : [...selectedValues, matchingCategory.name];
            await save(newCategories);
          } else if (
            selectedValues.length === 1 &&
            selectedValues[0] === matchingCategory.name
          ) {
            await save([]);
          } else {
            await save([matchingCategory.name]);
          }
        },
      }),
      [isMultiSelect, save, selectedValues],
    );

    return (
      <CategoricalToggleGroup
        categories={categories}
        isMultiSelect={isMultiSelect}
        groupClassName={groupClassName}
        itemClassName={itemClassName}
        selectedItemClassName={selectedItemClassName}
        selectedItemKeyboardShortcutClassName={
          selectedItemKeyboardShortcutClassName
        }
        showKeyboardShortcuts={showKeyboardShortcuts}
        disabled={updateCategories === undefined}
        value={selectedValues}
        onChange={async (v) => {
          if (isMultiSelect) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            await save(v as string[]);
          } else {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            await save(v.length > 0 ? [v as string] : []);
          }
        }}
      />
    );
  },
);
UncontrolledCategoricalToggleGroup.displayName =
  "UncontrolledCategoricalToggleGroup";

export const CategoricalToggleGroup = ({
  categories,
  isMultiSelect,
  groupClassName,
  itemClassName,
  selectedItemClassName = "",
  selectedItemKeyboardShortcutClassName,
  showKeyboardShortcuts,
  disabled,
  onChange,
  value,
}: {
  categories: ProjectScoreCategory[];
  isMultiSelect: boolean;
  groupClassName?: string;
  itemClassName?: string;
  selectedItemClassName?: string;
  selectedItemKeyboardShortcutClassName?: string;
  showKeyboardShortcuts?: boolean;
  disabled?: boolean;
  value: string[];
  onChange: (values: string[] | string) => void;
}) => {
  const groupClass = cn("flex-wrap justify-start", groupClassName);
  const items = (
    <>
      {categories.map((category, idx) => (
        <ToggleGroupItem
          value={category.name}
          key={category.name}
          aria-label={category.name}
          className={cn(
            "h-auto min-h-8 px-2 text-xs items-center font-medium border",
            itemClassName,
            {
              [selectedItemClassName]: value.includes(category.name),
            },
          )}
          size="dynamic"
        >
          {formatScoreLabel(category.name)}
          {showKeyboardShortcuts && idx < 10 && (
            <span
              className={cn("ml-2 text-xs text-primary-400 transition-colors", {
                [selectedItemKeyboardShortcutClassName ?? "text-primary-600"]:
                  value.includes(category.name),
              })}
            >
              {idx === 9 ? "0" : idx + 1}
            </span>
          )}
        </ToggleGroupItem>
      ))}
    </>
  );

  if (isMultiSelect) {
    return (
      <ToggleGroup
        type="multiple"
        className={groupClass}
        disabled={disabled}
        value={value}
        onValueChange={onChange}
        tabIndex={0}
      >
        {items}
      </ToggleGroup>
    );
  } else {
    return (
      <ToggleGroup
        type="single"
        className={groupClass}
        disabled={disabled}
        value={value[0] ?? ""}
        onValueChange={onChange}
      >
        {items}
      </ToggleGroup>
    );
  }
};

export function RootScorePercent({
  className,
  name,
  values,
}: {
  className?: string;
  name: string;
  values: (number | null)[] | null;
}) {
  if (
    !values ||
    values.every(
      (v) =>
        v == null ||
        // only show the value if there is more than one score to be averaged
        values.length <= 1,
    )
  ) {
    return null;
  }
  const value = average(values.filter((v) => v != null));
  if (!value) {
    return null;
  }
  return (
    <BasicTooltip
      delayDuration={200}
      disableHoverableContent
      tooltipContent={
        <span className={cn("text-primary-500", className)}>
          <span className="font-medium text-primary-700">{name}</span> has an
          average value of{" "}
          <span className="font-medium text-primary-700">
            {formatScoreValue(value)}
          </span>{" "}
          across all spans in this trace
        </span>
      }
    >
      <div className="flex-none text-xs text-primary-300">
        <Percent value={value} />
      </div>
    </BasicTooltip>
  );
}
