import { type AliasExpr } from "@braintrust/btql/parser";
import { type BtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type FetchBtqlOptions, fetchBtqlPaginated } from "#/utils/btql/btql";
import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  type SpanOverviewRow,
  baseSpanOverviewSchema,
  spanOverviewSchema,
  parseScores,
} from "./schema-utils";
import { type CustomColumn } from "#/utils/custom-columns/use-custom-columns";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";
import { type SpanMetrics } from "@braintrust/local";
import {
  fillSpanScores,
  fillSpanMetrics,
  type PreviewSpan,
  sortSpanChildren,
  topSortSpans,
  type PreviewTrace,
} from "#/ui/trace/graph";
import { type ModelCosts } from "#/ui/prompts/models";

const spanOverviewExprs = new Map([["model", "metadata.model"]]);

export function makeSpanOverviewProjection({
  builder,
  objectType,
}: {
  builder: BtqlQueryBuilder;
  objectType: DataObjectType;
}): AliasExpr[] {
  const schema =
    objectType === "dataset" ? baseSpanOverviewSchema : spanOverviewSchema;
  const aliases = Object.keys(schema.shape).filter(
    (name) => !spanOverviewExprs.has(name),
  );
  return aliases
    .map(
      (alias): AliasExpr => ({
        alias,
        expr: builder.ident(alias),
      }),
    )
    .concat(
      Array.from(spanOverviewExprs).map(
        ([alias, expr]): AliasExpr => ({
          alias,
          expr: { btql: expr },
        }),
      ),
    );
}

export type TraceQueryKeyParams = {
  objectType: DataObjectType;
  objectId: string | null;
  traceRowId: string | null;
};

export function makeFullSpanQueryKey(
  objectType: DataObjectType | undefined,
  objectId: string | undefined,
  projectId: string | null,
  rowId: string | null,
  isRoot?: boolean,
  customColumns?: CustomColumn[] | CustomColumnDefinition[],
) {
  return [
    "fullSpan",
    objectType,
    objectId,
    projectId,
    rowId,
    ...(isRoot ? [true] : []),
    ...(customColumns ? customColumns.map((c) => [c.name, c.expr]) : []),
  ];
}

export function buildVirtualTrace(
  rows: SpanOverviewRow[],
  rootRowId: string | null,
  modelCosts: Record<string, ModelCosts> | undefined,
): { trace: PreviewTrace | null; error: Error | null } {
  if (rows.length === 0) {
    return { trace: null, error: null };
  }

  let root: PreviewSpan | null = null;
  for (const row of rows) {
    if (rootRowId == null) {
      if ((row.span_parents ?? []).length === 0) {
        console.assert(root == null, "root already set");
        root = makePreviewSpanFromRow(row);
      }
    } else {
      if (rootRowId === row.id || rootRowId === row.span_id) {
        root = makePreviewSpanFromRow(row);
        continue;
      }
    }
  }

  if (!root) {
    console.warn("root span not found", rows);
    return { trace: null, error: new Error("Root span not found") };
  }

  const spans: Record<string, PreviewSpan> = { [root.span_id]: root };
  for (const row of rows) {
    if (root.root_span_id !== row.root_span_id) {
      // This should not be possible because the caller of this function should be
      // filtering for rows that match one of the root span rows, so the root
      // span row itself must exist.
      // However, we have seen this happen in the wild, so we handle it here.
      console.warn(
        `root span id ${row.root_span_id} for span ${row.span_id} does not match root span ${root.span_id}. All span ids: ${JSON.stringify(Object.keys(spans), null, 2)}`,
      );
      continue;
    }
    if (row.span_id === root.span_id) {
      continue;
    }

    const span = makePreviewSpanFromRow(row);
    spans[row.span_id] = span;
  }

  const sortedSpans = topSortSpans(spans);
  const topSortIndex: Map<string, number> = new Map();
  for (const [idx, span] of sortedSpans.entries()) {
    topSortIndex.set(span.span_id, idx);
  }

  for (const row of rows) {
    const span = spans[row.span_id];
    if (!span) {
      continue;
    }
    const span_parents = span.span_parents ?? [];
    if (span_parents.length > 0) {
      // Even though a span can technically have multiple parents, this
      // functionality is not used anywhere so we only handle the case of a
      // single parent.
      if (span_parents.length > 1) {
        console.warn("Only using first parent of span", row.span_id);
      }

      if (
        topSortIndex.get(span_parents[0])! > topSortIndex.get(span.span_id)! ||
        span.span_id === span_parents[0]
      ) {
        console.warn(
          `Cycle detected in span graph at span ${span.span_id}. Skipping its parent.`,
        );
        continue;
      }

      // If we find the actual parent span, use it and set `span.parent_span_id`
      // to point to it. Otherwise, we have an orphan span which we put
      // underneath the root_span for display purposes, but we don't set
      // `span.parent_span_id` to indicate that it's an orphan.
      const parentSpan = (() => {
        const parent_span_id = span_parents[0];
        if (parent_span_id in spans) {
          const parentSpan = spans[parent_span_id];
          span.parent_span_id = parentSpan.span_id;
          return parentSpan;
        } else {
          console.warn(
            `parent span ${parent_span_id} not found for span ${span.span_id}. Using root span ${root.span_id} instead`,
          );
          return root;
        }
      })();
      parentSpan.children.push(span);
    } else if (span.id !== root.id) {
      root.children.push(span);
    }
  }

  const colored: Record<string, boolean> = {};
  for (const span of Object.values(spans)) {
    fillSpanScores(span, colored);
  }

  const metricsColored: Record<string, boolean> = {};
  for (const span of Object.values(spans)) {
    fillSpanMetrics(span, metricsColored, modelCosts);
  }

  // For each span, sort its children by start time
  for (const span of Object.values(spans)) {
    sortSpanChildren(span);
  }

  return { trace: { root, spans }, error: null };
}

export function makeTraceQueryKey({
  objectType,
  objectId,
  traceRowId,
}: TraceQueryKeyParams) {
  return ["virtualTraceSpanTree", objectType, objectId, traceRowId];
}

export type TraceQueryResult = {
  id: string | null;
  queryData: Awaited<ReturnType<typeof fetchBtqlPaginated<SpanOverviewRow>>>;
};

// NOTE: longer term we want to move to r=root_span_id which is better for querying rows
export function makeTraceQueryFn(
  queryKeyParams: TraceQueryKeyParams,
  builder: BtqlQueryBuilder,
  btqlOptions: FetchBtqlOptions,
) {
  return async ({
    signal,
  }: {
    signal: AbortSignal;
  }): Promise<TraceQueryResult> => {
    const selectProjection = makeSpanOverviewProjection({
      builder,
      objectType: queryKeyParams.objectType,
    });

    const idQuery = await fetchBtqlPaginated(
      {
        args: {
          query: {
            filter: {
              op: "eq",
              left: { btql: "id" },
              right: {
                op: "literal",
                value: queryKeyParams.traceRowId,
              },
            },
            from: builder.from(
              queryKeyParams.objectType,
              queryKeyParams.objectId ? [queryKeyParams.objectId] : [],
              "traces",
            ),
            select: selectProjection,
            limit: 1,
          },
          brainstoreRealtime: true,
        },
        ...btqlOptions,
        schema: spanOverviewSchema,
        signal,
      },
      1000,
    );
    if (idQuery.data.length === 0) {
      // Fallback to support root_span_id as the url param
      const rootSpanIdQuery = await fetchBtqlPaginated(
        {
          args: {
            query: {
              filter: {
                op: "eq",
                left: { btql: "root_span_id" },
                right: {
                  op: "literal",
                  value: queryKeyParams.traceRowId,
                },
              },
              from: builder.from(
                queryKeyParams.objectType,
                queryKeyParams.objectId ? [queryKeyParams.objectId] : [],
                "traces",
              ),
              select: selectProjection,
              limit: 1,
            },
            brainstoreRealtime: true,
          },
          ...btqlOptions,
          schema: spanOverviewSchema,
          signal,
        },
        1000,
      );
      if (rootSpanIdQuery.data.length > 0) {
        return { id: queryKeyParams.traceRowId, queryData: rootSpanIdQuery };
      }
    }
    return { id: queryKeyParams.traceRowId, queryData: idQuery };
  };
}

function makePreviewSpanFromRow(row: SpanOverviewRow): PreviewSpan {
  return {
    id: row.id,
    span_id: row.span_id,
    root_span_id: row.root_span_id,
    span_parents: row.span_parents ?? [],
    parent_span_id: null,
    scores: parseScores({
      span: { span_id: row.span_id },
      scores: row?.scores,
    }),
    data: {
      ...row,
      span_attributes: {
        ...row.span_attributes,
        name: row.span_attributes?.name ?? "",
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      metrics: row.metrics as
        | (SpanMetrics & Record<string, number>)
        | undefined,
      scores: row.scores ?? {},
      tags: row.tags ?? [],
      error: row.error ?? undefined,
      origin: row.origin ?? undefined,
      model: row.model ?? undefined,
    },
    children: [],
    _pagination_key: row._pagination_key ?? "",
  };
}
