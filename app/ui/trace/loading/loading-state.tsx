import {
  getSpanBySpanId,
  type LoadedTrace,
  type PreviewSpan,
  type PreviewTrace,
} from "#/ui/trace/graph";
import {
  DiffRightField,
  isDiffObject,
  type RowId,
} from "#/utils/diffs/diff-objects";
import { type Span } from "@braintrust/local";

export type LoadingState = "loading" | "not_found" | "loaded";

export function calculateLoadingState({
  isTraceQueryLoading,
  hasNoLoadedTraceData,
  primaryTrace,
  comparisonTrace,
  primaryRowId,
  loadedSpan,
  isConfigLoading,
  relatedRowIds,
}: {
  isTraceQueryLoading?: boolean;
  hasNoLoadedTraceData?: boolean;
  primaryTrace?: PreviewTrace | null;
  comparisonTrace?: PreviewTrace | null;
  /** row id or root span id */
  primaryRowId?: string | null;
  loadedSpan?: Span | null;
  isConfigLoading?: boolean;
  relatedRowIds?: { id: string; root_span_id: string }[];
}): LoadingState {
  if (isTraceQueryLoading || isConfigLoading) {
    return "loading";
  }

  if (hasNoLoadedTraceData) {
    return "not_found";
  }

  const span = getSpanBySpanId({
    spanId: loadedSpan?.span_id,
    spanIdsMap: {},
    spans: Object.fromEntries([
      ...Object.entries(primaryTrace?.spans ?? {}),
      ...Object.entries(comparisonTrace?.spans ?? {}),
    ]),
  });
  if (
    primaryTrace &&
    isValidRootId({
      rowId: primaryRowId,
      rootSpan: primaryTrace.root,
      relatedRowIds,
    }) &&
    (span ||
      relatedRowIds?.some(
        ({ root_span_id }) => root_span_id === loadedSpan?.root_span_id,
      ))
  ) {
    return "loaded";
  }
  return "loading";
}

/**
 *
 * @param rowId row id or root span id
 * @param trace
 * @returns
 */
export function isValidRootId({
  rowId,
  rootSpan,
  relatedRowIds,
}: {
  /** row id or root span id */
  rowId?: string | null;
  rootSpan: Span | PreviewSpan | null | undefined;
  relatedRowIds?: { id: string; root_span_id: string }[];
}) {
  if (!rowId || !rootSpan) {
    return false;
  }

  return (
    rowId === rootSpan.id ||
    ((rootSpan.span_parents ?? []).length === 0 &&
      rowId === rootSpan.root_span_id) ||
    relatedRowIds?.some(
      ({ id, root_span_id }) => id === rowId || root_span_id === rowId,
    )
  );
}

export function isPrimarySpanLoaded({
  primaryLoadedSpan,
  primaryTrace,
  primaryRowId,
  activeSpanId,
  relatedRowIds,
}: {
  primaryLoadedSpan: Span | undefined;
  primaryTrace: PreviewTrace | null;
  primaryRowId: string | undefined;
  activeSpanId: string | null;
  relatedRowIds?: { id: string; root_span_id: string }[];
}) {
  if (!activeSpanId || !primaryTrace?.spans[activeSpanId ?? ""]) {
    // if the span_id in the url isn't provided or doesn't match the trace
    // then we used the primary row id to load the span to load the root span
    return isValidRootId({
      rowId: primaryRowId,
      rootSpan: primaryLoadedSpan,
      relatedRowIds,
    });
  }
  if (primaryLoadedSpan && primaryLoadedSpan.data.span_id === activeSpanId) {
    return true;
  }

  return false;
}

/**
 * Resolve row id from loaded trace if it needs to be updated.
 * Since we can have both row id and root_span_id for the primary row id,
 * we can normalize the url row id based on the loaded trace.
 * @param existingRowId the existing row id from the url
 * @param loadedTrace
 * @returns a resolved row id, null otherwise
 */
export function resolvePrimaryRowIdForUpdate({
  existingRowId,
  loadedTrace,
}: {
  existingRowId: RowId | null;
  loadedTrace: LoadedTrace | null;
}) {
  if (!loadedTrace) {
    return null;
  }

  if (isDiffObject(existingRowId)) {
    return {
      ...existingRowId,
      [DiffRightField]: loadedTrace.root.id,
    };
  }

  if (existingRowId === loadedTrace.root.id) {
    return null;
  }
  return loadedTrace.root.id;
}
