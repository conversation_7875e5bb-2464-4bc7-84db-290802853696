import { describe, it, expect } from "vitest";
import { buildVirtualTrace } from "./query-utils";
import { type SpanOverviewRow } from "./schema-utils";
import { v4 as uuidv4 } from "uuid";
import { MAX_DFS_DEPTH } from "#/ui/trace/graph";

const ROOT_SPAN_ID = uuidv4();

describe("buildVirtualTrace", () => {
  function makeSpanOverviewRow({
    spanId,
    spanParents,
    rootSpanId,
    scores,
  }: {
    spanId?: string;
    spanParents?: string[];
    rootSpanId?: string;
    scores?: Record<string, number>;
  }): SpanOverviewRow {
    return {
      id: uuidv4(),
      span_id: spanId ?? uuidv4(),
      root_span_id: rootSpanId ?? ROOT_SPAN_ID,
      span_parents: spanParents,
      span_attributes: { name: "test" },
      scores,
      _xact_id: "0",
      created: "2021-01-01T00:00:00.000Z",
    };
  }

  it("returns null with an empty span list", () => {
    const rows: SpanOverviewRow[] = [];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    expect(trace).toBe(null);

    expect(error).toBe(null);
  });

  it("returns null when no root span is found", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["A"],
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    expect(trace).toBe(null);

    expect(error).toBeInstanceOf(Error);
    expect(error?.message).toMatch(/Root span not found/);
  });

  it("skips spans that contain an invalid root span with error", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "A",
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: ["B"],
        rootSpanId: "other",
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    expect(trace).not.toBeNull();
    expect(trace).toBeDefined();
    expect(trace?.spans["A"]).toBeDefined();
    expect(trace?.spans["B"]).toBeUndefined(); // B is skipped

    expect(error).toBe(null);
  });

  it("handles self-cycle", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["A"],
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    const cycleSpan = trace?.spans["A"];
    expect(trace).not.toBeNull();
    expect(cycleSpan).toBeDefined();
    expect(cycleSpan?.children.length).toBe(0);

    expect(error).toBe(null);
  });

  it("handles two span cycle", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["B"],
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: ["A"],
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    const spanA = trace?.spans["A"];
    expect(spanA).not.toBeNull();
    expect(spanA).toBeDefined();
    expect(spanA?.children.length).toBe(0);
    const spanB = trace?.spans["B"];
    expect(spanB).not.toBeNull();
    expect(spanB).toBeDefined();
    expect(spanB?.children.length).toBe(1);

    expect(error).toBe(null);
  });

  it("puts other root spans under root based on rowId", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["other"],
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: [],
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, "root", undefined);
    expect(trace).not.toBeNull();
    expect(trace).toBeDefined();
    // Check that the orphan spans are under the root
    expect(trace?.root.children.map((c) => c.span_id)).toEqual(["A", "B"]);
    expect(trace?.spans["A"].span_parents).toEqual(["other"]);
    expect(trace?.spans["B"].span_parents).toEqual([]);

    expect(error).toBe(null);
  });

  it("merges scores onto root", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
        scores: {
          root: 1,
        },
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["root"],
        scores: {
          A: 1,
        },
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: ["A"],
        scores: {
          B: 1,
        },
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    expect(trace?.root.scores).toBeDefined();
    expect(trace?.root.scores).toEqual({
      A: {
        isDiff: undefined,
        left: [],
        right: [1],
        spanId: "A",
      },
      B: {
        isDiff: false,
        left: [],
        right: [1],
        spanId: "B",
      },
      root: {
        isDiff: undefined,
        left: [],
        right: [1],
        spanId: "root",
      },
    });

    expect(error).toBe(null);
  });

  it("merges up to MAX_DFS_DEPTH", () => {
    const rows: SpanOverviewRow[] = [
      ...Array.from({ length: MAX_DFS_DEPTH }).map((_, i) =>
        makeSpanOverviewRow({
          spanId: `A${i}`,
          spanParents: i === 0 ? ["root"] : [`A${i - 1}`],
        }),
      ),
      makeSpanOverviewRow({
        spanId: "lastFilledSpan",
        spanParents: [`A${MAX_DFS_DEPTH - 1}`],
        scores: {
          leaf: 1,
        },
      }),
      makeSpanOverviewRow({
        spanId: "leaf",
        spanParents: ["lastFilledSpan"],
        scores: {
          skipped: 1,
        },
      }),
      makeSpanOverviewRow({
        spanId: "root",
      }),
    ];

    const { trace, error } = buildVirtualTrace(rows, null, undefined);
    expect(trace?.root.scores).toBeDefined();
    expect(trace?.root.scores).toEqual({
      leaf: {
        isDiff: false,
        left: [],
        right: [1],
        spanId: "lastFilledSpan",
      },
    });

    expect(error).toBe(null);
  });
});
