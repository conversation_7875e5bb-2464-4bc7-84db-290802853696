/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, expect, test } from "vitest";
import {
  aiSDKResponseNormalizer,
  convertAISDKTopLevelText,
  isAISDKSpan,
} from "./ai-sdk-response-converter";
import { spanAttributesSchema } from "@braintrust/local";

describe("ai-sdk-response-converter", () => {
  test("isAISDKSpan detects span names with ai-sdk prefix", () => {
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "ai-sdk.generateText",
        }),
      ),
    ).toBe(true);
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "ai-sdk.streamText",
        }),
      ),
    ).toBe(true);
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "openai.generateText",
        }),
      ),
    ).toBe(false);
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "foo-ai-sdk",
        }),
      ),
    ).toBe(false);
  });

  test("converts top-level {type:'text', text:'...'} to assistant chat message (via normalizer)", () => {
    const input = undefined;
    const output = [
      {
        type: "text",
        text: "UTC time is 2025-09-10T18:48:44.342Z.",
      },
    ];

    const result = aiSDKResponseNormalizer.normalize(input, output);

    const normalizedOutput = result.output;
    expect(normalizedOutput).toEqual([
      {
        role: "assistant",
        content: "UTC time is 2025-09-10T18:48:44.342Z.",
      },
    ]);
  });

  test("passes through non-top-level-text outputs unchanged", () => {
    const passthrough = [{ role: "assistant", content: "hello" }];
    const result = convertAISDKTopLevelText(undefined, passthrough);
    expect(result.output).toEqual(passthrough);
  });

  test("detect identifies ai-sdk spans using spanAttributes name", () => {
    const output = { type: "text", text: "x" };
    expect(
      aiSDKResponseNormalizer.detect(undefined, output, undefined, {
        name: "ai-sdk.generateText",
      }),
    ).toBe(true);
    expect(
      aiSDKResponseNormalizer.detect(undefined, output, undefined, {
        name: "other.generateText",
      }),
    ).toBe(false);
  });
});
