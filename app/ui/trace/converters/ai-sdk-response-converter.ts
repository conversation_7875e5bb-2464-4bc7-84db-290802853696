import { type spanAttributesSchema } from "@braintrust/local";
import { type z } from "zod";
import type {
  FormatNormalizer,
  NormalizedData,
} from "#/ui/trace/format-normalizer";
import { type LLMMessageType } from "#/ui/LLMView";

export function isAISDKSpan(
  input: unknown,
  output: unknown,
  metadata?: string,
  spanAttributes?: z.infer<typeof spanAttributesSchema>,
): boolean {
  return spanAttributes?.name?.startsWith("ai-sdk") || false;
}

export function convertAISDKTopLevelText(
  input: unknown,
  output: unknown,
  metadata?: string,
  spanAttributes?: z.infer<typeof spanAttributesSchema>,
): NormalizedData {
  if (output instanceof Array) {
    const normalizedOutput = output.map((message) => {
      if (message.type === "text" && message.text) {
        const msg: LLMMessageType = {
          role: "assistant",
          content: message.text,
        };
        return msg;
      } else {
        return message;
      }
    });
    return {
      input,
      output: normalizedOutput,
      toolDefinitions: new Map<string, string>(),
      metadata,
    };
  }
  // Pass-through for everything else
  return {
    input,
    output,
    toolDefinitions: new Map<string, string>(),
    metadata,
  };
}

export const aiSDKResponseNormalizer: FormatNormalizer = {
  name: "AISDKResponseNormalizer",
  detect: isAISDKSpan,
  normalize: convertAISDKTopLevelText,
};
