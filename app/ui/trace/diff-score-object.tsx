import * as React from "react";
import { DiffScore } from "#/ui/diff";
import { type SpanScore } from "./graph";
import { cn } from "#/utils/classnames";
import { DataType } from "apache-arrow";
import { isEmpty, isNumber, isObject } from "#/utils/object";
import { Button } from "#/ui/button";
import { ListFilter } from "lucide-react";
import { toast } from "sonner";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { doubleQuote } from "#/utils/sql-utils";

export function DiffScoreObject({
  scores,
  onApplySearch,
  comparisonClassName,
}: {
  onApplySearch: ApplySearch;
  scores: Record<string, SpanScore>;
  comparisonClassName: string;
}) {
  return (
    <>
      {Object.entries(scores)
        .filter(([k, score]) => score.left.length > 0 || score.right.length > 0)
        .map(([k, score]) => {
          const avgRight = average(score.right);
          return (
            <div
              key={k}
              className="flex w-full gap-2 @sm:w-44 @sm:flex-col @sm:gap-1"
            >
              <div className="w-2/5 break-words text-xs text-primary-500 @sm:w-auto @sm:flex-none">
                {k}
              </div>
              <div className="w-3/5 text-sm @sm:w-auto">
                {score.isDiff ? (
                  <DiffScore
                    oldScoreClassName={cn(
                      comparisonClassName,
                      "bg-transparent",
                    )}
                    oldScore={average(score.left)}
                    newScore={avgRight}
                  />
                ) : (
                  <Percent value={avgRight} />
                )}
                <Button
                  size="inline"
                  transparent
                  className="ml-1 inline-flex items-center text-primary-500 opacity-0 transition-all hover:text-primary-900 group-hover:opacity-100 dark:text-primary-600"
                  onClick={() => {
                    const query = `scores.${doubleQuote(k)} = ${(Number(avgRight) * 100).toFixed(2)}%`;
                    toast.promise(
                      onApplySearch(query, undefined, {
                        originType: "btql",
                        label: query,
                      }),
                      {
                        loading: "Applying filter",
                        success: "Score filter applied",
                        error: "Failed to apply filter",
                      },
                    );
                  }}
                >
                  <ListFilter size={10} />
                </Button>
              </div>
            </div>
          );
        })}
    </>
  );
}

export function average(vals: number[]) {
  if (vals.length === 0 || vals.every((v) => v == null)) {
    return null;
  } else {
    return vals.reduce((a, b) => a + b, 0) / vals.length;
  }
}

function PercentComponent({
  value,
  type,
  className,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  value: any;
  type?: DataType;
  className?: string;
}) {
  if (type && DataType.isStruct(type) && value != null) {
    const nonEmpty = type.children.filter((f) => value[f.name] != null);

    return (
      <>
        {nonEmpty.map((f) => (
          <span key={f.name}>
            <span>{f.name}</span>:{" "}
            <Percent
              value={value[f.name]}
              type={f.type}
              className={className}
            />
            {"\n"}
          </span>
        ))}
      </>
    );
  } else if (isObject(value)) {
    const nonEmpty = Object.entries(value).filter(([_f, v]) => !isEmpty(v));

    return (
      <>
        {nonEmpty.map(([f, v]) => (
          <span key={f}>
            <span>{f}</span>
            : <Percent value={v} className={className} />
            {"\n"}
          </span>
        ))}
      </>
    );
  } else if (type ? isNumericType(type) : isNumber(value)) {
    const numericValue = value !== null ? Number(value) : null;
    if (numericValue !== null && !Number.isNaN(numericValue)) {
      // const setRed = numericValue < 0.5;
      return (
        <span className={cn("tabular-nums", className)}>
          {formatScoreValue(numericValue)}
        </span>
      );
    }
  }
  return <>{value ?? <NullFormatter />}</>;
}
export const Percent = React.memo(PercentComponent);

export const isNumericType = (type: DataType) =>
  DataType.isInt(type) || DataType.isFloat(type) || DataType.isDecimal(type);

export function formatScoreValue(score: number) {
  return `${(score * 100).toLocaleString(undefined, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  })}%`;
}
