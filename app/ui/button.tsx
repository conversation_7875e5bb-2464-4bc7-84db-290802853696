import { cn } from "#/utils/classnames";
import { type VariantProps, cva } from "class-variance-authority";
import { ChevronDown, type LucideIcon } from "lucide-react";
import React, { type ButtonHTMLAttributes, forwardRef } from "react";
import { Spinner } from "./icons/spinner";
import * as Slot from "@radix-ui/react-slot";

export const buttonVariants = cva(
  cn(
    "flex-none inline-flex items-center justify-center rounded-md",
    "text-sm transition-colors font-medium",
    "disabled:opacity-60 disabled:pointer-events-none",
    "disabled:bg-primary-50 gap-2 relative",
  ),
  {
    variants: {
      variant: {
        default: "bg-primary-100 hover:bg-primary-200",
        primary:
          "bg-accent-600 text-white hover:bg-accent-700 disabled:bg-accent-400 dark:bg-accent-400 dark:hover:bg-accent-500 disabled:dark:bg-accent-200",
        ghost: "text-primary-700 hover:bg-primary-100 hover:text-primary-900",
        border:
          "border text-primary-700 hover:bg-primary-100 hover:text-primary-900",
        success:
          "bg-accent-600 text-white hover:bg-accent-700 disabled:bg-accent-400 dark:bg-accent-400 dark:hover:bg-accent-500 disabled:dark:bg-accent-200",
        cta: cn(
          "bg-accent-600 text-white hover:bg-accent-700 uppercase font-semibold leading-5",
          "disabled:opacity-60 disabled:bg-accent-400 disabled:dark:bg-accent-200",
          "rounded-[3px] text-xl leading-5",
        ),
        inverted:
          "bg-primary-800 text-background hover:bg-primary-950 disabled:bg-primary-500",
        danger:
          "text-bad-500 hover:bg-bad-50 hover:text-bad-700 disabled:text-bad-300 dark:disabled:opacity-60",
        link: "text-accent-600 underline hover:text-accent-900",
      },
      transparent: {
        true: "rounded-none border-none bg-none hover:bg-transparent disabled:bg-transparent disabled:opacity-100",
        false: null,
      },
      size: {
        default: "px-4 py-2",
        lg: "h-11 min-w-11 px-8",
        xl: "h-12 min-w-12 px-6",
        sm: "h-8 min-w-8 px-3",
        xs: "h-7 min-w-7 gap-1 px-2 text-xs",
        icon: "size-10",
        inline: null,
      },
    },
    defaultVariants: {
      variant: "border",
      transparent: false,
      size: "default",
    },
  },
);

export type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & {
    Icon?: LucideIcon | typeof Spinner;
    IconLeft?: LucideIcon;
    IconRight?: LucideIcon;
    isDropdown?: boolean;
    isLoading?: boolean;
    asChild?: boolean;
    iconClassName?: string;
  };

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => {
    const {
      variant,
      transparent,
      className,
      children,
      Icon,
      IconLeft,
      IconRight,
      isDropdown,
      isLoading,
      size,
      asChild,
      iconClassName,
      ...otherProps
    } = props;
    const effectiveVariant = transparent && !variant ? "ghost" : variant;
    const LeftIconComponent = Icon ?? IconLeft;
    const RightIconComponent = isDropdown ? ChevronDown : IconRight;
    const hasChildren = React.Children.count(children) > 0;

    const Tag = asChild ? Slot.Root : "button";

    return (
      <Tag
        className={cn(
          buttonVariants({
            variant: effectiveVariant,
            transparent,
            size,
            className: cn(
              {
                "p-0": !hasChildren,
                "transition-none": isLoading,
              },
              className,
            ),
          }),
        )}
        {...otherProps}
        disabled={isLoading || otherProps.disabled}
        ref={ref}
      >
        {LeftIconComponent && (
          <LeftIconComponent
            className={cn("size-3 flex-none", iconClassName)}
          />
        )}
        <Slot.Slottable>{children}</Slot.Slottable>
        {RightIconComponent && (
          <RightIconComponent
            className={cn("flex-none size-3", iconClassName, {
              "text-primary-400": isDropdown,
            })}
          />
        )}
        {isLoading && (
          <span
            className={cn(
              "absolute inset-0 z-10 flex items-center justify-center rounded-md bg-inherit",
              {
                "bg-accent-600 dark:bg-accent-400":
                  effectiveVariant === "primary" ||
                  effectiveVariant === "success",
              },
            )}
          >
            <Spinner className="size-3" />
          </span>
        )}
      </Tag>
    );
  },
);
Button.displayName = "Button";
