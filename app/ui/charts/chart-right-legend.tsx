import { cn } from "#/utils/classnames";
import { type ChartPadding, type ChartSize } from "./chart.types";
import { CHART_COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/color";
import { ChartSymbol } from "./symbols";

interface ChartRightLegendProps {
  chartSize: ChartSize;
  chartPadding: ChartPadding;
  seriesLabels?: { label: string; colorIndex: number }[];
  symbolLabels?: { label: string; symbolIndex: number }[];
  enableSeriesToggles?: boolean;
  seriesEnabled: boolean[];
  setSeriesEnabled: React.Dispatch<React.SetStateAction<boolean[]>>;
}

const RightLegendContent = (props: ChartRightLegendProps) => {
  const {
    seriesLabels,
    symbolLabels,
    enableSeriesToggles,
    seriesEnabled,
    setSeriesEnabled,
  } = props;

  return (
    <>
      {seriesLabels
        ? seriesLabels.map(({ label, colorIndex }, i) => {
            if (colorIndex == null) return null;

            return (
              <div
                key={i}
                className={cn(
                  "flex items-center",
                  enableSeriesToggles && "cursor-pointer",
                )}
                onClick={() => {
                  if (
                    !enableSeriesToggles ||
                    Object.keys(seriesLabels).length <= 1
                  ) {
                    return;
                  }
                  const newSeriesEnabled = seriesEnabled.map((v, j) =>
                    j === i ? !seriesEnabled[j] : v,
                  );
                  setSeriesEnabled(newSeriesEnabled);
                }}
              >
                <div
                  className={cn(
                    "mr-1 size-2 rounded-full border-2",
                    CHART_COLOR_CLASSNAMES[colorIndex] ||
                      DEFAULT_COLOR_CLASSNAME,
                    !seriesEnabled[i] && "bg-transparent",
                  )}
                />
                <span className="max-w-32 truncate text-xs">{label}</span>
              </div>
            );
          })
        : null}
      {symbolLabels
        ? symbolLabels.map(({ label, symbolIndex }, i) => (
            <div key={i} className="flex items-center">
              <ChartSymbol
                className="fill-primary-800"
                index={symbolIndex}
                size={4}
              />
              <span className="max-w-32 truncate text-xs">{label}</span>
            </div>
          ))
        : null}
    </>
  );
};

export const ChartRightLegend = (props: ChartRightLegendProps) => {
  const { chartPadding, chartSize } = props;
  return (
    <div
      className={cn("flex flex-wrap gap-2 absolute overflow-hidden")}
      style={{
        left: chartSize.width - chartPadding.right,
        top: chartPadding.top,
        height: chartSize.height - chartPadding.top,
        width: chartPadding.right,
      }}
    >
      <RightLegendContent {...props} />
    </div>
  );
};
