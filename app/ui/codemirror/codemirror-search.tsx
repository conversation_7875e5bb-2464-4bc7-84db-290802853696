import { type Editor<PERSON>ie<PERSON> } from "@codemirror/view";
import { openSearchPanel, findNext, findPrevious } from "@codemirror/search";
import { useEffect, useMemo } from "react";
import "#/ui/TextEditor.css";
import { ArrowLef<PERSON>, ArrowRight, Locate } from "lucide-react";
import { Button } from "#/ui/button";
import { escapeRegExp } from "#/utils/regex";
import { pluralizeWithCount } from "#/utils/plurals";

export const CodemirrorSearch = ({
  value,
  searchQuery,
  editor,
}: {
  value?: string;
  searchQuery?: string;
  editor?: EditorView;
}) => {
  const numberOfSearchResults = useMemo(() => {
    if (!value || !searchQuery) return 0;
    const regex = new RegExp(escapeRegExp(searchQuery), "gi");
    return value.match(regex)?.length ?? 0;
  }, [value, searchQuery]);

  useEffect(() => {
    if (editor) {
      // @eden: open the hidden search panel when the editor is ready
      // to enable match highlighting
      openSearchPanel(editor);
    }
  }, [editor]);

  if (!(searchQuery && numberOfSearchResults > 0)) {
    return null;
  }

  return (
    <div className="sticky right-0 top-0 z-20 flex justify-end pr-1">
      <div className="flex items-center gap-1 rounded-md bg-orange-50 p-0.5 text-xs shadow-sm dark:bg-orange-950 dark:text-primary-300">
        <span className="px-1 text-primary-700">
          {pluralizeWithCount(numberOfSearchResults, "match", "matches")} in
          this field
        </span>
        {numberOfSearchResults > 1 && (
          <Button
            size="xs"
            variant="ghost"
            Icon={ArrowLeft}
            className="h-5 min-w-5"
            onClick={() => {
              if (!editor) return;
              findPrevious(editor);
            }}
          />
        )}
        <Button
          size="xs"
          variant="ghost"
          Icon={numberOfSearchResults === 1 ? Locate : ArrowRight}
          className="h-5 min-w-5"
          onClick={() => {
            if (!editor) return;
            findNext(editor);
          }}
        />
      </div>
    </div>
  );
};
