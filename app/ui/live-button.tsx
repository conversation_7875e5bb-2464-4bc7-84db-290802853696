import pluralize from "pluralize";
import { <PERSON><PERSON> } from "./button";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { PlayCircle, StopCircle } from "lucide-react";
import { cn } from "#/utils/classnames";
import { Spinner } from "./icons/spinner";
import { throttle } from "throttle-debounce";

export interface LiveButtonProps {
  newRows: number;
  onRefresh: () => Promise<void>;
  traces?: boolean;
  onPause?: () => void;
  onPlay?: () => void;
  variant?: "ghost" | "border";
}

export const MAX_NEW_ROWS = 10;

export const LiveButton = ({
  newRows,
  onRefresh,
  isLive,
  setIsLive,
  hideLiveButton,
  onPause,
  onPlay,
  variant,
}: LiveButtonProps & {
  isLive: boolean;
  setIsLive?: Dispatch<SetStateAction<boolean>>;
  hideLiveButton?: boolean;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const refresh = useCallback(async () => {
    try {
      await onRefresh();
    } catch (e) {
      console.error("Failed to refresh live data", e);
    } finally {
      setIsLoading(false);
    }
  }, [onRefresh]);
  const throttledRefresh = useMemo(() => throttle(2000, refresh), [refresh]);

  useEffect(() => {
    if (!isLive || newRows === 0) {
      return;
    }
    setIsLoading(true);
    throttledRefresh();
  }, [isLive, throttledRefresh, newRows]);

  if (hideLiveButton) {
    return null;
  }

  return (
    <Button
      size="xs"
      variant={variant ?? "ghost"}
      onClick={() => {
        setIsLive?.((l) => !l);
        if (isLive) {
          onPause?.();
        } else {
          onPlay?.();
        }
      }}
      className={cn({
        "border-accent-600 text-accent-600 bg-accent-50 hover:bg-accent-100 hover:text-accent-700":
          isLive,
      })}
    >
      {isLoading ? (
        <Spinner className="size-3" />
      ) : isLive ? (
        <StopCircle className="size-3" />
      ) : (
        <PlayCircle className="size-3" />
      )}
      <span className="hidden @xl/controls:block">Live</span>
    </Button>
  );
};

export const NewRowsBanner = ({
  newRows,
  onRefresh,
  traces,
}: LiveButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const objectName = traces ? "trace" : "row";

  if (newRows === 0) {
    return null;
  }

  return (
    <div className="sticky left-0 w-full">
      <Button
        size="xs"
        onClick={async () => {
          setIsLoading(true);
          try {
            await onRefresh();
          } finally {
            setIsLoading(false);
          }
        }}
        isLoading={isLoading}
        className="w-full border-primary-200/80 text-center font-normal"
      >
        {newRows > 0
          ? `Load ${newRows}${newRows >= MAX_NEW_ROWS ? "+" : ""} new ${pluralize(objectName, newRows)}`
          : `No new ${pluralize(objectName, 0)}`}
      </Button>
    </div>
  );
};
