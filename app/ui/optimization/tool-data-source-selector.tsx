"use client";

import {
  type Dispatch,
  forwardRef,
  type SetStateAction,
  useCallback,
  useMemo,
  type HTMLAttributes,
} from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { Button } from "#/ui/button";
import {
  contextualSearchFunctions,
  type ContextualSearchFunction,
  type SearchableItemInfo,
} from "#/utils/codemirror/btql-lang";
import { type RunBtqlToolParameters } from "@braintrust/local/optimization/tools";
import { Check } from "lucide-react";

interface DataSourceNestedDropdownProps {
  availableDataSources?: {
    projects: SearchableItemInfo[];
    datasets: SearchableItemInfo[];
    experiments: SearchableItemInfo[];
    promptSessions: SearchableItemInfo[];
    orgs: SearchableItemInfo[];
  };
  onSelect: (
    dataSource: NonNullable<RunBtqlToolParameters["dataSource"]>,
  ) => void;
  prefilteredEntityType?: ContextualSearchFunction;
}

type MenuItem = {
  type: ContextualSearchFunction;
  id: string;
  name: string;
  projectName?: string;
};

const getEntityLabel = (entityType: ContextualSearchFunction) => {
  switch (entityType) {
    case "project_logs":
    case "project_prompts":
    case "project_functions":
      return "a project";
    case "dataset":
      return "a dataset";
    case "experiment":
      return "an experiment";
    case "prompt_session":
    case "playground_logs":
      return "a playground";
    case "org_prompts":
    case "org_functions":
      return "an organization";
  }
};

export const DataSourceNestedDropdown = ({
  availableDataSources,
  onSelect,
  prefilteredEntityType,
}: DataSourceNestedDropdownProps) => {
  const subGroups = useMemo(() => {
    if (!availableDataSources) return [];

    const groups: Array<{ groupLabel: string; items: MenuItem[] }> = [];

    if (availableDataSources.projects.length > 0) {
      groups.push({
        groupLabel: "project_logs",
        items: availableDataSources.projects.map((p) => ({
          ...p,
          type: "project_logs" as const,
        })),
      });

      groups.push({
        groupLabel: "project_prompts",
        items: availableDataSources.projects.map((p) => ({
          ...p,
          type: "project_prompts" as const,
        })),
      });

      groups.push({
        groupLabel: "project_functions",
        items: availableDataSources.projects.map((p) => ({
          ...p,
          type: "project_prompts" as const,
        })),
      });
    }
    if (availableDataSources.datasets.length > 0) {
      groups.push({
        groupLabel: "dataset",
        items: availableDataSources.datasets.map((d) => ({
          ...d,
          type: "dataset" as const,
        })),
      });
    }
    if (availableDataSources.experiments.length > 0) {
      groups.push({
        groupLabel: "experiment",
        items: availableDataSources.experiments.map((e) => ({
          ...e,
          type: "experiment" as const,
        })),
      });
    }
    if (availableDataSources.promptSessions.length > 0) {
      groups.push({
        groupLabel: "prompt_session",
        items: availableDataSources.promptSessions.map((p) => ({
          ...p,
          type: "prompt_session" as const,
        })),
      });

      groups.push({
        groupLabel: "playground_logs",
        items: availableDataSources.promptSessions.map((p) => ({
          ...p,
          type: "playground_logs" as const,
        })),
      });
    }
    if (availableDataSources.orgs.length > 0) {
      groups.push({
        groupLabel: "org_prompts",
        items: availableDataSources.orgs.map((o) => ({
          ...o,
          type: "org_prompts" as const,
        })),
      });

      groups.push({
        groupLabel: "org_functions",
        items: availableDataSources.orgs.map((o) => ({
          ...o,
          type: "org_functions" as const,
        })),
      });
    }

    return groups;
  }, [availableDataSources]);

  const handleDataSourceSelect = useCallback(
    (item: MenuItem) => {
      onSelect({
        entity: item.type,
        id: item.id,
      });
    },
    [onSelect],
  );

  const DataSourceMenuItem = useMemo(() => {
    const Component = forwardRef<
      HTMLDivElement,
      { item: MenuItem } & HTMLAttributes<HTMLDivElement>
    >(({ item }, ref) => (
      <DropdownMenuItem
        ref={ref}
        className="cursor-pointer px-2 py-1.5"
        onSelect={() => handleDataSourceSelect(item)}
      >
        <div className="flex w-full items-center gap-2">
          <span className="flex-1 truncate text-xs">{item.name}</span>
          {item.projectName && (
            <span className="text-xs text-primary-500">{item.projectName}</span>
          )}
        </div>
      </DropdownMenuItem>
    ));
    Component.displayName = "DataSourceMenuItem";
    return Component;
  }, [handleDataSourceSelect]);

  return (
    <div className="relative flex">
      <NestedDropdown<MenuItem>
        placeholder=""
        objectType="data source"
        dropdownItems={
          prefilteredEntityType
            ? subGroups.find(
                (group) => group.groupLabel === prefilteredEntityType,
              )
            : undefined
        }
        subGroups={prefilteredEntityType ? undefined : subGroups}
        DropdownItemComponent={DataSourceMenuItem}
        filterItems={useCallback(
          (search, items) =>
            items.filter((i) =>
              (i.projectName ? `${i.name} (${i.projectName})` : i.name)
                .toLowerCase()
                .includes(search.toLowerCase()),
            ),
          [],
        )}
        emptyMessage="No data sources found"
        className="w-[--radix-popper-anchor-width]"
      >
        <Button
          size="xs"
          className="flex-1 justify-between bg-primary-100 text-xs"
          isDropdown
        >
          Select{" "}
          {prefilteredEntityType
            ? getEntityLabel(prefilteredEntityType)
            : "a data source"}
          ...
        </Button>
      </NestedDropdown>
    </div>
  );
};

export const DataSourceTypeSelector = ({
  prefilteredEntityType,
  setPrefilteredEntityType,
}: {
  prefilteredEntityType: ContextualSearchFunction;
  setPrefilteredEntityType: Dispatch<
    SetStateAction<ContextualSearchFunction | undefined>
  >;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="border"
          size="xs"
          className="w-full justify-between"
          isDropdown
        >
          {prefilteredEntityType}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[--radix-popper-anchor-width]">
        {contextualSearchFunctions.map((type) => (
          <DropdownMenuItem
            key={type}
            onClick={() => setPrefilteredEntityType(type)}
            className="text-xs"
          >
            {prefilteredEntityType === type ? (
              <Check className="size-3" />
            ) : (
              <div className="size-3" />
            )}
            {type}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
