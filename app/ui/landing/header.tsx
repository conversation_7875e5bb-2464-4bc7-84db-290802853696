"use client";

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
} from "#/ui/navigation-menu";
import Link from "next/link";

import { Button, buttonVariants } from "#/ui/button";
import { navigationLinks } from "#/app/(landing)/constants";
import { Logo } from "#/ui/landing/logo";
import { cn } from "#/utils/classnames";
import { <PERSON><PERSON>, <PERSON>etContent, SheetTitle, SheetTrigger } from "#/ui/sheet";
import { type BtSession } from "#/utils/auth/server-session";
import { signInPath, signUpPath } from "#/utils/auth/redirects";
import { Menu } from "lucide-react";
import { CtaLink } from "#/app/(landing)/cta-button";
import { useState } from "react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface Props {
  className?: string;
  session: BtSession | null;
  isInverted?: boolean;
}

const Header = ({ className, session, isInverted = false }: Props) => {
  const [mobileSheetOpen, setMobileSheetOpen] = useState(false);
  const closeSheet = () => setMobileSheetOpen(false);

  return (
    <header
      className={cn(
        "flex gap-4 items-center justify-between py-4 z-30 relative",
        {
          "text-white rounded-md": isInverted,
        },
        className,
      )}
    >
      <Link href={session?.loggedIn ? "/home" : "/"} aria-label="Home">
        <Logo
          width={160}
          className={cn("text-black", { "text-white": isInverted })}
        />
      </Link>
      <Sheet open={mobileSheetOpen} onOpenChange={setMobileSheetOpen}>
        <SheetTrigger asChild className={cn("lg:hidden")}>
          <Button variant="ghost" size="icon">
            <Menu className="size-6 md:size-7" />
          </Button>
        </SheetTrigger>
        <SheetContent side="top" className="bg-background shadow-md lg:hidden">
          <VisuallyHidden>
            <SheetTitle>Navigation</SheetTitle>
          </VisuallyHidden>
          <div className="flex flex-col gap-3">
            {navigationLinks.map((link) => (
              <Link
                key={link.title}
                href={link.href}
                className="text-center"
                onMouseUp={closeSheet}
              >
                {link.title}
              </Link>
            ))}
            <Link
              href={signInPath()}
              className="text-center"
              onMouseUp={closeSheet}
            >
              Sign in
            </Link>
            {!session?.loggedIn && (
              <Link
                href={signUpPath()}
                className="text-center"
                onMouseUp={closeSheet}
              >
                Sign up for free
              </Link>
            )}
          </div>
        </SheetContent>
      </Sheet>
      <NavigationMenu className="hidden lg:block">
        <NavigationMenuList
          className={cn("group gap-2 text-sm font-medium", {
            "bg-white/80 py-1 px-0.5 rounded-md": !isInverted,
          })}
        >
          {navigationLinks.map((link) => (
            <NavItem key={link.title} href={link.href} isInverted={isInverted}>
              {link.title}
            </NavItem>
          ))}
          {session?.loggedIn ? (
            <>
              <NavItem href="/app" className="border" isInverted={isInverted}>
                Dashboard
              </NavItem>
            </>
          ) : (
            <>
              <NavItem href={signInPath()} isInverted={isInverted}>
                Sign in
              </NavItem>
              <CtaLink
                className={cn(
                  buttonVariants({
                    variant: "ghost",
                    size: "sm",
                    transparent: true,
                  }),
                  "pointer-events-auto transition-all rounded bg-brandBlue text-white hover:bg-black hover:text-white text-base font-display font-semibold",
                  {
                    "text-white hover:text-white": isInverted,
                  },
                )}
                size="sm"
                cta="Sign up for free"
              />
            </>
          )}
        </NavigationMenuList>
      </NavigationMenu>
    </header>
  );
};

export default Header;

const NavItem = ({
  children,
  href,
  className,
  isInverted = false,
}: {
  children: React.ReactNode;
  href: string;
  className?: string;
  isInverted?: boolean;
}) => {
  return (
    <NavigationMenuItem className="ml-0">
      <Link
        href={href}
        className={cn(
          buttonVariants({
            variant: "ghost",
            size: "sm",
            transparent: true,
          }),
          "pointer-events-auto group-hover:opacity-60 hover:!opacity-100 transition-opacity rounded text-base font-display font-semibold text-black",
          {
            "text-white hover:text-white": isInverted,
          },
          className,
        )}
      >
        {children}
      </Link>
    </NavigationMenuItem>
  );
};
