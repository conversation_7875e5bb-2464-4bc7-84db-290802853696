import {
  type Cell,
  type Row,
  type RowSelectionState,
} from "@tanstack/react-table";
import { getDiffRight, makeRowIdPrimary } from "#/utils/diffs/diff-objects";
import {
  type RefObject,
  useEffect,
  use<PERSON><PERSON><PERSON>,
  type MouseEventHandler,
} from "react";
import React from "react";
import { CheckboxCell } from "#/ui/table/checkbox-cell";
import { RowComparisonCell } from "#/ui/table/cells/row-comparison-cell";
import { DeadCell } from "#/ui/dead-cell";
import { cn } from "#/utils/classnames";
import { type VirtualItem, type Virtualizer } from "@tanstack/react-virtual";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import { GroupExpandCell, SpacerGroupCell } from "./table/group-expand-cell";
import { Skeleton } from "./skeleton";
import { CellWithTooltips } from "./table/cells/cell-with-tooltip";
import { type RowComparisonFormatterProps } from "./table/formatters/row-comparison-formatter";
import { type StreamingContentProps } from "./table/cells/streaming";
import useTableVirtualizer, {
  type UseTableVirtualizerArgs,
} from "./table/use-table-virtualizer";
import { z } from "zod";
import { spanTypeInfoSchema } from "./table/formatters/span-info-formatter";
import { useResizeObserver } from "./charts/padding/use-resize-observer";

export type MultilineRowProps = {
  numRows?: number;
  gapSize?: number;
  numGroupRows?: number;
  fixedHeight?: number;
  isGridLayout?: boolean;
};

interface BaseVirtualTableBodyProps<TsData> {
  onClickRowDeadCell: (e: React.MouseEvent, row: Row<TsData>) => void;
  rowSelectionState: RowSelectionState;
  tableType: "list" | "detailed";
  onCellClick: (e: React.MouseEvent, cell: Cell<TsData, unknown>) => void;
  rowEvents?: Record<string, (row: Row<TsData>) => MouseEventHandler>;
  multilineRow?: MultilineRowProps;
  isGridLayout?: boolean;
  loadingColumns?: string[];
  isLoading?: boolean;
  activeGroupValue?: unknown;
  rowComparisonProps?: RowComparisonFormatterProps;
  streamingContentProps?: StreamingContentProps;
  disableErrorRowHighlight?: boolean;
  removeLimiter?: boolean;
}

interface VirtualTableBodyProps<TsData>
  extends BaseVirtualTableBodyProps<TsData> {
  rows: Row<TsData>[];
  useTableVirtualizerProps: UseTableVirtualizerArgs<TsData>;
  measureColumnsRef: RefObject<(() => void) | null>;
  measureRowsRef: RefObject<(() => void) | null>;
  scrollToIndexRef: RefObject<
    Virtualizer<HTMLElement, Element>["scrollToIndex"] | null
  >;
  openRowId?: string | null;
  /**
   * for trace group highlighting on the logs page.
   * Once we support grouping in the logs table we can remove this in favor of actual table groups
   */
  groupedRowIds?: Set<string>;
  scrollMarginRef?: RefObject<HTMLElement | null>;
}

export const spanTypeInfoHasError = (
  spanTypeInfo?: string | z.infer<typeof spanTypeInfoSchema>,
): boolean => {
  if (!spanTypeInfo) return false;
  if (typeof spanTypeInfo === "string") {
    try {
      const result = JSON.parse(spanTypeInfo);
      return result.has_error;
    } catch (e) {
      return false;
    }
  }
  return spanTypeInfo.has_error;
};

const spanTypeInfoUnionSchema = z.object({
  span_type_info: z.union([z.string(), spanTypeInfoSchema]),
});

export const VirtualTableBodyComponent = <TsData,>({
  rows,
  useTableVirtualizerProps,
  measureColumnsRef,
  measureRowsRef,
  scrollToIndexRef,
  isGridLayout,
  disableErrorRowHighlight,
  removeLimiter,
  openRowId,
  groupedRowIds,
  scrollMarginRef,
  ...tableRowProps
}: VirtualTableBodyProps<TsData>) => {
  const [{ r }] = useActiveRowAndSpan();
  const activeRowId = makeRowIdPrimary(r ?? null);

  const { height: scrollMarginFromObserver } =
    useResizeObserver(scrollMarginRef);
  const scrollMargin = scrollMarginRef
    ? scrollMarginFromObserver
    : useTableVirtualizerProps.scrollMargin;

  const {
    measureColumns,
    measureRows,
    scrollToIndex,
    columnVirtualizer,
    rowVirtualizerParams,
    // don't lift this state up because it can cause too many re-renders in arrow-table
  } = useTableVirtualizer({
    ...useTableVirtualizerProps,
    scrollMargin,
  });

  useEffect(() => {
    // eslint-disable-next-line react-compiler/react-compiler
    measureColumnsRef.current = measureColumns;
    // eslint-disable-next-line react-compiler/react-compiler
    measureRowsRef.current = measureRows;
    // eslint-disable-next-line react-compiler/react-compiler
    scrollToIndexRef.current = scrollToIndex;
  }, [
    measureColumnsRef,
    measureRowsRef,
    scrollToIndexRef,
    measureColumns,
    measureRows,
    scrollToIndex,
  ]);

  if (
    rowVirtualizerParams.type === "scrollContainer" ||
    tableRowProps.tableType === "detailed"
  ) {
    const rowVirtualizer = rowVirtualizerParams.virtualizer;
    const virtualColumns = columnVirtualizer.getVirtualItems();

    // different virtualization strategy for columns - instead of absolute and translateY, we add empty columns to the left and right
    let virtualPaddingLeft: number | undefined;
    let virtualPaddingRight: number | undefined;

    if (columnVirtualizer && virtualColumns?.length) {
      virtualPaddingLeft = virtualColumns[0]?.start ?? 0;
      virtualPaddingRight =
        columnVirtualizer.getTotalSize() -
        (virtualColumns[virtualColumns.length - 1]?.end ?? 0);
    }

    const dynamicRowHeight = isGridLayout;
    return (
      <div
        className="relative block"
        style={{
          height: removeLimiter ? undefined : rowVirtualizer.getTotalSize(),
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const row = rows[virtualRow.index];
          if (!row) return null;

          const rowId = makeRowIdPrimary(row.id);
          const isRowOpen = rowId === activeRowId || rowId === openRowId;
          const rowSelected =
            tableRowProps.rowSelectionState && row.getIsSelected();
          const isGroupRow = row.getCanExpand();

          const spanTypeInfoParsed = spanTypeInfoUnionSchema.safeParse(
            row.original,
          );
          const spanTypeInfo = spanTypeInfoParsed.success
            ? spanTypeInfoParsed.data.span_type_info
            : undefined;
          const hasError =
            !disableErrorRowHighlight &&
            spanTypeInfoHasError(getDiffRight(spanTypeInfo));

          const isGrouped = rowId && groupedRowIds?.has(rowId);

          return (
            <div
              key={virtualRow.key} // important - use the row's key to align with measureElement
              ref={dynamicRowHeight ? rowVirtualizer.measureElement : undefined}
              // needed for dynamic row height measurement
              data-index={virtualRow.index}
              className={cn(
                "flex absolute group/tablerow w-full will-change-transform border-y border-background text-primary-900 rounded-md bg-primary-50",
                {
                  static: removeLimiter, // remove dynamic height measurement for playgrounds without dataset
                  "bg-primary-100/70": isGroupRow,
                  "bg-comparison-100/70": isGrouped,
                  // row being opened in sidebar
                  "bg-primary-200/60": isRowOpen,
                  "bg-comparison-200/60": isRowOpen && isGrouped,
                  // row being selected with checkbox
                  "bg-accent-50 dark:bg-accent-50/60": rowSelected,
                  "bg-bad-50 hover:bg-bad-100/80 dark:bg-bad-50/50 hover:dark:bg-bad-50/60":
                    hasError,
                  "bg-bad-200/50 dark:bg-bad-50/80": hasError && isRowOpen,
                  "hover:bg-primary-100/80":
                    !isRowOpen && !rowSelected && !hasError,
                  "hover:bg-comparison-100/80":
                    !isRowOpen && !rowSelected && !hasError && isGrouped,
                },
              )}
              style={{
                transform: `translateY(${virtualRow.start - (scrollMargin ?? 0)}px)`,
                contain: "paint",
                minHeight: removeLimiter ? 300 : virtualRow.size,
                // Ideally this is undefined for dynamic heights, but that's causing
                // grid rows to grow really tall. This will constrain them to our computed height.
                height: removeLimiter ? undefined : virtualRow.size,
              }}
            >
              {virtualPaddingLeft ? (
                //fake empty column to the left for virtualization scroll padding
                <div style={{ display: "flex", width: virtualPaddingLeft }} />
              ) : null}
              <VirtualTableRow
                row={row}
                isGroupRow={isGroupRow}
                columnVirtualizer={columnVirtualizer}
                {...tableRowProps}
              />
              {virtualPaddingRight ? (
                //fake empty column to the right for virtualization scroll padding
                <div style={{ display: "flex", width: virtualPaddingRight }} />
              ) : null}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div>
      {rows.map((row) => (
        <VirtualTableRow
          key={row.id}
          row={row}
          isGroupRow={row.getCanExpand()}
          columnVirtualizer={columnVirtualizer}
          {...tableRowProps}
        />
      ))}
    </div>
  );
};

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const VirtualTableBody = React.memo(
  VirtualTableBodyComponent,
) as typeof VirtualTableBodyComponent;

function VirtualTableRowComponent<TsData>({
  row,
  onCellClick,
  rowEvents,
  tableType,
  onClickRowDeadCell,
  multilineRow,
  columnVirtualizer,
  loadingColumns,
  isLoading: isLoadingProp,
  rowComparisonProps,
  streamingContentProps,
  isGroupRow,
  rowSelectionState,
}: BaseVirtualTableBodyProps<TsData> & {
  row: Row<TsData>;
  isGroupRow: boolean;
  columnVirtualizer: Virtualizer<HTMLElement, Element>;
}) {
  const cells = row.getVisibleCells();

  return columnVirtualizer.getVirtualItems().map((item) => {
    const cell: Cell<TsData, unknown> | undefined = cells[item.index];

    let deadCellWidth: number | undefined;
    if (!cell) {
      deadCellWidth = Math.max(
        0,
        (columnVirtualizer.scrollRect?.width ?? 0) - 28 - item.start,
      );
    }
    const value = cell?.getValue();
    return (
      <div
        key={item.key}
        data-index={item.index}
        style={{
          // always measure the base cell size since colspan can affect the item width
          width: deadCellWidth ?? cell?.column.getSize(),
          // to support colspan tooltips, push null cells to the back
          zIndex:
            tableType === "detailed" && isGroupRow && value == null
              ? -1
              : undefined,
        }}
        className="grow-1 flex"
        // dead cell is the only one that's not a static width
        ref={!cell ? columnVirtualizer.measureElement : undefined}
      >
        <VirtualCell
          item={item}
          deadCellWidth={deadCellWidth}
          isGroupRow={isGroupRow}
          row={row}
          cells={cells}
          onClickRowDeadCell={onClickRowDeadCell}
          onCellClick={onCellClick}
          tableType={tableType}
          rowEvents={rowEvents}
          rowComparisonProps={rowComparisonProps}
          multilineRow={multilineRow}
          streamingContentProps={streamingContentProps}
          loadingColumns={loadingColumns}
          isLoading={isLoadingProp}
          rowSelectionState={rowSelectionState}
        />
      </div>
    );
  });
}

//const VirtualTableRow = genericMemo(VirtualTableRowComponent);
const VirtualTableRow = VirtualTableRowComponent;

type VirtualCellProps<TsData> = BaseVirtualTableBodyProps<TsData> & {
  item: VirtualItem;
  deadCellWidth?: number;
  isGroupRow: boolean;
  row: Row<TsData>;
  cells: Cell<TsData, unknown>[];
  /**
   * Non-reactive table properties for which we want to
   * trigger a re-render on a memoized cell should go here
   */
  rerenderProps: {
    isExpanded: boolean;
    canExpand: boolean;
    isSelected: boolean;
    canSelect: boolean;
    cellValue: unknown;
  };
};

function VirtualCellComponent<TsData>({
  item,
  deadCellWidth,
  isGroupRow,
  row,
  cells,

  onClickRowDeadCell,
  onCellClick,
  tableType,
  rowEvents,
  rowComparisonProps,
  multilineRow,
  streamingContentProps,
  loadingColumns,
  isLoading: isLoadingProp,
}: VirtualCellProps<TsData>) {
  const cell: Cell<TsData, unknown> | undefined = cells[item.index];
  const internalType = cell?.column.columnDef.meta?.internalType;
  const isLoading =
    isLoadingProp || loadingColumns?.includes(cell?.column.id ?? "");

  let colspanSize = cell?.column.getSize() ?? 0;
  let i = 1;
  while (
    isGroupRow &&
    cell?.column.columnDef.meta?.colSpan &&
    cells[item.index + i] &&
    cells[item.index + i].getValue() == null &&
    !isScoreOrMetricPath(
      cells[item.index + i].column.columnDef.meta?.path ?? [],
    ) &&
    !cells[item.index + i].column.columnDef.meta?.isNumeric
  ) {
    colspanSize += cells[item.index + i].column.getSize() ?? 0;
    i++;
  }

  const value = cell?.getValue();
  const prevCell = cells[item.index - 1];
  // hack to show a right border on a null valued column if it's preceded by a non-null value
  const showLeftBorder =
    isGroupRow &&
    value == null &&
    !prevCell?.column.columnDef.meta?.colSpan &&
    prevCell?.getValue() != null;

  const width = deadCellWidth ?? colspanSize;
  return (
    <div className="grow-1 flex shrink-0 truncate" style={{ width }}>
      {!cell ? (
        <DeadCell
          isGroupRow={isGroupRow}
          onClick={(e) => {
            onClickRowDeadCell(e, row);
          }}
          onAuxClick={(e) => {
            // Treat middle-click as a click.
            // The parent will handle the middle-click and open a new tab
            if (e.button === 1) {
              onClickRowDeadCell(e, row);
            }
          }}
        />
      ) : internalType === "checkbox" || internalType === "star" ? (
        isGroupRow ? (
          internalType === "star" ? (
            <SpacerGroupCell cell={cell} />
          ) : (
            <GroupExpandCell cell={cell} />
          )
        ) : (
          <CheckboxCell cell={cell} />
        )
      ) : internalType === "row_comparison" ? (
        <RowComparisonCell isGroupRow={isGroupRow} cell={cell} />
      ) : isLoading ? (
        <div
          style={{ width: cell.column.getSize() }}
          className="relative flex-none p-1.5"
        >
          <Skeleton className="h-7 w-full bg-primary-200" />
        </div>
      ) : (
        <CellWithTooltips
          size={width}
          tableType={tableType}
          cell={cell}
          onClick={(e) => {
            if (cell.row.getCanExpand()) {
              e.stopPropagation();
              cell.row.toggleExpanded();
              return;
            }
            if (e.defaultPrevented) {
              return;
            }
            onCellClick(e, cell);
          }}
          multilineRowCount={multilineRow?.numRows}
          {...(!isGroupRow && rowEvents
            ? Object.fromEntries(
                Object.entries(rowEvents).map(([event, handler]) => [
                  event,
                  handler?.(row),
                ]),
              )
            : {})}
          showLeftBorder={showLeftBorder}
          rowComparisonProps={rowComparisonProps}
          streamingContentProps={streamingContentProps}
        />
      )}
    </div>
  );
}

const genericMemo: <T>(component: T) => T = React.memo;
const VirtualCellWithMemo = genericMemo(VirtualCellComponent);
const VirtualCell = <TsData,>({
  cells,
  item,
  row,
  ...rest
}: Omit<VirtualCellProps<TsData>, "rerenderProps">) => {
  const cell = cells[item.index];
  const isExpanded = row.getIsExpanded();
  const canExpand = row.getCanExpand();
  const isSelected = rest.rowSelectionState && row.getIsSelected();
  const canSelect = row.getCanSelect();
  const cellValue = cell?.getValue();
  const rerenderProps = useMemo(
    () => ({
      isExpanded,
      canExpand,
      isSelected,
      canSelect,
      cellValue,
    }),
    [canExpand, canSelect, cellValue, isExpanded, isSelected],
  );

  return (
    <VirtualCellWithMemo
      cells={cells}
      item={item}
      row={row}
      rerenderProps={rerenderProps}
      {...rest}
    />
  );
};

function isScoreOrMetricPath(path: string[]) {
  return path.length > 0 && (path[0] === "scores" || path[0] === "metrics");
}
