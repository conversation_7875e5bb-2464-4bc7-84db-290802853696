"use client";

import { cn } from "#/utils/classnames";
import { useReadRefreshing } from "#/utils/refresh-data";
import { useOrg, useUser } from "#/utils/user";
import { PanelLeft, type LucideIcon } from "lucide-react";
import Link from "next/link";
import { useState, memo } from "react";
import { Button, buttonVariants } from "#/ui/button";
import UpgradeModal from "./upgrade-modal";
import { useRouter } from "next/navigation";
import { Spinner } from "#/ui/icons/spinner";
import { CheckApiVersion } from "#/ui/api-version/check-api-version";
import { signInPath, signUpPath } from "#/utils/auth/redirects";
import { BasicTooltip } from "#/ui/tooltip";
import { IncidentStatus } from "./incident-status";
import { useRedirectPath } from "#/utils/use-redirect-path";
import { type FeatureFlags } from "#/lib/feature-flags";
import { AdminObjectFinder } from "./admin-object-finder";
import { AdminSegmentFinder } from "./admin-segment-finder";
import { MultiTenantApiURL, type OrgContextT } from "#/utils/user-types";
import {
  isFreePlan,
  isUnlimitedOrg,
} from "#/app/app/[org]/settings/billing/plans";
import { getOrgSettingsLink } from "#/app/app/[org]/getOrgLink";
import { HelpButton } from "./help-button";
import { ProfileMenu } from "./profile-menu";
import {
  useIsSidenavDocked,
  useToggleSidenavState,
} from "#/app/app/[org]/sidenav-state";
import { HeaderDisplay } from "./header-display";
import { Healthcheck } from "#/ui/api-version/healthcheck";
import { isFriendOrFamily } from "#/ui/unlimited-free-orgs";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { Icon } from "#/ui/landing/logo";
import { type EventProps } from "#/analytics/events";
import { ToggleCommandBar } from "#/app/app/[org]/toggle-command-bar";
import { getPageName } from "#/utils/analytics";

function shouldShowUpgradeButton({ org }: { org: OrgContextT }) {
  const apiUrl = org.api_url;
  const isOnPrem = apiUrl !== MultiTenantApiURL;

  if (isOnPrem) {
    return false;
  }

  const planId = org.plan_id;
  const isFreeOrg = planId && isFreePlan(planId) && !isUnlimitedOrg({ org });
  const isFriend = isFriendOrFamily(org?.id ?? "") && !planId;

  const hasNoPlanInOrb = planId == null;
  const isUnlimited = isUnlimitedOrg({ org });

  // TODO: When we have all orgs in Orb, this can be simplified to just return isFreeOrg
  // For now, we also assume that an org that is not in Orb and is not unlimited is a free org
  return isFriend || isFreeOrg || (hasNoPlanInOrb && !isUnlimited);
}

export const HeaderNavLink = ({
  href,
  isActive,
  children,
  Icon,
  onClick,
  className,
  iconClassName,
}: React.PropsWithChildren<{
  href: string;
  isActive: boolean;
  Icon?: LucideIcon;
  className?: string;
  childLinks?: {
    Icon: LucideIcon;
    iconClassName?: string;
    label: string;
    path: string;
    description?: string;
    className?: string;
    featureFlag?: keyof FeatureFlags;
  }[];
  onClick?: () => void;
  iconClassName?: string;
}>) => {
  return (
    <Link
      href={href}
      className={cn(
        buttonVariants({
          variant: isActive ? "default" : "ghost",
          size: "sm",
        }),
        "px-2 text-left justify-start group text-[13px] h-7 gap-2.5",
        {
          "text-primary-700 hover:text-primary-950 hover:bg-primary-100":
            !isActive,
          "bg-primary-200": isActive,
        },
        className,
      )}
      onClick={(e) => {
        const baseUrl = new URL(location.href);
        if (isActive && href === baseUrl.pathname && !e.ctrlKey && !e.metaKey) {
          e.preventDefault();
        }
        onClick?.();
      }}
    >
      {Icon && (
        <Icon
          className={cn(
            "size-3.5 flex-none transition-colors text-primary-900 group-hover:text-primary-950",
            iconClassName,
          )}
        />
      )}
      <span className="flex-1 truncate">{children}</span>
    </Link>
  );
};

const Header = memo(function Header({
  noCTA,
  orgName = "",
  isAdmin = false,
  redirectPaths: redirectPaths = {},
  signupTrackingSource,
}: React.PropsWithChildren<{
  noCTA?: boolean;
  orgName?: string;
  isAdmin?: boolean;
  redirectPaths?: {
    signUp?: string;
    signIn?: string;
  };
  signupTrackingSource?: EventProps<"signup">["source"];
}>) {
  const { user, status } = useUser();
  const org = useOrg();
  const redirectPathDefault = useRedirectPath();
  const { signUp: redirectPathSignUpProp, signIn: redirectPathSignInProp } =
    redirectPaths;
  const redirectPathSignUp = redirectPathSignUpProp ?? redirectPathDefault;
  const redirectPathSignIn = redirectPathSignInProp ?? redirectPathDefault;

  const isLoggedOut = status === "unauthenticated";

  const { refreshingCounter } = useReadRefreshing();
  const [upgradeOpen, setUpgradeOpen] = useState(false);

  const router = useRouter();

  const toggleSidenavState = useToggleSidenavState();
  const isSidenavDocked = useIsSidenavDocked();

  const { track } = useAppAnalytics();

  return (
    <header className="sticky top-0 z-30 flex-none">
      <div className="flex h-11 w-full items-center gap-2 bg-primary-50 text-sm font-medium">
        {isLoggedOut && (
          <div className="pl-3">
            <Icon size={24} />
          </div>
        )}
        {isAdmin && (
          <>
            <div className="pl-3 font-mono tracking-widest text-accent-700">
              <Link href="/admin">ADMIN</Link>
            </div>
            <div className="ml-5">
              <AdminObjectFinder />
            </div>
            <div>
              <AdminSegmentFinder orgName={org.name} apiUrl={org.api_url} />
            </div>
            <div>
              <Button
                size="xs"
                onClick={() => {
                  if (!org.name) {
                    router.push("/admin/processes");
                  } else {
                    router.push(`/admin/org/${org.name}/processes`);
                  }
                }}
              >
                Brainstore processes
              </Button>
            </div>
            <div>
              <Button
                size="xs"
                onClick={() => {
                  if (!org.name) {
                    router.push("/admin/failed-segments");
                  } else {
                    router.push(`/admin/org/${org.name}/failed-segments`);
                  }
                }}
              >
                Failed Brainstore segments
              </Button>
            </div>
          </>
        )}
        <div className="flex flex-1 items-center gap-3 overflow-hidden pl-3">
          {!isSidenavDocked && !isAdmin && !!org.id && (
            <Button
              size="inline"
              transparent
              className="text-primary-600 hover:text-primary-900"
              onClick={toggleSidenavState}
            >
              <PanelLeft className="size-4" />
            </Button>
          )}
          <HeaderDisplay />
        </div>
        <div className="flex flex-none items-center gap-1 pr-3">
          {refreshingCounter > 0 && (
            <BasicTooltip asChild={false} tooltipContent="Refreshing data">
              <span className="opacity-50">
                <Spinner className="mr-2" />
              </span>
            </BasicTooltip>
          )}
          {org.id && <CheckApiVersion />}
          {org.id && <Healthcheck />}
          {user?.id && shouldShowUpgradeButton({ org }) && (
            <Link
              href={`${getOrgSettingsLink({ orgName })}/billing`}
              className={cn(
                buttonVariants({ variant: "ghost", size: "xs" }),
                "text-accent-600",
              )}
              onClick={(e) => {
                track("upgradeClick", {
                  orgName,
                  orgId: org.id ?? "",
                  entryPoint: "header",
                  destination: "billingPage",
                  destinationUrl: `${getOrgSettingsLink({ orgName })}/billing`,
                  sourcePage: getPageName(),
                });
              }}
            >
              Upgrade
            </Link>
          )}
          {user?.id && <IncidentStatus />}
          {isLoggedOut && !noCTA ? (
            <>
              <Link
                className={buttonVariants({ size: "xs", variant: "ghost" })}
                href={signInPath({ redirectPath: redirectPathSignIn })}
              >
                Sign in
              </Link>
              <Link
                className={cn(
                  buttonVariants({ size: "xs", variant: "ghost" }),
                  "text-accent-600",
                )}
                onClick={(e) => {
                  track("signup", {
                    source: signupTrackingSource ?? "in_app",
                  });
                }}
                href={signUpPath({ redirectPath: redirectPathSignUp })}
              >
                Sign up for free
              </Link>
              <HelpButton />
            </>
          ) : (
            <>
              <HelpButton />
              <ToggleCommandBar />
              <ProfileMenu />
            </>
          )}
        </div>
        <UpgradeModal open={upgradeOpen} onOpenChange={setUpgradeOpen} />
      </div>
    </header>
  );
});

Header.displayName = "Header";

export default Header;
