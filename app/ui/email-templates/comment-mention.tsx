import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  Html,
  Preview,
  Section,
  Text,
  Img,
  Link,
  Markdown,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface CommentMentionProps {
  mentionerName: string;
  projectName?: string;
  entityType?: string | null;
  entityName?: string | null;
  baseUrl?: string;
  comment: string;
  commentLink?: string;
}

const DEFAULT_BASE_URL = process.env.NEXT_PUBLIC_SITE_URL ?? "";

const CommentMention = ({
  mentionerName,
  projectName,
  baseUrl = DEFAULT_BASE_URL,
  entityType,
  entityName,
  comment,
  commentLink,
}: CommentMentionProps) => {
  const commentUrl = `${baseUrl}${commentLink}`;

  const formatText = (emailBody: boolean) =>
    !entityType || !projectName
      ? `${mentionerName} mentioned you in a comment`
      : entityType === "logs"
        ? `${mentionerName} mentioned you in ${emailBody ? `**${projectName}**` : projectName} logs`
        : entityName
          ? `${mentionerName} mentioned you in ${emailBody ? "" : `${/[aeiouAEIOU]/.test(entityType.charAt(0)) ? "an" : "a"} `}${entityType.charAt(entityType.length - 1) === "s" ? entityType.slice(0, entityType.length - 1) : entityType}${emailBody ? ` **${entityName}**` : ""}`
          : `${mentionerName} mentioned you in ${projectName}`;

  return (
    <Html>
      <Head />
      <Preview>{formatText(false)}</Preview>
      <Tailwind>
        <Body className="m-auto p-4 font-sans">
          <Container className="max-w-[520px] bg-white pt-8">
            <Section>
              <Img
                src={`${baseUrl}/logo-letter.png`}
                width="100"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Markdown
              markdownContainerStyles={{
                paddingTop: "16px",
              }}
              markdownCustomStyles={{
                p: {
                  fontSize: "16px",
                  color: "#222222",
                },
              }}
            >
              {formatText(true)}
            </Markdown>
            <Markdown
              markdownContainerStyles={{
                backgroundColor: "#f1f5f9",
                padding: "4px 16px",
                borderRadius: "8px",
              }}
            >
              {comment}
            </Markdown>
            <Section className="my-[24px]">
              <Button
                className="rounded-md bg-[#3b82f6] px-4 py-2.5 text-center text-[14px] font-medium text-white no-underline"
                href={commentUrl}
              >
                View comment
              </Button>
            </Section>
            <div className="h-px bg-zinc-200/80" />
            <Text className="text-sm text-zinc-500">
              Or, copy and paste this URL into your browser:{" "}
              <Link
                href={commentUrl}
                className="break-all text-blue-600 no-underline"
              >
                {commentUrl}
              </Link>
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: CommentMentionProps = {
  projectName: "Example Project",
  baseUrl: "https://braintrust.dev",
  comment: "comment",
  commentLink: "https://braintrust.dev/app/example-project/comments/123",
  mentionerName: "Ankur Goyal",
  entityType: "experiment",
  entityName: "bt experiment",
};

CommentMention.PreviewProps = previewProps;

export default CommentMention;
