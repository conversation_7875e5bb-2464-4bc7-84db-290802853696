import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface PaymentFailedNotificationProps {
  organizationName: string;
  baseImgUrl?: string;
  baseAppUrl?: string;
}

const DEFAULT_BASE_URL =
  process.env.NEXT_PUBLIC_SITE_URL ?? "https://braintrust.dev";

function getLimitMessage(organizationName: string) {
  return `We were unable to process your payment for ${organizationName}.`;
}

function getConsequenceMessage() {
  return "To ensure uninterrupted access to your account and prevent any service disruption, please update your payment method as soon as possible.";
}

const PaymentFailedNotification = ({
  organizationName,
  baseImgUrl = DEFAULT_BASE_URL,
  baseAppUrl = DEFAULT_BASE_URL,
}: PaymentFailedNotificationProps) => {
  const limitMessage = getLimitMessage(organizationName);
  const consequenceMessage = getConsequenceMessage();

  return (
    <Html>
      <Head />
      <Preview>{limitMessage}</Preview>
      <Tailwind>
        <Body className="m-auto p-4 font-sans">
          <Container className="max-w-[520px] bg-white pt-8">
            <Section>
              <Img
                src={`${baseImgUrl}/logo-letter.png`}
                width="100"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Heading className="mx-0 my-[30px] p-0 text-xl font-medium text-black">
              {limitMessage}
            </Heading>
            <Text className="text-base text-black">{consequenceMessage}</Text>
            <Section className="my-[32px]">
              <Button
                className="mr-2 rounded bg-[#3b82f6] px-4 py-2 text-sm font-medium text-white no-underline"
                href={`${baseAppUrl}/app/settings?subroute=billing/payment`}
              >
                Update payment method
              </Button>
            </Section>
            <div className="h-px bg-zinc-200/80" />
            <Text className="text-sm text-black">
              If you have any questions, please contact{" "}
              <Link href="mailto:<EMAIL>">
                <EMAIL>
              </Link>
              .
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: PaymentFailedNotificationProps = {
  organizationName: "Example Org",
  baseImgUrl: "https://braintrust.dev",
  baseAppUrl: "http://localhost:3000",
};

PaymentFailedNotification.PreviewProps = previewProps;

export default PaymentFailedNotification;
