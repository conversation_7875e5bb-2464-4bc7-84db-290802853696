import { cn } from "#/utils/classnames";
import Link from "next/link";
import React from "react";
import { AvatarGroup } from "#/ui/avatar";
import { type Author } from "./cookbook";

export function CookbookCards({ children }: { children: React.ReactNode }) {
  return (
    <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {children}
    </div>
  );
}

export function CookbookCard({
  title,
  route,
  authors,
  date,
  language,
  tags,
  logoIconUrl,
}: {
  title: string;
  route: string;
  authors: Author[];
  date: string;
  className?: string;
  language: string;
  tags: string[];
  logoIconUrl?: string;
}) {
  return (
    <Link
      href={route}
      className="plain flex h-52 flex-col rounded-lg border border-primary-100 bg-primary-50 p-4 !no-underline transition-colors hover:bg-primary-100"
    >
      <div className="mb-1 flex h-5 flex-none justify-between text-xs text-primary-500">
        {language === "typescript"
          ? "TypeScript"
          : language === "python"
            ? "Python"
            : language}
        <span className="flex-none text-primary-400">
          {new Date(date).toLocaleDateString("default", {
            month: "short",
            day: "numeric",
            year: "numeric",
          })}
        </span>
      </div>
      <div className="m-0 line-clamp-3 flex-1 overflow-hidden font-display text-2xl leading-tight">
        <div className="line-clamp-3">{title}</div>
      </div>
      <Authors authors={authors} />

      <div className="mt-2 flex items-center gap-2 text-xs font-normal text-primary-600">
        <span className="flex-1 truncate font-suisse text-[11px] font-medium uppercase">
          {tags.map((tag, idx) => (
            <span className="pr-2" key={idx}>
              {tag}
            </span>
          ))}
        </span>
      </div>
    </Link>
  );
}

function Authors({ authors }: { authors: Author[] }) {
  return (
    <div className={cn("flex items-center font-normal")}>
      <AvatarGroup avatars={authors.map((a) => ({ imgUrl: a.avatar }))} />
      <div className="ml-1 text-xs text-primary-800">
        {authors.map((author, idx) => (
          <React.Fragment key={idx}>
            <span>{author.name}</span>
            {idx < authors.length - 1 && ", "}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
