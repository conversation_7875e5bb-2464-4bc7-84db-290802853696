"use client";

import { useId, useMemo, useState } from "react";
import {
  Alert<PERSON>riangle,
  ArrowUpRight,
  DownloadIcon,
  Maximize2Icon,
  Paperclip,
  PauseIcon,
  PlayIcon,
  XIcon,
} from "lucide-react";
import { Button } from "#/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import { AttachmentViewer } from "#/ui/attachment/attachment-viewer";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHotkeysContext } from "react-hotkeys-hook";
import { cn } from "#/utils/classnames";
import { AttachmentIcon } from "./attachment-icon";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { pluralizeWithCount } from "#/utils/plurals";
import { useAttachmentBrowser } from "#/ui/query-parameters";
import {
  extractAttachmentReferences,
  type ExtractedAttachment,
  useExtractedAttachment,
  getAttachmentSource,
} from "./use-extracted-attachment";
import { BasicTooltip } from "#/ui/tooltip";

export function AttachmentListFromData({
  data,
  layout,
  localStorageKey,
  className,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Log data can have any shape.
  data: any;
  layout?: "dense";
  localStorageKey?: string;
  className?: string;
}) {
  const [references, keyPaths] = useMemo(
    () => extractAttachmentReferences(data),
    [data],
  );
  return (
    <AttachmentList
      references={references}
      keyPaths={keyPaths}
      localStorageKey={localStorageKey}
      layout={layout}
      className={className}
    />
  );
}

export function AttachmentList({
  references,
  keyPaths,
  localStorageKey,
  layout,
  className,
}: {
  references: ExtractedAttachment[];
  keyPaths?: string[];
  localStorageKey?: string;
  layout?: "dense";
  className?: string;
}) {
  const [isAttachmentBrowserOpen, setAttachmentBrowserOpen] =
    useAttachmentBrowser();

  if (references.length === 0) {
    return null;
  }

  const contents = (
    <>
      {references.map((item, index) => (
        <AttachmentItem
          mode="preview"
          layout={layout}
          extracted={item}
          // `reference.key/url/src` is not unique when there are duplicate attachments.
          key={`${getAttachmentSource(item)}|${keyPaths?.[index]}`}
        />
      ))}
    </>
  );

  if (layout === "dense") {
    return (
      <div className={cn("grid grid-cols-2 gap-1", className)}>{contents}</div>
    );
  }

  return (
    <div className={cn("mt-2", className)}>
      <CollapsibleSection
        className="inline-flex flex-none font-normal"
        collapsedClassName="p-0 mb-2 text-primary-500 hover:text-primary-700 bg-transparent hover:p-0 hover:bg-transparent"
        expandedClassName="p-0 text-primary-500 hover:text-primary-700 hover:p-0 hover:bg-transparent"
        localStorageKey={localStorageKey}
        title={
          <span className="flex items-center gap-1 text-left">
            <Paperclip className="size-3 flex-none" />
            {pluralizeWithCount(references.length, "attachment", "attachments")}
          </span>
        }
        actionRight={
          <BasicTooltip tooltipContent="Open attachments browser">
            <Button
              variant="ghost"
              className={cn("hidden text-primary-400", {
                "@4xl:flex": !isAttachmentBrowserOpen,
              })}
              size="xs"
              onClick={() => setAttachmentBrowserOpen(true)}
              Icon={ArrowUpRight}
            />
          </BasicTooltip>
        }
      >
        <div className="mb-2 flex flex-col gap-1">{contents}</div>
      </CollapsibleSection>
    </div>
  );
}

export const AttachmentItem = ({
  mode,
  layout,
  extracted,
}: {
  mode: "preview" | "full";
  layout?: "dense";
  extracted: ExtractedAttachment;
}) => {
  const { enableScope, disableScope } = useHotkeysContext();
  const [audioPlaying, setAudioPlaying] = useState(false);

  const hiddenAudioPlayerId = useId();

  const config = useExtractedAttachment({ extracted });

  if (!config) return null;

  const {
    isLoaded,
    enableViewer,
    filename,
    isPdf,
    contentType,
    status,
    embedUrl,
    isImage,
    enableThumbnail,
    isAudio,
    downloadUrl,
    isVisual,
  } = config;

  const fullAttachment = (
    <div className="flex flex-1 items-center justify-center">
      {isLoaded && enableViewer ? (
        <AttachmentViewer
          className={cn("max-w-full max-h-full", { "size-full": isPdf })}
          filename={filename}
          contentType={contentType}
          status={status}
          src={embedUrl}
        />
      ) : status?.error_message ? (
        <p
          className="whitespace-pre-wrap text-center text-xs text-bad-700"
          style={{ wordBreak: "break-word" }}
        >
          {status.error_message}
        </p>
      ) : (
        <p className="text-center text-xs text-primary-500">
          File is too large to preview
        </p>
      )}
    </div>
  );

  if (mode === "full") return fullAttachment;

  const showPreview = isImage && enableThumbnail && isLoaded;

  return (
    <Dialog
      onOpenChange={(open) => {
        if (open) {
          enableScope(HotkeyScope.ExpandedFrame);
          // Make audio playback mutually exclusive.
          const audio = document.getElementById(hiddenAudioPlayerId);
          if (audio instanceof HTMLAudioElement) {
            audio.pause();
          }
        } else {
          disableScope(HotkeyScope.ExpandedFrame);
        }
      }}
    >
      <div className="flex w-full max-w-md gap-1">
        <DialogTrigger asChild>
          <Button
            size="xs"
            className={cn(
              "group min-h-7 flex-1 flex-col items-stretch gap-0 overflow-hidden p-0 text-left font-normal",
              {
                "h-auto": showPreview || layout !== "dense",
                "h-[140px]": showPreview && layout === "dense",
              },
            )}
            isLoading={!isLoaded && status?.upload_status !== "error"}
            disabled={!isLoaded}
            onClick={(e) => {
              e.stopPropagation();
              // Check for ctrl/cmd/shift click.
              if ((e.metaKey || e.ctrlKey || e.shiftKey) && embedUrl) {
                e.preventDefault();
                window.open(embedUrl, "_blank");
              }
            }}
          >
            <div className="flex h-7 flex-none items-center gap-1.5 truncate px-2">
              {status?.error_message ? (
                <AlertTriangle className="size-3 flex-none text-bad-700" />
              ) : (
                <AttachmentIcon
                  contentType={contentType}
                  className="size-3 flex-none text-primary-500"
                />
              )}
              <span className="flex-1 truncate">{filename}</span>
              <Maximize2Icon className="size-3 flex-none text-primary-500 opacity-0 transition-opacity group-hover:opacity-100" />
            </div>
            {showPreview && (
              <div
                className={cn(
                  "aspect-video h-48 flex-none border-t bg-contain bg-center bg-no-repeat bg-primary-50 border-primary-100",
                  {
                    "h-auto flex-1 bg-cover aspect-auto": layout === "dense",
                  },
                )}
                style={{ backgroundImage: `url("${embedUrl}")` }}
              />
            )}
          </Button>
        </DialogTrigger>
        {isAudio && isLoaded && (
          <>
            <Button
              size="xs"
              Icon={audioPlaying ? PauseIcon : PlayIcon}
              disabled={!isLoaded}
              onClick={(event) => {
                event.preventDefault();
                const audio = document.getElementById(hiddenAudioPlayerId);
                if (!(audio instanceof HTMLAudioElement)) {
                  return;
                }
                if (audio.paused) {
                  audio.play();
                } else {
                  audio.pause();
                }
              }}
            />
            <div className="hidden">
              <AttachmentViewer
                id={hiddenAudioPlayerId}
                filename={filename}
                contentType={contentType}
                status={status}
                src={embedUrl}
                setAudioPlaying={setAudioPlaying}
              />
            </div>
          </>
        )}
      </div>
      <DialogContent
        hideCloseButton
        onEscapeKeyDown={(e) => e.stopPropagation()}
        className={cn("flex flex-col", {
          "sm:max-w-screen-xl": isVisual,
          "sm:h-screen": isPdf,
        })}
      >
        <DialogTitle className="flex flex-none items-start gap-2 overflow-hidden">
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <div className="min-w-0 flex-1">
                <div className="truncate text-sm font-medium">{filename}</div>
                <div className="truncate text-xs font-normal text-primary-500">
                  {contentType}
                </div>
              </div>
              <div className="flex flex-none items-start gap-1">
                <Button
                  size="xs"
                  className="flex-none"
                  Icon={DownloadIcon}
                  disabled={!isLoaded}
                  onClick={() =>
                    downloadUrl && window.location.assign(downloadUrl)
                  }
                >
                  Download
                </Button>
                <DialogClose asChild>
                  <Button size="xs" className="flex-none" Icon={XIcon} />
                </DialogClose>
              </div>
            </div>
          </div>
        </DialogTitle>
        {fullAttachment}
      </DialogContent>
    </Dialog>
  );
};
