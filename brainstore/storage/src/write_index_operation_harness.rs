use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::{<PERSON>, Mutex},
};

use async_util::test_util::TwoWaySyncPointSendAndWait;
use futures::future::BoxFuture;
use lazy_static::lazy_static;
use otel_common::opentelemetry::{
    metrics::{Gauge, Histogram},
    KeyValue,
};
use tracing::Instrument;
use util::system_types::ObjectType;
use util::{anyhow::Result, uuid::Uuid};

use crate::{
    clear_compacted_index::{clear_compacted_index_inner, ClearCompactedIndexInnerInput},
    config_with_store::StoreInfo,
    global_locks_manager::GlobalLocksManager,
    global_store::{
        GlobalStore, LastCompactedIndexMeta, LastIndexOperation, LastIndexOperationDetails,
    },
    limits::global_limits,
    status_updater::{StatusUpdate, StatusUpdater},
    tantivy_index::{
        extract_opstamp, flash_tantivy_index, validate_tantivy_index, IndexMetaJson,
        TantivyIndexScope, ValidateTantivyIndexOptions,
    },
};

struct IndexingOperationMetrics {
    pub active_operations: Gauge<u64>,
    pub operation_duration_ms: Histogram<u64>,
    // key: (index_type, object_type) -> current active count
    pub active_counts_by_type: Mutex<HashMap<(IndexOperationType, ObjectType), i64>>,
}

impl Default for IndexingOperationMetrics {
    fn default() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            active_operations: meter
                .u64_gauge("brainstore.storage.active_indexing_operations")
                .with_description("Number of indexing operations currently running (with permit and lock acquired)")
                .build(),
            operation_duration_ms: meter
                .u64_histogram("brainstore.storage.indexing_operation_duration_ms")
                .with_unit("ms")
                .with_description("Duration of indexing operations that have acquired permit and lock")
                .build(),
            active_counts_by_type: Mutex::new(HashMap::new()),
        }
    }
}

impl IndexingOperationMetrics {
    fn record_active_operations(&self) {
        let map = self.active_counts_by_type.lock().expect("poisoned mutex");
        for ((index_type, object_type), count) in map.iter() {
            self.active_operations.record(
                (*count).max(0) as u64,
                &[
                    KeyValue::new("op_type", index_type.as_str()),
                    KeyValue::new("object_type", object_type.to_string()),
                ],
            );
        }
    }

    fn increment_active(&self, operation_type: &IndexOperationType, object_type: &ObjectType) {
        let key = (operation_type.clone(), *object_type);
        {
            let mut map = self.active_counts_by_type.lock().expect("poisoned mutex");
            let entry = map.entry(key).or_insert(0);
            *entry += 1;
        }
        self.record_active_operations();
    }

    fn decrement_active(&self, operation_type: &IndexOperationType, object_type: &ObjectType) {
        let key = (operation_type.clone(), *object_type);
        {
            let mut map = self.active_counts_by_type.lock().expect("poisoned mutex");
            if let Some(entry) = map.get_mut(&key) {
                *entry -= 1;
            }
        }
        self.record_active_operations();
    }
}

lazy_static! {
    static ref INDEXING_OPERATION_METRICS: IndexingOperationMetrics =
        IndexingOperationMetrics::default();
}

struct ActiveOperationGuard {
    operation_type: IndexOperationType,
    object_type: Option<ObjectType>,
}

impl Drop for ActiveOperationGuard {
    fn drop(&mut self) {
        if let Some(obj_type) = &self.object_type {
            INDEXING_OPERATION_METRICS.decrement_active(&self.operation_type, obj_type);
        }
    }
}

#[derive(Clone, Debug)]
pub struct WriteIndexOperationHarnessInput {
    pub segment_id: Uuid,
    pub index_store: StoreInfo,
    pub schema: util::schema::Schema,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: Arc<dyn GlobalLocksManager>,
    pub validate_opts: ValidateTantivyIndexOptions,
    pub index_type: IndexOperationType,
}

#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub enum IndexOperationType {
    Compact,
    Merge,
    Delete,
}

impl IndexOperationType {
    pub fn as_str(&self) -> &'static str {
        match self {
            IndexOperationType::Compact => "compact",
            IndexOperationType::Merge => "merge",
            IndexOperationType::Delete => "delete",
        }
    }
}

#[derive(Default)]
pub struct WriteIndexOperationHarnessOptionalInput {
    pub use_status_updater: bool,
    pub skip_permit: bool,
    pub try_acquire: bool,
    pub testing_override_missing_del_file_retries: Option<usize>,
    pub sleep_after_acquire_lock_ms: Option<usize>,
    pub testing_sync_points: WriteIndexOperationHarnessTestingSyncPoints,
}

#[derive(Default)]
pub struct WriteIndexOperationHarnessTestingSyncPoints {
    pub after_acquire_index_lock: Option<TwoWaySyncPointSendAndWait>,
}

#[derive(Debug)]
pub struct WriteIndexOperationHarnessTaskAdditionalInput<'a> {
    pub global_store_last_compacted_index_meta: Option<LastCompactedIndexMeta>,
    pub status_updater: &'a Option<StatusUpdater>,
}

// A harness for operations which write to a segment's tantivy index. The harness handles the
// following boilerplate:
//
//   - Manage a status updater for publishing live updates.
//
//   - Acquire resource permit and distributed lock for the segment index.
//
//   - Retry certain failure scenarios, generally due to unavoidable index corruption (see comments
//   below). Note that in some cases we clear the index in order to start from scratch.
pub async fn write_index_operation_harness<F, TaskInput, TaskOptions, TaskOutput>(
    harness_input: WriteIndexOperationHarnessInput,
    harness_optional_input: WriteIndexOperationHarnessOptionalInput,
    task_factory: F,
    task_input: TaskInput,
    task_options: TaskOptions,
    task_noop_output: TaskOutput,
) -> Result<TaskOutput>
where
    TaskInput: Clone,
    TaskOptions: Clone,
    F: for<'a> FnMut(
        TaskInput,
        TaskOptions,
        WriteIndexOperationHarnessTaskAdditionalInput<'a>,
    ) -> BoxFuture<'a, Result<TaskOutput>>,
{
    let status_updater = if harness_optional_input.use_status_updater {
        Some(StatusUpdater::new(
            harness_input.global_store.clone(),
            harness_input.segment_id,
        ))
    } else {
        None
    };

    let output = write_index_operation_harness_inner(
        harness_input,
        harness_optional_input,
        task_factory,
        task_input,
        task_options,
        task_noop_output,
        &status_updater,
    )
    .await;
    if let Err(e) = &output {
        status_updater.update(LastIndexOperation {
            error: Some(e.to_string()),
            ..Default::default()
        });
    }
    status_updater.finish().await?;
    output
}

async fn write_index_operation_harness_inner<'a, F, TaskInput, TaskOptions, TaskOutput>(
    harness_input: WriteIndexOperationHarnessInput,
    harness_optional_input: WriteIndexOperationHarnessOptionalInput,
    mut task_factory: F,
    task_input: TaskInput,
    task_options: TaskOptions,
    task_noop_output: TaskOutput,
    status_updater: &'a Option<StatusUpdater>,
) -> Result<TaskOutput>
where
    TaskInput: Clone,
    TaskOptions: Clone,
    F: for<'b> FnMut(
        TaskInput,
        TaskOptions,
        WriteIndexOperationHarnessTaskAdditionalInput<'b>,
    ) -> BoxFuture<'b, Result<TaskOutput>>,
{
    if !harness_optional_input.skip_permit {
        status_updater.update(LastIndexOperation {
            stage: Some("acquiring permit".to_string()),
            details: Some(LastIndexOperationDetails::Compact { num_wal_entries: 0 }),
            ..Default::default()
        });
    }

    let _permit = if harness_optional_input.skip_permit {
        None
    } else {
        Some(
            global_limits()
                .index_operations
                .start_task()
                .instrument(tracing::info_span!("acquiring permit",))
                .await?,
        )
    };

    let index_scope = TantivyIndexScope::Segment(harness_input.segment_id);

    status_updater.update(LastIndexOperation {
        stage: Some("acquiring lock".to_string()),
        ..Default::default()
    });
    let _lock = if harness_optional_input.try_acquire {
        match harness_input
            .locks_manager
            .try_write(&index_scope.lock_name())
            .await?
        {
            Some(lock) => lock,
            None => {
                tracing::info!(
                    "Could not acquire lock for segment {}",
                    harness_input.segment_id
                );
                status_updater.update(LastIndexOperation {
                    stage: Some("skipping (lock not available)".to_string()),
                    finished: Some(true),
                    estimated_progress: Some(0.0),
                    ..Default::default()
                });

                return Ok(task_noop_output);
            }
        }
    } else {
        harness_input
            .locks_manager
            .write(&index_scope.lock_name())
            .await?
    };
    let _status_updater_guard = status_updater.ensure_next_update();
    status_updater.update(LastIndexOperation {
        stage: Some("acquired lock".to_string()),
        ..Default::default()
    });

    // Record active operation and start timing - operation now has permit and lock
    let index_type_str = harness_input.index_type.as_str();
    let object_type_label = harness_input.schema.name().clone();
    let object_type_enum = std::str::FromStr::from_str(&object_type_label).ok();
    if object_type_enum.is_none() {
        log::warn!(
            "Unrecognized object type label '{}' in schema; skipping active gauge update",
            object_type_label
        );
    }
    let operation_start = std::time::Instant::now();
    // Helper to construct attributes for duration metric with an error flag.
    // Omit object_type if the schema name is unrecognized.
    let mut attrs_ok = Vec::with_capacity(3);
    attrs_ok.push(KeyValue::new("op_type", index_type_str));
    attrs_ok.push(KeyValue::new("error", false));
    if object_type_enum.is_some() {
        attrs_ok.push(KeyValue::new("object_type", object_type_label.clone()));
    }
    let mut attrs_err = Vec::with_capacity(3);
    attrs_err.push(KeyValue::new("op_type", index_type_str));
    attrs_err.push(KeyValue::new("error", true));
    if object_type_enum.is_some() {
        attrs_err.push(KeyValue::new("object_type", object_type_label.clone()));
    }

    if let Some(obj) = &object_type_enum {
        INDEXING_OPERATION_METRICS.increment_active(&harness_input.index_type, obj);
    }

    // Ensure we decrement the counter when the operation completes or fails
    let _active_operation_guard = ActiveOperationGuard {
        operation_type: harness_input.index_type.clone(),
        object_type: object_type_enum,
    };

    if let Some(duration_ms) = harness_optional_input.sleep_after_acquire_lock_ms {
        tokio::time::sleep(tokio::time::Duration::from_millis(duration_ms as u64)).await;
    }
    if let Some(sync_point) = harness_optional_input
        .testing_sync_points
        .after_acquire_index_lock
    {
        sync_point.send_and_wait().await;
    }

    // In some cases index ops will fail for reasons that warrant a retry:
    //
    // - There are conflicting files lying around on the filesystem which interfere with the
    // compaction process. One specific example we handle is when there is a
    // [segment_id].[opstamp].del file, where the [opstamp] is greater than our stored meta.json.
    // So when we try to run compaction, we may end up re-creating that `.del` file and failing
    // because it already exists. In this case, our solution is to retry, deleting that file before
    // proceeding with the compaction.
    //
    //      - In some cases, we observe this error cropping up even when there are no prior `.del`
    //      files in the index. Which indicates some sort of yet-undiagnosed index corruption or
    //      tantivy bug. So as a fallback, we'll clear the index if we can't get past the error
    //      after the number of retries, and try once more.
    //
    // - The index has been corrupted for other reasons. E.g. we occasionally see `.del` files
    // mentioned in the index metadata, but they no longer exist on the filesystem. In this case,
    // we also recompact and retry.
    let mut wipe_opstamp_file: Option<PathBuf> = None;
    let missing_del_file_max_retries = harness_optional_input
        .testing_override_missing_del_file_retries
        .unwrap_or(2);
    let mut num_missing_del_file_errors = 0;
    let mut tried_recompact_and_retry = false;
    loop {
        // Before any index write operation, we flash the tantivy metadata on the filesystem to the
        // last compacted index metadata. This way, if we happened to try a compaction run which
        // failed partway through, or the metadata changed for any other reason, we can restore the
        // index state to a known valid point, and avoid considering any stray segments from the
        // failed compaction.

        let global_store_last_compacted_index_meta = {
            let segment_id_arr = [harness_input.segment_id];
            let mut segment_metadatas = harness_input
                .global_store
                .query_segment_metadatas(&segment_id_arr)
                .await?;
            segment_metadatas.remove(0).last_compacted_index_meta
        };

        status_updater.update(LastIndexOperation {
            stage: Some("flashing index".to_string()),
            ..Default::default()
        });
        flash_tantivy_index(
            global_store_last_compacted_index_meta
                .as_ref()
                .map(|x| &x.tantivy_meta),
            &harness_input.schema,
            &harness_input.index_store,
            &index_scope,
            wipe_opstamp_file.take().as_deref(),
        )
        .await?;

        let output = task_factory(
            task_input.clone(),
            task_options.clone(),
            WriteIndexOperationHarnessTaskAdditionalInput {
                global_store_last_compacted_index_meta: global_store_last_compacted_index_meta
                    .clone(),
                status_updater,
            },
        )
        .await;

        let output_err = match output {
            Ok(x) => {
                // Record successful operation duration
                let duration_ms = operation_start.elapsed().as_millis() as u64;
                INDEXING_OPERATION_METRICS
                    .operation_duration_ms
                    .record(duration_ms, &attrs_ok);
                return Ok(x);
            }
            Err(e) => e,
        };

        // If we haven't exceeded the maximum number of deletion file errors, retry with the
        // wipe_opstamp_file set to the stray `.del` file.
        if num_missing_del_file_errors < missing_del_file_max_retries {
            if let Some(path) = extract_file_already_exists_error_path(&output_err) {
                log::warn!(
                      "Encountered existing file {:?}. Retrying with deletion of existing opstamp file",
                      path,
                );
                wipe_opstamp_file = Some(path);
                num_missing_del_file_errors += 1;
                continue;
            }
        }

        // If we haven't yet recompacted-and-retried, and it's a valid error to recompact, try
        // that.
        if !tried_recompact_and_retry {
            let is_file_already_exists_error =
                if let Some(path) = extract_file_already_exists_error_path(&output_err) {
                    log::warn!(
                        "Encountered existing file {:?}. Recompacting and retrying.",
                        path
                    );
                    true
                } else {
                    false
                };
            if is_file_already_exists_error
                || should_recompact_invalid_segment(
                    harness_input.segment_id,
                    &harness_input.index_store,
                    global_store_last_compacted_index_meta
                        .as_ref()
                        .map(|x| &x.tantivy_meta)
                        .cloned(),
                    &harness_input.validate_opts,
                )
                .await?
            {
                tried_recompact_and_retry = true;
                clear_compacted_index_inner(ClearCompactedIndexInnerInput {
                    segment_id: harness_input.segment_id,
                    global_store: harness_input.global_store.clone(),
                    current_last_compacted_index_meta: global_store_last_compacted_index_meta
                        .clone(),
                })
                .await?;
                continue;
            }
        }

        let duration_ms = operation_start.elapsed().as_millis() as u64;
        INDEXING_OPERATION_METRICS
            .operation_duration_ms
            .record(duration_ms, &attrs_err);
        return Err(output_err);
    }
}

fn extract_file_already_exists_error_path(e: &util::anyhow::Error) -> Option<PathBuf> {
    if let Some(tantivy_error) = e.downcast_ref::<tantivy::TantivyError>() {
        if let tantivy::TantivyError::OpenWriteError(open_write_error) = tantivy_error {
            if let tantivy::directory::error::OpenWriteError::FileAlreadyExists(path) =
                open_write_error
            {
                if let Some(file_name) = path.file_name() {
                    if extract_opstamp(Path::new(file_name)).is_some() {
                        return Some(path.clone());
                    }
                }
            }
        }
    }
    None
}

async fn should_recompact_invalid_segment(
    segment_id: Uuid,
    index: &StoreInfo,
    last_compacted_tantivy_meta: Option<IndexMetaJson>,
    validate_opts: &ValidateTantivyIndexOptions,
) -> Result<bool> {
    let tantivy_meta = match last_compacted_tantivy_meta {
        Some(meta) => meta,
        None => {
            log::debug!("No last_compacted_tantivy_meta found for segment {}. Validation vacuously succeeded.", segment_id);
            return Ok(false);
        }
    };
    let validation_result = validate_tantivy_index(
        tantivy_meta,
        &index.store,
        &index.prefix,
        &TantivyIndexScope::Segment(segment_id),
        validate_opts,
    )
    .await?;
    match validation_result.check_success() {
        Ok(validated_metadata) => {
            log::debug!(
                "Index validation succeeded for segment {}. last_compacted_index_meta: {:?}",
                segment_id,
                serde_json::to_string(&validated_metadata)?
            );
            Ok(false)
        }
        Err(e) => {
            log::warn!(
                "Index validation failed for segment {}. {}. Recompacting and retrying.",
                segment_id,
                e
            );
            Ok(true)
        }
    }
}
